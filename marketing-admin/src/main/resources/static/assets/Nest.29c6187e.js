import { _ as _export_sfc } from './index.8a10f1d7.js';
import { p as resolveComponent, l as openBlock, J as createElementBlock, j as createVNode, F as Fragment, K as createBaseVNode } from './element-plus.2a1d1bd8.js';

const _sfc_main = {};

const _hoisted_1 = /*#__PURE__*/createBaseVNode("h1", null, "二级菜单", -1);
const _hoisted_2 = /*#__PURE__*/createBaseVNode("h3", null, "二级菜单的页面是不会缓存的", -1);

function _sfc_render(_ctx, _cache) {
  const _component_router_view = resolveComponent("router-view");

  return (openBlock(), createElementBlock(Fragment, null, [
    _hoisted_1,
    _hoisted_2,
    createVNode(_component_router_view)
  ], 64))
}
var Nest = /*#__PURE__*/_export_sfc(_sfc_main, [['render',_sfc_render]]);

export { Nest as default };
