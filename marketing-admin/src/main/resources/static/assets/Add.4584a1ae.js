import { _ as _export_sfc } from './index.8a10f1d7.js';
import { l as openBlock, J as createElementBlock, F as Fragment, K as createBaseVNode, R as createTextVNode } from './element-plus.2a1d1bd8.js';

const _sfc_main = {};

const _hoisted_1 = /*#__PURE__*/createBaseVNode("h2", null, "该页面入口不在菜单中显示", -1);
const _hoisted_2 = /*#__PURE__*/createBaseVNode("div", null, [
  /*#__PURE__*/createTextVNode(" 如果不需要在菜单中显示： "),
  /*#__PURE__*/createBaseVNode("br"),
  /*#__PURE__*/createTextVNode(" 需要配置路由增加属性hidden: true，注意不是在meta中增加该属性，而是跟meta同级 ")
], -1);

function _sfc_render(_ctx, _cache) {
  return (openBlock(), createElementBlock(Fragment, null, [
    _hoisted_1,
    _hoisted_2
  ], 64))
}
var Add = /*#__PURE__*/_export_sfc(_sfc_main, [['render',_sfc_render]]);

export { Add as default };
