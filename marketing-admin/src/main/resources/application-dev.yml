
server:
  port: 8082

#spring
spring:
  main:
    lazy-initialization: false
  kafka:
    bootstrap-servers: kafka.ops.17usoft.com:9092 # kafka集群信息，多个用逗号间隔
    # 生产者
    producer:
      # 重试次数，设置大于0的值，则客户端会将发送失败的记录重新发送
      retries: 1
      batch-size: 65536 #批量处理大小，64k
      buffer-memory: 67108864 #缓冲存储大，64M
      acks: 1
      # 指定消息key和消息体的编解码方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      compression-type: snappy
    # 消费者
    consumer:
      # 消费者组
      group-id: tcla_consumer_group_localactivity_marketing_default
      # 是否自动提交
      enable-auto-commit: true
      # 消费偏移配置
      # none：如果没有为消费者找到先前的offset的值,即没有自动维护偏移量,也没有手动维护偏移量,则抛出异常
      # earliest：在各分区下有提交的offset时：从offset处开始消费；在各分区下无提交的offset时：从头开始消费
      # latest：在各分区下有提交的offset时：从offset处开始消费；在各分区下无提交的offset时：从最新的数据开始消费
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 单次拉取消息的最大条数
      max-poll-records: 200
    # 监听
    listener:
      # record：当每一条记录被消费者监听器（ListenerConsumer）处理之后提交
      # batch：当每一批poll()的数据被ListenerConsumer处理之后提交
      # time：当每一批poll()的数据被ListenerConsumer处理之后，距离上次提交时间大于TIME时提交
      # count：当每一批poll()的数据被ListenerConsumer处理之后，被处理record数量大于等于COUNT时提交
      # count_time：TIME或COUNT中有一个条件满足时提交
      # manual：当每一批poll()的数据被ListenerConsumer处理之后, 手动调用Acknowledgment.acknowledge()后提交
      # manual_immediate：手动调用Acknowledgment.acknowledge()后立即提交，一般推荐使用这种
      ack-mode: manual_immediate
      type: batch
      missing-topics-fatal: false

# 日志配置
logging:
  level:
    root: info
    org.springframework.boot.autoconfigure: error
    com.ly.localactivity.marketing.mapper.marketing: debug
    com.ly.localactivity.framework: debug

  file:
    path: datalogs

local-activity:
  apiDoc:
    enabled: true
  testApi:
    enabled: true
  config:
    turbomq:
      enabled: true
      namesrvAddr: mqnameserver.qa.17usoft.com:9876
      producerGroups:
        - tcla_consumer_group_lamarketing_admin_backend
    redis:
      enabled: true
      groupName: redis.group.tcscenery.localactivity.marketing
      prefix: tclami
      env: test


