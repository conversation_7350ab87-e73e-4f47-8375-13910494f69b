//package com.ly.localactivity.marketing.kafka;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
///**
// * KafkaConsumer
// *
// * <AUTHOR>
// * @date 2024/1/30
// */
//@Slf4j
//@Component
//public class KafkaConsumer {
//
//    @KafkaListener(topics = {"sb_topic"})
//    public void onNormalMessage(ConsumerRecord<String, Object> record) {
//
//        System.out.println("消费kafka：" + record.topic() + "-" + record.partition() + "=" +
//                record.value());
//    }
//}
