package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.marketing.common.bean.SelectItemVo;
import com.ly.localactivity.marketing.common.constants.EnvConst;
import com.ly.localactivity.marketing.common.service.DictService;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.ro.RedisSetRo;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchListService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * CommonController
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@RestController
@RequestMapping("/api/common")
public class CommonController {
    @Resource
    private DictService dictService;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private ICouponBatchListService couponBatchListService;

    /**
     * 获取dic项目
     *
     * @param type 类型
     * @return {@link CommonResult}<{@link List}<{@link SelectItemVo}>>
     */
    @GetMapping("/getDicItems")
    @ApiOperation("获取字典项目，下拉选项使用")
    public CommonResult<List<SelectItemVo>> getDicItems(@RequestParam("type") String type) {
        return CommonResult.success(dictService.getDicSelectItems(type));
    }

    @PostMapping("/postTest")
    @ApiOperation("post")
    public CommonResult post(){
        return CommonResult.success(adminTokenUtils.getModifier());
    }

    /**
     * 获取通用配置
     */
    @ApiOperation("获取通用配置")
    @GetMapping("/getCommonConfig")
    public CommonResult<Object> getCommonConfig(@RequestParam("type") String type) {
        return CommonResult.success(dictService.getCommonConfig(type));
    }

    @ApiOperation(value = "删除缓存")
    @PostMapping("/redis/delete")
    public CommonResult deletRedis(@RequestParam("key") String key) {
        if (StringUtils.isBlank(key)) {
            return CommonResult.failed("key不能为空");
        }
        redisUtils.del(key);
        return CommonResult.success();
    }

    @ApiOperation(value = "获取缓存")
    @PostMapping("/redis/get")
    public String getRedis(@RequestParam("key") String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisUtils.get(key);
    }

    @ApiOperation(value = "设置缓存")
    @PostMapping("/redis/set")
    public CommonResult setRedis(@RequestBody @Valid RedisSetRo redisSetRo){
        int expireTime = redisSetRo.getExpireTime() == null ? 60 * 30 : redisSetRo.getExpireTime();
        redisUtils.setex(redisSetRo.getKey(), redisSetRo.getData(), expireTime);
        return CommonResult.success();
    }

    @ApiOperation(value = "刷新红包批次")
    @PostMapping("/coupon/refreshBatch")
    public CommonResult refreshCouponBatch(@RequestParam("batchNo") String batchNo, @RequestParam("env") String env) {
        if (StringUtils.isNotBlank(env) && (env.equals(EnvConst.ENV_STAGE) || env.equals(EnvConst.ENV_PRODUCT))) {
            couponQueryService.refreshCache(batchNo, env);
        } else {
            couponQueryService.refreshCache(batchNo);
        }
        return CommonResult.success();
    }

    @ApiOperation(value = "控制红包状态")
    @PostMapping("/coupon/controlBatchList")
    public CommonResult controlBatchList(@RequestParam("couponBatchId") String couponBatchId, @RequestParam("status") Integer status) {
        if (couponBatchId != null && status != null) {
            couponBatchListService.controlBatchList(couponBatchId, status);
        }
        return CommonResult.success();
    }

}
