package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplatePageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplatePageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplateParamsVo;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionLinkTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPromotionLinkTemPlateController
 *
 * <AUTHOR>
 * @date 2024-2-6
 */
@RestController
@RequestMapping("/api/marketing/operationPromotionLinkTemPlate")
@Api(value = "链接模板相关接口", tags = {"链接模板相关接口"})
public class OperationPromotionLinkTemPlateController {
    @Resource
    private IOperationPromotionLinkTemplateService operationPromotionLinkTemplateService;

    /**
     * 查询链接模板列表
     * @return {@link CommonResult}<{@link List}<{@link OperationPositionPageVo}>>
     */
    @ApiOperation("查询链接模板列表")
    @PostMapping(value = "/list")
    public CommonResult<OperationPromotionLinkTemplatePageVo> listOperationPromotionLinkTemPlate(@RequestBody @Valid OperationPromotionLinkTemplatePageRo request) {
        return CommonResult.success(operationPromotionLinkTemplateService.listOperationPromotionLinkTemplate(request));
    }

    /**
     * 根据 id 查询链接模板详情
     * @param id
     * @return {@link CommonResult}
     */
    @ApiOperation("根据 id 查询链接模板")
    @GetMapping("/get")
    public CommonResult<OperationPromotionLinkTemplateParamsVo> getOperationPromotionLinkTemplate(@Valid @RequestParam(value = "id") @NotNull(message = "链接模板id不能为空") Long id) {
        return CommonResult.success(operationPromotionLinkTemplateService.getOperationPromotionLinkTemplateById(id));
    }

    /**
     * 新增链接模板
     * @param request
     * @return {@link CommonResult}
     */
    @ApiOperation("新增链接模板")
    @PostMapping("/save")
    public CommonResult<Boolean> saveOperationPromotionLinkTemplate(@RequestBody @Valid OperationPromotionLinkTemplateRo request) {
        return CommonResult.success(operationPromotionLinkTemplateService.saveOrUpdateOperationPromotionLinkTemplate(request));
    }

    /**
     * 编辑链接模板
     * @param request
     * @return {@link CommonResult}
     */
    @ApiOperation("编辑链接模板")
    @PostMapping("/update")
    public CommonResult<Boolean> updateOperationPromotionLinkTemplate(@RequestBody @Valid OperationPromotionLinkTemplateRo request) {
        return CommonResult.success(operationPromotionLinkTemplateService.saveOrUpdateOperationPromotionLinkTemplate(request));
    }

    /**
     * 设置链接模板状态(有效|无效)
     * @param request
     * @return {@link CommonResult}
     */
    @ApiOperation("设置链接模板状态")
    @PostMapping("/updateStatus")
    public CommonResult<Boolean> updateOperationPromotionLinkTemplateStatus(@RequestBody @Valid OperationPromotionLinkTemplateStatusRo request) {
        return CommonResult.success(operationPromotionLinkTemplateService.updateOperationPromotionLinkTemplateStatus(request));
    }
}
