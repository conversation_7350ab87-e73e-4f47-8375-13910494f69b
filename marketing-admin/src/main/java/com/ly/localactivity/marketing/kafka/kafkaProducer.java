//package com.ly.localactivity.marketing.kafka;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * kafkaProducer
// *
// * <AUTHOR>
// * @date 2024/1/30
// */
//@Component
//public class kafkaProducer {
//    @Resource
//    private KafkaTemplate<String, Object> kafkaTemplate;
//
//}
