package com.ly.localactivity.marketing.common.security;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.common.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * ReqFilter
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Slf4j
@Order(1)
@WebFilter(
        filterName = "reqFilter",
        urlPatterns = {"/*"}
)
@Component
public class ReqFilter extends OncePerRequestFilter {

    private final Set<String> ALLOWED_PATHS = new HashSet<>();

    @PostConstruct
    public void init() {
        ALLOWED_PATHS.addAll(Arrays.asList("/health", "/job/**"));

        //非生产环境放行其它接口
        String activeProfile = SpringUtil.getActiveProfile();
        if (StringUtils.isNotBlank(activeProfile) && !activeProfile.equals("product")) {
            ALLOWED_PATHS.addAll(Arrays.asList("/doc.html", "/swagger-resources/**"
                    , "/v2/api-docs", "/webjars/**", "/api/test/**"));
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        // 排除放行url
        String path = request.getRequestURI().substring(request.getContextPath().length()).replaceAll("[/]+$", "");

        if (matches(path, ALLOWED_PATHS)) {
            chain.doFilter(request, response);
            return;
        }

        //header token校验
//        String token = request.getHeader(AdminConst.Admin_Token_Header);
//        String apiToken = ConfigUtils.get(ConfigConst.Admin_API_Token);
//        if (StringUtils.isBlank(token) || !token.equals(apiToken)) {
//            writeResponse(response, CommonResult.failed("token校验失败"));
//            return;
//        }

        chain.doFilter(request, response);
    }

    /**
     * 判断指定url地址是否匹配指定url集合中的任意一个
     *
     * @param urlPath 指定url地址
     * @param urls    需要检查的url集合
     * @return 是否匹配  匹配返回true，不匹配返回false
     */
    public static boolean matches(String urlPath, Set<String> urls) {
        if (StringUtils.isEmpty(urlPath) || CollectionUtil.isEmpty(urls)) {
            return false;
        }
        for (String url : urls) {
            if (isMatch(url, urlPath)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 判断url是否与规则配置:
     * ? 表示单个字符
     * * 表示一层路径内的任意字符串，不可跨层级
     * ** 表示任意层路径
     *
     * @param url     匹配规则
     * @param urlPath 需要匹配的url
     * @return
     */
    public static boolean isMatch(String url, String urlPath) {
        AntPathMatcher matcher = new AntPathMatcher();
        return matcher.match(url, urlPath);
    }

    private static void writeResponse(HttpServletResponse response, CommonResult result) throws IOException {
        response.setCharacterEncoding("utf-8");
        PrintWriter writer = response.getWriter();
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        writer.print(JSON.toJSONString(result));
        writer.close();
    }
}
