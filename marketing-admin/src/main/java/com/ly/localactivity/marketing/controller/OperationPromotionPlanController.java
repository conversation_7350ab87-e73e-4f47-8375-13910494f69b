package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanPageDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.*;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanContentService;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPromotionPlanController
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@RestController
@RequestMapping("/api/marketing/operationPromotionPlan")
@Api(value = "推广计划相关接口", tags = {"运营营销-推广计划相关接口"})
@Validated
public class OperationPromotionPlanController {
    @Resource
    private IOperationPromotionPlanService operationPromotionPlanService;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private IOperationPromotionPlanContentService operationPromotionPlanContentService;




    @ApiOperation("批量新增推广计划")
    @PostMapping("/saveBatch")
    public CommonResult<Boolean> saveBatch(@RequestBody @Valid OperationPromotionPlanBatchAddRo addRoList) {
        boolean res = operationPromotionPlanService.saveBatch(addRoList);
        if (res) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 是否可以锁定
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link Boolean}>
     */
    @ApiOperation("是否可以锁定")
    @PostMapping("/isCanLocked")
    public CommonResult<Boolean> isCanLocked(@RequestBody @Valid OperationPromotionIsCanLockedRo request) {
        return CommonResult.success(operationPromotionPlanService.isCanLocked(request));
    }

    /**
     * 获取已经锁定的顺位
     */
    @ApiOperation("获取已经锁定的顺位")
    @PostMapping("/listLockedIndex")
    public CommonResult<List<String>> listLockedIndex(@RequestBody @Valid OperationPromotionListLockedIndexRo request) {
        return CommonResult.success(operationPromotionPlanService.listLockedIndex(request));
    }

    /**
     * 获取已经占用的顺位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link List}<{@link String}>
     */
    @ApiOperation("获取已经占位的顺位")
    @PostMapping("/listPlaceholderIndex")
    public CommonResult<List<String>> listPlaceholderIndex(@RequestBody @Valid OperationPromotionListPlaceholderIndexRo request) {
        return CommonResult.success(operationPromotionPlanService.listPlaceholderIndex(request));
    }

    /**
     * 获取已经维护的顺位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link List}<{@link String}>>
     */
    @ApiOperation("获取已经维护的顺位")
    @PostMapping("/listMaintainIndex")
    public CommonResult<List<String>> listMaintainIndex(@RequestBody @Valid OperationPromotionListMaintainIndexRo request) {
        return CommonResult.success(operationPromotionPlanService.listMaintainIndex(request));
    }


    /**
     * 获取不可用时间段
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link OperationPromotionPlanCoverConditionVo}>
     */
    @ApiOperation("获取不可用时间段")
    @PostMapping("/listUnValidDate")
    public CommonResult<List<PeriodVo>> listUsedDate(@RequestBody @Valid OperationPromotionPlanUnavailableDateRo request) {
        return CommonResult.success(operationPromotionPlanService.getUnavailableDate(request));
    }

    /**
     * 获取当前顺位,所选推广时间推广计划覆盖情况
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link OperationPromotionPlanCoverConditionVo}>
     */
    @ApiOperation("获取当前顺位,所选推广时间推广计划覆盖情况(更高优先级)")
    @PostMapping("/getCoverCondition")
    public CommonResult<List<OperationPromotionPlanCoverConditionVo>> getCoverCondition(@RequestBody @Valid OperationPromotionPlanCoverConditionRo request) {
        return CommonResult.success(operationPromotionPlanService.getCoverCondition(request));
    }

    /**
     * 编辑
     *
     * @param editRo 编辑ro
     * @return {@link CommonResult}
     */
    @ApiOperation("编辑推广计划")
    @PostMapping("/edit")
    public CommonResult<Boolean> edit(@RequestBody @Valid OperationPromotionPlanSingleEditRo editRo) {
        boolean res = operationPromotionPlanService.saveSinglePlan(editRo, adminTokenUtils.getModifier(), true) > 0;
        if (res) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("获取推广计划详情")
    @GetMapping("/info")
    public CommonResult<OperationPromotionPlanSingleEditVo> getDetail(@Valid @Min(value = 1, message = "id必须大于0") @ApiParam("推广计划id") @RequestParam(value = "planId", required = true) Long planId) {
        return CommonResult.success(operationPromotionPlanService.getDetail(planId));
    }

    @ApiOperation("获取推广计划列表")
    @PostMapping("/list")
    public CommonResult<CommonPage<OperationPromotionPlanPageVo>> getList(@RequestBody OperationPromotionPlanPageDto request) {
        return CommonResult.success(operationPromotionPlanService.pageList(request));
    }

    @ApiOperation("根据运营点位或者推广计划获取相关联的所有推广计划的图片")
    @GetMapping("/getPlanImgList")
    public CommonResult<List<ImageVo>> getPlanImgList(@RequestParam(value = "positionId", required = false) @ApiParam("运营点位Id") Long positionId, @RequestParam(value = "planId", required = false) @ApiParam("推广计划Id") Long planId) {
        return CommonResult.success(operationPromotionPlanService.getPlanImgList(positionId, planId));
    }

    @ApiOperation(value = "改变推广计划有效状态")
    @PostMapping("/invalid")
    public CommonResult<Boolean> invalid(@RequestBody @Valid OperationPromotionPlanValidRo request) {
        boolean res = operationPromotionPlanService.changeValid(request);
        if (res) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation(value = "获取推广计划统计信息")
    @PostMapping("/getPromotionPlanStatistics")
    public CommonResult<PromotionPlanStatisticsVo> getPromotionPlanStatistics(@RequestBody @Valid PromotionPlanStatisticsRo request) {
        return CommonResult.success(operationPromotionPlanService.getPromotionPlanStatistics(request));
    }

    @ApiOperation(value = "获取运营点位对应最晚到期的推广计划到期天数")
    @GetMapping("/getLatestExpireDay")
    public CommonResult<Integer> getLatestExpireDay(@Valid @NotNull(message = "运营点位id不能为空") @RequestParam(value = "positionId", required = true) @ApiParam("运营点位Id") Long positionId) {
        return CommonResult.success(operationPromotionPlanService.getLatestExpireDay(positionId));
    }
    @GetMapping("/syncField")
    @ApiOperation("同步字段")
    public CommonResult<String> syncField() {
        return CommonResult.success(operationPromotionPlanContentService.syncField());
    }


}
