package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageSelectVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageVo;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * OperationPositionPageController
 *
 * <AUTHOR>
 * @date 2024-1-31
 */
@RestController
@RequestMapping("/api/marketing/operationPositionPage")
@Api(value = "频道页相关接口", tags = {"频道页相关接口"})
public class OperationPositionPageController {
    @Resource
    private IOperationPositionPageService operationPositionPageService;


    /**
     * 查询频道列表页(desc排序值)
     *
     * @return {@link CommonResult}<{@link List}<{@link OperationPositionPageVo}>>
     */
    @RequestMapping("/list")
    public CommonResult<List<OperationPositionPageVo>> listOperationPositionPage() {
        return CommonResult.success(operationPositionPageService.listOperationPositionPage());
    }

    /**
     * 根据名称查询
     *
     * @param keyword 关键词
     * @return {@link CommonResult}<{@link OperationPositionPageVo}>
     */
    @ApiOperation("根据名称查询")
    @GetMapping("/search")
    public CommonResult<List<OperationPositionPageSelectVo>> searchOperationPositionPage(@RequestParam("keyword") String keyword) {
        return CommonResult.success(operationPositionPageService.searchOperationPositionPage(keyword));
    }


    /**
     * 新增频道页(频道页名称+版本不能重复)
     *
     * @param request
     * @return
     */
    @RequestMapping("/insert")
    public CommonResult<Boolean> insertOperationPositionPage(@RequestBody @Validated OperationPositionPageRo request) {
        return CommonResult.success(operationPositionPageService.insertOperationPositionPage(request));
    }

    /**
     * 编辑/新增频道页(频道页名称+版本不能重复)
     * @param request
     * @return
     */
    @RequestMapping("/update")
    public CommonResult<Boolean> updateOperationPositionPage(@RequestBody @Validated OperationPositionPageRo request) {
        return CommonResult.success(operationPositionPageService.updateOperationPositionPage(request));
    }

    /**
     * 根据 id 获取频道页详情
     *
     * @param id id
     * @return {@link CommonResult}<{@link OperationPositionPageVo}>
     */
    @RequestMapping("/detail")
    public CommonResult<OperationPositionPageVo> detailOperationPositionPage(Long id) {
        return CommonResult.success(operationPositionPageService.detailOperationPositionPage(id));
    }

    /**
     * 频道页设置有效|无效
     * @param request
     * @return
     */
    @RequestMapping("/updateStatus")
    public CommonResult<Boolean> updateOperationPositionPageStatus(@RequestBody @Validated OperationPositionPageStatusRo request) {
        return CommonResult.success(operationPositionPageService.updateOperationPositionPageStatus(request));
    }
}
