//package com.ly.localactivity.marketing.controller;
//
//import com.ly.localactivity.marketing.common.bean.LoginVo;
//import com.ly.localactivity.framework.model.api.CommonResult;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * LoginIndex
// *
// * <AUTHOR>
// * @date 2024/1/29
// */
//@RestController
//@RequestMapping("/api")
//public class LoginController {
//    @ApiOperation("登录")
//    @PostMapping("/login")
//    public CommonResult<LoginVo> login() {
//        //TODO:接入公共统一权限
//        return CommonResult.success(LoginVo.builder().token("123123").refreshToken("2222").build());
//    }
//}
