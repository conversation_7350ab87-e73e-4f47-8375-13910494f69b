package com.ly.localactivity.marketing.controller;


import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionDetailRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionEditRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionDetailVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionSelectVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import com.ly.localactivity.marketing.domain.marketing.vo.StatisticsVo;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPositionController
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@RestController
@RequestMapping("/api/marketing/operationPosition")
@Api(value = "运营点位相关接口", tags = {"运营营销-运营点位相关接口"})
public class OperationPositionController {
    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private RedisUtils redisUtils;

    public static final String PREFIX = "tclami";


    /**
     * 分页查询运营点位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link OperationPositionVo}>>
     */
    @ApiOperation("运营点位列表查询接口")
    @PostMapping("/list")
    public CommonResult<CommonPage<OperationPositionVo>> list(@RequestBody @Validated OperationPositionListRo request) {
        return CommonResult.success(operationPositionService.selectOperationPositionPage(request));
    }

    /**
     * 查询运营点位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link OperationPositionVo}>>
     */
    @ApiOperation("运营点位列表查询接口")
    @PostMapping("/queryList")
    public CommonResult<List<OperationPositionVo>> queryList(@RequestBody @Validated OperationPositionListRo request) {
        return CommonResult.success(operationPositionService.queryList(request));
    }

    @ApiOperation("红包点位列表查询接口")
    @PostMapping("/queryCouponPositionList")
    public CommonResult<List<OperationPositionSelectVo>> queryCouponPositionList(@RequestBody @Validated OperationPositionListRo request) {
        return CommonResult.success(operationPositionService.queryCouponPositionList(request));
    }


    /**
     * 查询运营点
     */
    @ApiOperation(value = "查询运营点位接口", notes = "根据运营点位ID查询运营点位")
    @GetMapping("/getOperationPosition")
    public CommonResult<OperationPositionVo> getOperationPosition(@RequestParam("id") @Validated @ApiParam(name = "id", value = "运营点位ID", required = true) @NotNull(message = "运营点位id不能为空") @Min(value = 1, message = "id必须大于0") Long id) {
        return CommonResult.success(operationPositionService.getOperationPosition(id));
    }

    /**
     * 修改运营点位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link Boolean}>
     */
    @ApiOperation("修改运营点位接口")
    @PostMapping("/update")
    public CommonResult<Boolean> update(@RequestBody @Validated OperationPositionEditRo request) {
        operationPositionService.editOperationPosition(request);
        return CommonResult.success();
    }

    /**
     * 新增运营点位
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link Boolean}>
     */
    @ApiOperation("新增运营点位接口")
    @PostMapping("/add")
    public CommonResult<Boolean> add(@RequestBody @Validated OperationPositionEditRo request) {
        operationPositionService.editOperationPosition(request);
        return CommonResult.success();
    }

    /**
     * 查询运营点位详情
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link OperationPositionVo}>
     */
    @ApiOperation("查询运营点位详情接口")
    @PostMapping("/detail")
    public CommonResult<OperationPositionDetailVo> detail(@RequestBody @Validated OperationPositionDetailRo request) {
        return CommonResult.success(operationPositionService.selectOperationPositionDetail(request));
    }

    /**
     * 获取统计数据
     */
    @ApiOperation("获取统计数据")
    @GetMapping("/getStatistics")
    public CommonResult<StatisticsVo> getStatistics() {
        return CommonResult.success(operationPositionService.getStatistics());
    }


    /**
     * 刷新缓存
     */
    @ApiOperation("刷新缓存")
    @GetMapping("/refreshOperationPositionCache")
    public CommonResult<Boolean> refreshOperationPositionCache() {
        List<String> keys = redisUtils.keys(PREFIX + "getPromotionPlan*");
        keys.forEach(key -> redisUtils.del(key));
        return CommonResult.success();
    }


}
