//package com.ly.localactivity.marketing.controller;
//
//import com.ly.localactivity.marketing.common.bean.LoginUserDto;
//import com.ly.localactivity.marketing.common.bean.MenuVo;
//import com.ly.localactivity.framework.model.api.CommonResult;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * UserController
// *
// * <AUTHOR>
// * @date 2024/1/11
// */
//@RestController
//@RequestMapping("/api/user")
//public class UserController {
//    @GetMapping("/info")
//    @ApiOperation("获取用户信息")
//    public CommonResult userInfo() {
//        return CommonResult.success(new LoginUserDto(1L, "123", "aaron", "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80"));
//    }
//
//    @GetMapping("/menus")
//    @ApiOperation("获取用户菜单")
//    public CommonResult menus() {
//        List<MenuVo> menuVos = new ArrayList<>();
//        MenuVo menuVo = new MenuVo();
//        menuVo.setName("营销管理");
//        //TODO:根据菜单获取
//
//        return CommonResult.success(menuVos);
//    }
//}
