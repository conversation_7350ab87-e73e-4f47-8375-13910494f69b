package com.ly.localactivity.marketing.controller.job;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.service.marketing.IPromotionPlanAutoInvalidService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * OperationJobController
 *
 * <AUTHOR>
 * @date 2024/3/22
 */
@RestController
@RequestMapping("/api/job/operation")
public class OperationJobController {
    @Resource
    private IPromotionPlanAutoInvalidService promotionPlanAutoInvalidService;

    @PostMapping("/autoInvalidPlan")
    @ApiOperation("自动无效推广计划")
    public CommonResult autoInvalid() {
        String traceId = IdUtil.simpleUUID();
        ThreadUtil.execAsync(() -> promotionPlanAutoInvalidService.autoInvalid(traceId));
        return CommonResult.success("操作成功,traceId:" + traceId);
    }
}
