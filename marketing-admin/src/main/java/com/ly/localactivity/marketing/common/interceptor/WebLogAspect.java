package com.ly.localactivity.marketing.common.interceptor;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.ly.localactivity.framework.common.CommonConstant;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.annotation.IgnoreLog;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * WebLogAspect
 *
 * @auth aaron
 * @date 2023/12/20
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class WebLogAspect {
    @Resource
    private AdminTokenUtils adminTokenUtils;

    @Pointcut("execution(* com.ly.localactivity.marketing.controller.*Controller.*(..))")
    public void webLog() {

    }

    @Around(value = "webLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        // 获取签名
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        // 获得方法
        Method method = methodSignature.getMethod();

        // 开始时间
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long time = System.currentTimeMillis() - startTime;
            recordLog(joinPoint, time, result, method, request, null);
            return result;
        } catch (Throwable ex) {
            // 方法执行耗时
            long time = System.currentTimeMillis() - startTime;
            recordLog(joinPoint, time, null, method, request, ex);
            throw ex;
        }

    }

    private void recordLog(ProceedingJoinPoint joinPoint, long time, Object result, Method method, HttpServletRequest request, Throwable ex) {

        IgnoreLog ignoreLog = method.getAnnotation(IgnoreLog.class);
        if (ignoreLog != null) {
            return;
        }

        // 请求的方法名
        String methodName = method.getName();
        // 接口请求参数
        Object[] args = joinPoint.getArgs();
        // 获取请求的 URL 和 IP

        // 获取请求 URL
        String url = request.getRequestURL().toString();
        // 获取请求 IP
        String ip = request.getRemoteAddr();

        if (StringUtils.isNotBlank(url) && url.contains("/health")) {
            // 心跳检测不记录日志
            return;
        }
        String requestId = request.getAttribute(CommonConstant.REQUEST_ID) != null ? request.getAttribute(CommonConstant.REQUEST_ID).toString() : "";
        String tranceId = MDC.get(CommonConstant.TRACEID_NAME);
        if (StringUtils.isBlank(tranceId)) {
            tranceId = "";
        }

        Map<String, String> extraInfo = new HashMap<>();
        extraInfo.put("class", method.getDeclaringClass().getName());
        extraInfo.put("method", methodName);
        extraInfo.put("time", String.valueOf(time) + "ms");
        extraInfo.put("url", url);
        extraInfo.put("ip", ip);
        if (ex != null) {
            SkyLogUtils.error("Global", method.getDeclaringClass().getSimpleName(), methodName, url + "请求发生异常：" + ex.getMessage(), tranceId, requestId, ex);
            extraInfo.put("exception", ex.getMessage());
        }

        List<Object> logArgs = Arrays
                .stream(args)
                .filter(arg -> (!(arg instanceof HttpServletRequest)
                        && !(arg instanceof HttpServletResponse))
                        && !(arg instanceof MultipartFile[])
                        && !(arg instanceof MultipartFile))
                .collect(Collectors.toList());
        extraInfo.put("args", JSONArray.toJSONString(logArgs));


        if (result != null) {
            if (!(result instanceof ResponseEntity)) {
                extraInfo.put("result", JSON.toJSONString(result));
            }
        }

        String employeeNo = adminTokenUtils.getEmployeeNo();
        if (StringUtils.isNotBlank(employeeNo)) {
            extraInfo.put("employeeNo", employeeNo);
        }

        SkyLogUtils.info("Global", method.getDeclaringClass().getSimpleName(), methodName, url, tranceId, requestId, extraInfo);
    }
}
