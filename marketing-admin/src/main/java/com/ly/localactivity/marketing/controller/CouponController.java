package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.application.model.vo.CouponExportVo;
import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchListDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRelationResourceRuleDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.*;
import com.ly.localactivity.marketing.domain.resource.vo.ProductOptionVo;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * fsd
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@RestController
@RequestMapping("/api/marketing/coupon")
@Api(value = "红包营销接口", tags = {"运营营销-红包营销接口"})
public class CouponController {
    @Resource
    private ICouponBatchListService couponBatchListService;

    /**
     * 查询红包列表
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link OperationPositionVo}>>
     */
    @ApiOperation("查询红包列表")
    @PostMapping("/pageQueryCouponBatchList")
    public CommonResult<CommonPage<CouponBatchListVo>> pageQueryCouponBatchList(@RequestBody @Validated QueryCouponBatchListRo request) {
        return CommonResult.success(couponBatchListService.pageQueryCouponBatchList(request));
    }

    /**
     * 查询红包统计数据
     */
    @ApiOperation("查询红包统计数据")
    @PostMapping("/queryCouponStatistics")
    public CommonResult<List<CouponStatisticsVo>> queryCouponStatistics(@RequestBody @Validated QueryCouponBatchListRo request) {
        return CommonResult.success(couponBatchListService.queryCouponStatistics(request));
    }

    /**
     * 分页查询红包批次列表
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchDetail}>>
     */
    @ApiOperation("查询红包批次列表")
    @PostMapping("/pageQueryCouponBatchDetailList")
    public CommonResult<CommonPage<CouponBatchDetailDto>> pageQueryCouponBatchDetailList(@RequestBody @Validated QueryCouponBatchDetailListRo request) {
        return CommonResult.success(couponBatchListService.pageQueryCouponBatchDetailList(request));
    }

    /**
     * 查询鲲鹏红包信息并更新本地数据
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchDetail}>>
     */
    @ApiOperation("查询红包批次列表")
    @PostMapping("/queryKunPengCouponBatchDetailAndUpdateDb")
    public CommonResult<List<CouponBatchDetailDto>> queryKunPengCouponBatchDetailAndUpdateDb(@RequestBody @Validated KunPengCouponBatchDetailListRo request) {
        return CommonResult.success(couponBatchListService.queryKunPengCouponBatchDetailAndUpdateDb(request));
    }

    /**
     * 查询鲲鹏红包信息
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchDetail}>>
     */
    @ApiOperation("查询红包批次列表")
    @PostMapping("/queryKunPengCouponBatchDetail")
    public CommonResult<List<CouponBatchDetailDto>> queryKunPengCouponBatchDetail(@RequestBody @Validated KunPengCouponBatchDetailListRo request) {
        return CommonResult.success(couponBatchListService.queryKunPengCouponBatchDetail(request));
    }

    /**
     * 保存红包信息
     */
    @ApiOperation("保存红包信息")
    @PostMapping("/saveCouponBatch")
    public CommonResult<Long> saveCouponBatch(@RequestBody @Validated(BusinessValidation.class) CouponBatchListDto request) {
        return CommonResult.success(couponBatchListService.saveCouponBatch(request));
    }

    /**
     * 查询红包批次信息
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchListDto}>>
     */
    @ApiOperation("查询红包批次列表")
    @PostMapping("/queryCouponBatchDetail")
    public CommonResult<CouponBatchListDto> queryCouponBatchDetail(@RequestBody @Validated CouponBatchQueryRo request) {
        return CommonResult.success(couponBatchListService.queryCouponBatchDetail(request));
    }

    /**
     * 查询红包规则信息
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchListDto}>>
     */
    @ApiOperation("查询红包规则信息")
    @PostMapping("/queryCouponBatchRule")
    public CommonResult<CouponBatchListDto> queryCouponBatchRule(@RequestBody @Validated CouponBatchQueryRo request) {
        return CommonResult.success(couponBatchListService.queryCouponBatchRule(request));
    }

    /**
     * 查询红包渠道信息
     *
     * @param request 请求
     * @return {@link CommonResult}<{@link CommonPage}<{@link CouponBatchListDto}>>
     */
    @ApiOperation("查询红包渠道信息")
    @PostMapping("/queryCouponBatchOperation")
    public CommonResult<CouponBatchListDto> queryCouponBatchOperation(@RequestBody @Validated CouponBatchQueryRo request) {
        return CommonResult.success(couponBatchListService.queryCouponBatchOperation(request));
    }


    @ApiOperation("查询红包简要信息")
    @GetMapping("/queryCouponSimpleInfo")
    public CommonResult<CouponSimpleInfoVo> queryCouponSimpleInfo(@RequestParam("couponBatchId") Long couponBatchId) {
        return CommonResult.success(couponBatchListService.queryCouponSimpleInfo(couponBatchId));
    }


    @ApiOperation(value = "商家提交/撤销红包审核")
    @PostMapping("changeCouponAuditStatusToB")
    public CommonResult<Boolean> changeCouponAuditStatusToB(@RequestBody @Validated(BusinessValidation.class) ChangeCouponAuditStatusRo request) {
        return CommonResult.success(couponBatchListService.changeCouponAuditStatus(request));
    }

    @ApiOperation("商家上/下架红包")
    @PostMapping("changeStatus")
    public CommonResult<Boolean> changeStatus(@RequestBody CouponChangeStatusRo request) {
        return CommonResult.success(couponBatchListService.changeStatus(request));
    }

    @ApiOperation("查询refId是否已经被使用")
    @PostMapping("queryHasBindOtherCouponRefIdList")
    public CommonResult<List<String>> queryHasBindOtherCouponRefIdList(@RequestBody QueryHasBindOtherCouponRefIdListRo request) {
        return CommonResult.success(couponBatchListService.queryHasBindOtherCouponRefIdList(request));
    }

    @ApiOperation("检查红包批次是否能被正常使用")
    @PostMapping("checkCouponBatchList")
    public CommonResult<List<CheckCouponBatchVo>> checkCouponBatchList(@RequestBody CheckCouponBatchRo request) {
        return CommonResult.success(couponBatchListService.checkCouponBatchList(request));
    }

    @ApiOperation(value = "审核红包")
    @PostMapping("auditCouponBatch")
    public CommonResult<Boolean> auditCouponBatch(@RequestBody @Validated AuditCouponBatchRo request) {
        return CommonResult.success(couponBatchListService.auditCouponBatch(request));
    }


    @ApiOperation("查询已关联的产品")
    @PostMapping("queryCouponBatchRelatedProductList")
    public CommonResult<List<ProductOptionVo>> queryCouponBatchRelatedProductList(@RequestBody @Validated CouponRelatedProductRo request) {
        return CommonResult.success(couponBatchListService.queryCouponBatchRelatedProductList(request));
    }

    @ApiOperation("删除红包")
    @PostMapping("deleteCouponBatch")
    public CommonResult<Boolean> deleteCouponBatch(@RequestBody @Validated CouponBatchQueryRo request) {
        return CommonResult.success(couponBatchListService.deleteCouponBatchList(request));
    }

    @ApiOperation("导出红包红包")
    @PostMapping("exportCouponBatchList")
    public CommonResult<List<CouponExportVo>> exportCouponBatchList(QueryCouponBatchListRo request) {
        List<CouponExportVo> couponExportVoList = couponBatchListService.exportCouponBatchList(request);
        return CommonResult.success(couponExportVoList);
    }
    @ApiOperation("获取运营中红包批次规则信息")
    @PostMapping("getAllCouponBatchRelationResourceRuleDto")
    public CommonResult<List<CouponBatchRelationResourceRuleDto>> getAllCouponBatchRelationResourceRuleDto() {
        return CommonResult.success(couponBatchListService.getAllCouponBatchRelationResourceRuleDto());
    }

}
