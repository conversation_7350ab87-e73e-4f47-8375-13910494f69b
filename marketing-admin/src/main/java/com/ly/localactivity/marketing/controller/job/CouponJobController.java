package com.ly.localactivity.marketing.controller.job;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRelationResourceRuleDto;
import com.ly.localactivity.marketing.domain.marketing.ro.BatchPushCouponRo;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchListService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * CouponJobController
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
@RestController
@RequestMapping("/api/job/coupon")
public class CouponJobController {

    @Resource
    private ICouponBatchListService couponBatchListService;
    @Resource
    private ICouponQueryService couponQueryService;

    @ApiOperation("刷新所有待审核鲲鹏红包批次")
    @PostMapping("refreshWaitingAuditCouponBatchDetail")
    public CommonResult<Boolean> refreshWaitingAuditCouponBatchDetail(@RequestBody @Validated BatchPushCouponRo request) {
        return CommonResult.success(couponBatchListService.refreshWaitingAuditCouponBatchDetail(request));
    }

    @ApiOperation("刷新所有红包批次")
    @PostMapping("refreshAll")
    public CommonResult refreshAllCoupon() {
        int count = couponQueryService.refreshCache("");
        return CommonResult.success(count);
    }
}