<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchDetailMapper">

    <select id="listAvailable" resultType="java.lang.String" >
        -- 查询前台可用批次号
        select distinct cbd.batch_no
        from coupon_batch_detail cbd
        inner join coupon_batch_list cbl on cbd.coupon_batch_id = cbl.id and cbl.delete_flag=0 and cbl.status=3
        inner join coupon_batch_platform cbp on cbd.id = cbp.coupon_batch_detail_id and cbp.delete_flag=0
        where cbd.delete_flag=0 and cbp.delete_flag=0
        and cbd.status = 2
        and cbp.platform = #{platform}
        and #{dateNow} between cbd.event_valid_begin_date and cbd.event_valid_end_date
        <if test="cityIds != null and cityIds.size() > 0">
            -- 指定出发地
            and cbl.id in (
            select cbrd.coupon_batch_id
            from coupon_batch_rule_detail cbrd
            where limit_type = 2 and delete_flag = 0 and cbrd.data_id in
            <foreach collection="cityIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="secondCategorys != null and secondCategorys.size() > 0">
            -- 指定二级品类
            and cbl.id in (
            select cbrd.coupon_batch_id
            from coupon_batch_rule_detail cbrd
            where limit_type = 3 and delete_flag = 0 and cbrd.data_id in
            <foreach collection="secondCategorys" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="resourceIds != null and resourceIds.size() > 0">
            -- 指定资源
            and cbl.id in (
            select cbrd.coupon_batch_id
            from coupon_batch_rule_detail cbrd
            where limit_type = 4 and delete_flag = 0 and cbrd.data_id in
            <foreach collection="resourceIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>

    </select>
    <select id="listAllAvailable" resultType="java.lang.String">
        select distinct cbd.batch_no from
            coupon_batch_list cbl
                inner join coupon_batch_detail cbd on cbl.id = cbd.coupon_batch_id
                inner join coupon_batch_platform cbp on cbd.coupon_batch_id = cbp.coupon_batch_id
                inner join coupon_batch_rule cbr on cbl.id = cbr.coupon_batch_id
                left join coupon_batch_rule_detail cbrd on cbrd.coupon_batch_id = cbl.id and cbr.id = cbrd.coupon_batch_rule_id and cbrd.delete_flag = 0
                <if test="positionCode != null and positionCode != ''">
                    inner join coupon_batch_operation cbo on cbl.id = cbo.coupon_batch_id and cbo.delete_flag = 0
                </if>
        where   cbl.delete_flag = 0 and cbd.delete_flag = 0 and cbp.delete_flag = 0 and cbr.delete_flag = 0 and cbl.`status` = 3 and cbd.`status` = 2
          and cbp.platform = #{platform}
          AND #{dateNow} BETWEEN cbd.event_valid_begin_date AND cbd.event_valid_end_date
        <if test="positionCode != null and positionCode != ''">
            and cbo.operation_position_code = #{positionCode}
        </if>
          and (
            -- 城市id 资源id 二级品类id 和 所有红包 取并集 商家所有红包只能作用于当前供应商
            <if test="(cityIds != null and cityIds.size() > 0) or (secondCategorys != null and secondCategorys.size() > 0) or (resourceIds != null and resourceIds.size() > 0)">
                (
                    <if test="cityIds != null and cityIds.size() > 0">
                        (
                            <if test="resourceIds == null or resourceIds.size() == 0">
                                cbl.type = 1 and -- 无resourceId 只查询平台红包
                            </if>
                            cbrd.limit_type = 2 and cbrd.data_id in
                        <foreach collection="cityIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="secondCategorys != null and secondCategorys.size() > 0">
                        <if test="cityIds != null and cityIds.size() > 0">
                            or
                        </if>
                      (
                            <if test="resourceIds == null or resourceIds.size() == 0">
                                cbl.type = 1 and -- 无resourceId 只查询平台红包
                            </if>
                          cbrd.limit_type = 3 and cbrd.data_id in
                      <foreach collection="secondCategorys" item="item" separator="," open="(" close=")">
                          #{item}
                      </foreach>
                      )
                     </if>
                    <if test="resourceIds != null and resourceIds.size() > 0">
                        <if test="(cityIds != null and cityIds.size() > 0) or (secondCategorys != null and secondCategorys.size() > 0)">
                            or
                        </if>
                        (cbrd.limit_type = 4 and cbrd.data_id in
                        <foreach collection="resourceIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                )
                or
            </if>
                    (
                        cbr.limit_type = 1 and
                        (
                            cbl.type = 1
                            <if test="supplierIds != null and supplierIds.size() > 0">
                                or (cbl.type = 2 and cbl.supplier_id in
                                <foreach collection="supplierIds" item="item" separator="," open="(" close=")">
                                    #{item}
                                </foreach>)
                            </if>
                        )
                    )



              );
    </select>
    <select id="selectNeedRefreshCouponBatchDetailList"
            resultType="com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail">
        select cbd.coupon_batch_id, cbd.id , cbd.batch_no
        from coupon_batch_detail cbd
        inner join coupon_batch_list cbl on cbl.id = cbd.coupon_batch_id
        where cbl.delete_flag = 0 and cbd.delete_flag = 0
        <if test="couponBatchListStatusList != null and couponBatchListStatusList.size > 0">
            and cbl.`status` in
            <foreach collection="couponBatchListStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="couponBatchDetailStatusList != null and couponBatchDetailStatusList.size > 0">
            and cbd.`status` in
            <foreach collection="couponBatchDetailStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and length(cbd.batch_no) > 0
        <if test="couponBatchIds != null and couponBatchIds.size > 0">
            and cbd.coupon_batch_id in
            <foreach collection="couponBatchIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
