<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchRuleMapper">

    <select id="listByBatchNo" resultType="com.ly.localactivity.marketing.domain.marketing.CouponBatchRule">
        select cbr.*
        from coupon_batch_rule cbr
                 inner join coupon_batch_list cbl on cbr.coupon_batch_id = cbl.id and cbl.delete_flag=0
                 inner join coupon_batch_detail cbd on cbr.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
        where cbd.batch_no=#{batchNo}
    </select>

    <select id="listByBatchNos" resultType="com.ly.localactivity.marketing.domain.marketing.CouponBatchRule">
        select cbr.*
        from coupon_batch_rule cbr
                 inner join coupon_batch_list cbl on cbr.coupon_batch_id = cbl.id and cbl.delete_flag=0
                 inner join coupon_batch_detail cbd on cbr.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
        where cbd.batch_no in
        <foreach collection="batchNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
