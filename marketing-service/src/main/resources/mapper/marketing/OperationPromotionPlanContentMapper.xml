<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentMapper">

    <select id="selectByParamTemplate" resultType="com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent">
        select con.*
        from operation_promotion_plan_content con
                 inner join operation_promotion_plan plan on con.plan_id = plan.id
        where con.delete_flag = 0 and plan.delete_flag = 0 and plan.valid_flag=1
          and con.link_type=2 and con.link_template_id=#{templateId}
    </select>
</mapper>
