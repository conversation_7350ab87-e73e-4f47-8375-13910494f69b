<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.OperationPositionMapper">
    <resultMap id="listOperationPositionPageResultMap"
               type="com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="pageName" property="pageName"/>
        <result column="pageVersion" property="pageVersion"/>
        <result column="status" property="status"/>
        <result column="modifiedTime" property="modifiedTime"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierNo" property="modifierNo"/>
        <result column="emptyFlag" property="emptyFlag"/>
        <result column="outputType" property="outputType"/>
        <result column="coverRegionType" property="coverRegionType"/>
        <result column="expireDays" property="expireDays"/>
        <result column="imageMaxCount" property="imageMaxCount"/>
        <result column="imageHeight" property="imageHeight"/>
        <result column="imageWidth" property="imageWidth"/>
        <result column="imageMaxSize" property="imageMaxSize"/>
        <result column="keywordMaxCount" property="keywordMaxCount"/>
        <result column="keywordMaxLength" property="keywordMaxLength"/>
        <result column="positionPageId" property="positionPageId"/>
        <result column="vindicateFlag" property="vindicateFlag"/>
        <result column="subTitleMaxLength" property="subTitleMaxLength"/>
        <collection property="platforms"
                    ofType="com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo"
                    column="id"
                    select="listOperationPositionPlatform"
                    javaType="list"/>
    </resultMap>
    <resultMap id="platformResultMap" type="com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo">
        <result column="platformName" property="name"/>
        <result column="platformCode" property="code"/>
    </resultMap>

    <select id="listOperationPositionPage"
            resultMap="listOperationPositionPageResultMap">
        select
        op.id as id,
        op.code,
        op.name,
        op.image_max_count as imageMaxCount,
        op.image_height as imageHeight,
        op.image_width as imageWidth,
        op.image_max_size as imageMaxSize,
        op.position_page_id as positionPageId,
        opp.name as pageName,
        opp.version as pageVersion,
        op.image_url as imageUrl,
        op.output_type as outputType,
        op.status as status,
        op.keyword_max_count as keywordMaxCount,
        op.keyword_max_length as keywordMaxLength,
        op.cover_region_type as coverRegionType,
        op.expire_days as expireDays,
        op.modified_time as modifiedTime,
        op.modifier as modifier,
        op.modifier_no as modifierNo,
        op.empty_flag as emptyFlag,
        op.required_maintained_flag as vindicateFlag,
        op.sub_title_max_length as subTitleMaxLength,
        op.alias as alias
        from operation_position op
        left join operation_position_page opp on opp.id = op.position_page_id and opp.delete_flag = 0
        left join operation_position_platform oppf on oppf.position_id = op.id and oppf.delete_flag = 0
        where op.delete_flag = 0
        <if test="query.codeOrName != null and query.codeOrName != ''">
            and (op.code like concat('%',#{query.codeOrName},'%') or op.name like concat('%',#{query.codeOrName},'%'))
        </if>
        <if test="query.platforms != null and query.platforms.size>0">
            and oppf.platform in
            <foreach collection="query.platforms" item="platform" open="(" separator="," close=")">
                #{platform}
            </foreach>
        </if>
        <if test="query.status != null">
            and op.status = #{query.status}
        </if>
        <if test="query.positionPageId != null ">
            and op.position_page_id=#{query.positionPageId}
        </if>
        <if test="query.pageName != null and query.pageName != ''">
            and opp.name like concat('%',#{query.pageName},'%')
        </if>
        <if test="query.id != null">
            and op.id = #{query.id}
        </if>
        <if test="query.requiredMaintainedFlag!=null">
            and op.required_maintained_flag = #{query.requiredMaintainedFlag}
            and op.empty_flag=0
        </if>
        group by op.id order by op.id desc
    </select>

    <select id="listOperationPositionPlatform" resultMap="platformResultMap">
        select sdd.dict_label as platformName,
               sdd.dict_value as platformCode
        from operation_position_platform oppf
                 left join sys_dict_detail sdd
                           on sdd.dict_value = oppf.platform and sdd.dict_type = 'platform'
        where oppf.delete_flag = 0
          and sdd.delete_flag = 0
          and oppf.position_id = #{id}
    </select>
</mapper>
