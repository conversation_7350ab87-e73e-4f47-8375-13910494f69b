<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.OperationPositionPlatformMapper">

    <insert id="insertBatch">
        insert into operation_position_platform(position_id,platform,create_time,creator,delete_flag,modifier)
        values
        <foreach collection="list" item="item" index="index"  separator=",">
            (#{item.positionId},#{item.platform},#{item.createTime},#{item.creator},#{item.deleteFlag},#{item.modifier})
        </foreach>
    </insert>
</mapper>
