<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchRuleDetailMapper">

    <select id="listByCouponBatchNo" resultType="com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail" >
        select cbrd.*
        from coupon_batch_rule_detail cbrd
                 inner join coupon_batch_rule cbr on cbrd.coupon_batch_rule_id = cbr.id and cbr.delete_flag=0
                 inner join coupon_batch_detail cbd on cbrd.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
                 inner join coupon_batch_list cbl on cbrd.coupon_batch_id=cbl.id and cbl.delete_flag=0
        where cbrd.delete_flag=0 and cbd.batch_no=#{couponBatchNo}
    </select>

    <select id="listByCouponBatchNos" resultType="com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail" >
        select cbrd.*
        from coupon_batch_rule_detail cbrd
                 inner join coupon_batch_rule cbr on cbrd.coupon_batch_rule_id = cbr.id and cbr.delete_flag=0
                 inner join coupon_batch_detail cbd on cbrd.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
                 inner join coupon_batch_list cbl on cbrd.coupon_batch_id=cbl.id and cbl.delete_flag=0
        where cbrd.delete_flag=0 and cbd.batch_no in
        <foreach collection="batchNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
