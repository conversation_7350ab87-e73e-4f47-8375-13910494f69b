<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchOperationMapper">

    <select id="listBatchNoByOperationCode" resultType="java.lang.String">
        -- 根据运营点位查询有效的批次号
        select cbd.batch_no
        from coupon_batch_operation cbo
                 inner join coupon_batch_detail cbd on cbo.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
                 inner join coupon_batch_platform cbp on cbd.id = cbp.coupon_batch_detail_id and cbp.delete_flag=0
                 inner join coupon_batch_list cbl on cbl.id=cbo.coupon_batch_id and cbl.delete_flag=0 and cbl.status=3
        where cbd.status=2 and cbo.delete_flag=0
          and cbp.platform = #{platform}
          and #{dtNow} between cbd.event_valid_begin_date and cbd.event_valid_end_date
          and cbo.operation_position_code=#{positionCode}
    </select>
</mapper>
