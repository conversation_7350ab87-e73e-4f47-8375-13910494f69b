<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanMapper">

    <select id="listPage" resultType="com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanPageVo">
        select
        plan.id
        ,plan.code
        ,plan.name
        ,plan.position_id
        ,plan.position_code
        ,plan.platform
        ,plan.promote_type
        ,plan.index_value
        ,plan.valid_date_type
        ,plan.valid_begin_date
        ,plan.valid_end_date
        ,plan.top_flag
        ,plan.place_flag
        ,plan.default_show_type
        ,plan.default_flag
        ,plan.reason_type
        ,plan.reason_remark
        ,plan.invalid_time
        ,plan.valid_flag
        ,plan.alias
        ,plan.delete_flag
        ,plan.creator
        ,plan.create_time
        ,plan.modifier
        ,plan.modifier_no
        ,plan.modified_time
        ,content.keyword
        ,content.image_url
        ,content.link_url
        ,content.sub_title
        <if test="model.regionIds != null or model.areaIds != null">
            ,region.region_id
            ,region.region_zone_id
        </if>
        from operation_promotion_plan plan
        inner join operation_promotion_plan_content content on plan.id = content.plan_id and content.delete_flag = 0

        <if test="model.regionIds != null or model.areaIds != null">
            left join operation_promotion_plan_region region on plan.id=region.plan_id and region.delete_flag = 0
        </if>
        where plan.delete_flag = 0
        <choose>
            <when test="model.id != null and model.id > 0">
                and plan.id = #{model.id}
            </when>
            <otherwise>
                <if test="model.positionId != null">
                    and plan.position_id = #{model.positionId}
                </if>
                <if test="model.validStartDate != null">
                    and (plan.valid_end_date >= #{model.validStartDate})
                </if>
                <if test="model.validEndDate != null">
                    and (plan.valid_begin_date &lt;= #{model.validEndDate})
                </if>
                <if test="model.keyword != null and model.keyword != ''">
                    and (plan.name like concat('%',#{model.keyword},'%')
                    or plan.code like concat('%',#{model.keyword},'%')
                    )
                </if>
                <if test="model.alias != null and model.alias != ''">
                    and plan.alias like concat('%',#{model.alias},'%')
                </if>
                <if test="model.platforms != null and model.platforms.size>0">
                    AND plan.platform in
                    <foreach collection="model.platforms" index="index" item="item" open="("
                             separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="model.statusList != null and model.statusList.size() >0">
                    and(
                    <foreach collection="model.statusList" item="status" index="index" open="(" separator="or"
                             close=")">
                        <choose>
                            <when test="status == 1">
                                plan.valid_flag =1
                                and plan.valid_begin_date > #{model.currentDate}
                            </when>
                            <when test="status == 2">
                                plan.valid_flag =1
                                and(
                                (plan.valid_begin_date &lt;= #{model.currentDate}
                                and plan.valid_end_date >= #{model.currentDate})
                                or
                                plan.valid_date_type = 1
                                )

                            </when>
                            <when test="status == 3">
                                plan.valid_flag =1
                                and plan.valid_date_type = 2
                                and plan.valid_end_date &lt; #{model.currentDate}
                            </when>
                            <when test="status == 4">
                                plan.valid_flag = 0
                            </when>
                        </choose>
                    </foreach>
                    )
                </if>
                <!--                <if test="model.status != null">-->
                <!--                    <choose>-->
                <!--                        <when test="model.status == 1">-->
                <!--                            and plan.valid_flag =1-->
                <!--                            and plan.valid_begin_date > #{model.currentDate}-->
                <!--                        </when>-->
                <!--                        <when test="model.status == 2">-->
                <!--                            and plan.valid_flag =1-->
                <!--                            and(-->
                <!--                            (plan.valid_begin_date &lt;= #{model.currentDate}-->
                <!--                            and plan.valid_end_date >= #{model.currentDate})-->
                <!--                            or-->
                <!--                            plan.valid_date_type = 1-->
                <!--                            )-->

                <!--                        </when>-->
                <!--                        <when test="model.status == 3">-->
                <!--                            and plan.valid_flag =1-->
                <!--                            and plan.valid_date_type = 2-->
                <!--                            and plan.valid_end_date &lt; #{model.currentDate}-->
                <!--                        </when>-->
                <!--                        <when test="model.status == 4">-->
                <!--                            and plan.valid_flag = 0-->
                <!--                        </when>-->
                <!--                    </choose>-->
                <!--                </if>-->
                <if test="model.promoteType != null">
                    <if test="model.promoteType != 0">
                        and plan.promote_type = #{model.promoteType}
                    </if>
                    <if test="model.promoteType == 0">
                        and plan.default_flag = 1
                    </if>
                </if>
                <if test="(model.regionIds != null and model.regionIds.size>0) or (model.areaIds != null and model.areaIds.size>0)">
                    and(
                    <if test="model.areaIds != null and model.areaIds.size>0">
                        region.region_zone_id in
                        <foreach collection="model.areaIds" item="item" open="(" close=")" index="index" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="model.regionIds != null and model.regionIds.size>0">
                        <if test="model.areaIds != null and model.areaIds.size>0">
                            or
                        </if>
                        region.region_id in
                        <foreach collection="model.regionIds" item="item" index="index" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                    </if>

                    )
                </if>

                <if test="model.reasonType != null and model.reasonType != ''">
                    and plan.reason_type = #{model.reasonType}
                </if>
                <if test="model.reasonRemark != null and model.reasonRemark != ''">
                    and plan.reason_remark like concat('%',#{model.reasonRemark},'%')
                </if>
                <if test="model.defaultFlag != null">
                    and plan.default_flag = #{model.defaultFlag}
                </if>
                <if test="model.topFlag != null">
                    and plan.top_flag = #{model.topFlag}
                </if>
                <if test="model.placeFlag != null">
                    and plan.place_flag = #{model.placeFlag}
                </if>
                <if test="model.modifier != null and model.modifier != ''">
                    and (plan.modifier_no = #{model.modifier} or plan.modifier like concat('%',#{model.modifier},'%'))
                </if>
                <if test="model.placeEndDate != null">
                    and plan.valid_end_date &lt;= #{model.placeEndDate}
                    and plan.valid_end_date >= #{model.currentDate}
                    and plan.place_flag = 1
                    and plan.valid_flag =1
                </if>
                <if test="model.expireEndDate != null">
                    and plan.valid_end_date &lt;= #{model.expireEndDate}
                    and plan.valid_end_date >= #{model.currentDate}
                    and plan.place_flag = 0
                    and plan.valid_flag =1
                </if>
            </otherwise>
        </choose>
        group by
        plan.id,plan.valid_flag,plan.index_value,plan.default_flag,plan.top_flag,plan.place_flag,plan.valid_end_date,plan.custom_sort
        order by plan.valid_flag desc, plan.custom_sort desc, plan.default_flag ,plan.index_value,plan.place_flag
        desc,plan.valid_end_date
    </select>

    <select id="listLockedIndex" resultType="java.lang.String">
        -- 查询存在锁定的顺位
        select distinct plan.index_value
        from operation_promotion_plan plan
        where plan.delete_flag = 0
        and plan.position_id = #{query.pointId}
        <if test="query.platform !=null and query.platform !=823">
            AND (plan.platform = #{query.platform} or plan.platform= '823')
        </if>
        and plan.top_flag = 1
        and plan.valid_flag = 1
        and plan.valid_end_date >= #{query.nowTime}
    </select>
    <select id="listPlaceholderIndex" resultType="java.lang.String">
        -- 查询存在占位的顺位
        select distinct plan.index_value
        from operation_promotion_plan plan
        left join operation_promotion_plan_region region on plan.id = region.plan_id
        where plan.delete_flag = 0
        and plan.position_id = #{query.pointId}
        <if test="query.platform !=null and query.platform !=823">
            AND (plan.platform = #{query.platform} or plan.platform= '823')
        </if>
        and plan.promote_type= #{query.promotionType}
        <if test="query.regionIds != null">
            <if test="query.promotionType != null and query.promotionType == 3">
                and region.region_zone_id in
                <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.promotionType != null and query.promotionType != 3 and query.promotionType != 1 and query.promotionType != 2 ">
                and region.region_id in
                <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        and plan.default_flag = 0
        and plan.valid_flag = 1
        and plan.place_flag = 1
        and plan.valid_end_date >= #{query.nowTime}
    </select>
    <select id="listMaintainIndex" resultType="java.lang.String">
        -- 查询已经维护了的顺位
        select distinct plan.index_value
        from operation_promotion_plan plan
        left join operation_promotion_plan_region region on plan.id = region.plan_id and region.delete_flag = 0
        where plan.delete_flag = 0
        and plan.position_id = #{query.pointId}
        <if test="query.platform !=null and query.platform !=823">
            AND (plan.platform = #{query.platform} or plan.platform= '823')
        </if>
        and plan.promote_type= #{query.promotionType}
        <if test="query.regionIds != null">
            <if test="query.promotionType != null and query.promotionType == 3">
                and region.region_zone_id in
                <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.promotionType != null and query.promotionType != 3 and query.promotionType != 1 and query.promotionType != 2">
                and region.region_id in
                <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        and plan.default_flag = 0
        and plan.valid_flag = 1
        and plan.place_flag = 0
    </select>
    <select id="listUnavailableDate" resultType="com.ly.localactivity.marketing.domain.marketing.vo.PeriodVo">
        -- 查询已使用时间段
        select plan.valid_begin_date AS startTime,
        plan.valid_end_date AS endTime
        from operation_promotion_plan plan
        left join operation_promotion_plan_region region on plan.id = region.plan_id and region.delete_flag = 0
        where plan.delete_flag = 0
        and plan.position_id = #{query.pointId}
        <if test="query.platform !=null and query.platform !=823">
            AND (plan.platform = #{query.platform} or plan.platform= '823')
        </if>
        and plan.valid_flag = 1
        and plan.promote_type= #{query.promotionType}
        and plan.valid_date_type = 2
        and plan.default_flag = 0
        <if test="query.planId != null and query.planId > 0">
            and plan.id != #{query.planId}
        </if>
        and plan.index_value = #{query.indexValue}
        <if test="query.promotionType != null and query.promotionType == 3">
            and region.region_zone_id in
            <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.promotionType != null and query.promotionType != 3 and query.promotionType != 1 and query.promotionType != 2 ">
            and region.region_id in
            <foreach collection="query.regionIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and plan.valid_end_date >= #{query.nowTime}
    </select>

    <select id="listIdsForApi" resultType="java.lang.Long">
        -- 前台查询活动列表listIdsForApi
        select distinct plan.id
        from operation_promotion_plan plan
        left join operation_promotion_plan_region pr on plan.id=pr.plan_id and pr.delete_flag=0
        where plan.delete_flag=0
        <if test="model.positionIds != null and model.positionIds.size()>0">
            and plan.position_id in
            <foreach collection="model.positionIds" item="positionId" index="index" open="(" separator="," close=")">
                #{positionId}
            </foreach>
        </if>
        <if test="model.positionId != null and model.positionId>0">
            and plan.position_id = #{model.positionId}
        </if>
        and(plan.platform=#{model.platform} or plan.platform=823)
        and plan.place_flag =0
        and (plan.valid_date_type=1 or ( #{model.date} >= plan.valid_begin_date and
        #{model.date} &lt;= plan.valid_end_date))
        and plan.valid_flag=1 and
        (pr.region_id = #{model.cityId}
        or pr.region_id=#{model.provinceId}
        <if test="model.areaIds != null and model.areaIds.size>0">
            or pr.region_zone_id in
            <foreach item="item" index="index" collection="model.areaIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="model.areaCoverage != null and model.areaCoverage == 1">
                <!-- 境内 -->
                or plan.promote_type = 1
            </when>
            <when test="model.areaCoverage != null and model.areaCoverage == 2">
                <!-- 境外 -->
                or plan.promote_type = 2
            </when>
        </choose>
        or plan.default_flag = 1
        )

        <choose>
            <when test="model.areaCoverage != null and model.areaCoverage == 1">
                <!-- 境内排除境外 -->
                and plan.promote_type != 2
            </when>
            <when test="model.areaCoverage != null and model.areaCoverage == 2">
                <!-- 境外排除境内 -->
                and plan.promote_type != 1
            </when>
        </choose>
    </select>
    <select id="imageCount" resultType="java.lang.Integer">
        -- 查询图片数量
        select count(1)
        from operation_promotion_plan plan
        left join operation_promotion_plan_content content
        on plan.id = content.plan_id and content.delete_flag = 0
        where plan.delete_flag = 0
        and plan.valid_flag = 1
        and plan.position_id = #{positionId}
        <if test="platform !=null and platform != 823">
            AND (plan.platform = #{platform} or plan.platform= '823')
        </if>
        <if test="id != null ">
            and plan.id != #{id}
        </if>
        and content.image_name=#{imageName}
    </select>
    <select id="listImageByPositionId" resultType="com.ly.localactivity.marketing.domain.marketing.vo.ImageVo">
        -- 查询图片
        select distinct content.image_url  as imageUrl,
                        content.image_name as imageName,
                        content.image_info as imageInfo
        from operation_promotion_plan plan
                 left join operation_promotion_plan_content content
                           on plan.id = content.plan_id and content.delete_flag = 0
        where plan.delete_flag = 0
          and plan.valid_flag = 1
          and plan.place_flag = 0
          and plan.position_id = #{positionId}
    </select>
    <select id="countTimeCross" resultType="java.lang.Integer">
        -- 存在查询时间交叉的数量
        select count(1)
        from operation_promotion_plan plan
        left join operation_promotion_plan_region region on plan.id = region.plan_id and region.delete_flag = 0
        where plan.delete_flag = 0
        and plan.valid_flag = 1
        and plan.position_id = #{positionId}
        <if test="platform !=null and platform !=823">
            AND (plan.platform = #{platform} or plan.platform= '823')
        </if>
        and plan.valid_date_type = 2
        and plan.promote_type = #{promoteType}
        and plan.index_value = #{indexValue}
        and plan.default_flag = 0
        <if test="promoteType != null and promoteType == 3">
            and region.region_zone_id in
            <foreach collection="regionIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="promoteType != null and promoteType != 3 and promoteType != 1 and promoteType != 2 ">
            and region.region_id in
            <foreach collection="regionIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="id != null and id !=0">
            and plan.id != #{id}
        </if>
        and plan.valid_begin_date &lt;= #{validEndDate} and
        plan.valid_end_date>=#{validBeginDate}
    </select>
    <select id="listPlanCoverCondition"
            resultType="com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan">
        -- 查询活动覆盖情况
        SELECT distinct plan.id,top_flag,valid_begin_date,valid_end_date
        FROM operation_promotion_plan plan
        <if test="regionIds != null and regionIds.size>0">
            LEFT JOIN operation_promotion_plan_region region ON plan.id = region.plan_id and region.delete_flag = 0
        </if>
        WHERE plan.valid_flag = TRUE
        <if test="platform !=null and platform !=823">
            AND (plan.platform = #{platform} or plan.platform= '823')
        </if>
        AND position_id = #{pointId}
        AND plan.index_value = #{indexValue}
        AND plan.delete_flag = FALSE
        <if test="planId!= null and planId >0">
            AND plan.id != #{planId}
        </if>
        -- 非境外向下查询的条件，以及排除境外的条件
        <if test="promoteType != null">
            <if test=" promoteType != 2">
                AND (((promote_type > #{promoteType} AND promote_type != 2)
                <if test="regionIds != null and regionIds.size>0">
                    and region.region_id
                    -- 境内排除港澳
                    <if test="promoteType != null and promoteType == 1">
                        not in
                    </if>
                    -- 其他正常查询
                    <if test="promoteType != null and promoteType != 1">
                        in
                    </if>
                    <foreach collection="regionIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                ) OR top_flag = TRUE)
            </if>
            -- 境外 向下查询港澳
            <if test="promoteType == 2">
                AND ((promote_type >= 4
                <if test="regionIds != null and regionIds.size>0">
                    and region.region_id in
                    <foreach collection="regionIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                ) OR top_flag = TRUE)
            </if>
        </if>
        AND default_flag = FALSE
        AND valid_date_type = 2
        AND valid_begin_date &lt;= #{endTime} AND valid_end_date >= #{startTime}

    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(*)
        from operation_promotion_plan
        where position_id = #{positionId}
    </select>
    <select id="listForApi"
            resultType="com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan">
        -- 查询活动列表
        SELECT plan.id, plan.code, plan.name, plan.position_id, plan.position_code, plan.platform, plan.promote_type,
        plan.index_value, plan.valid_date_type, plan.valid_begin_date, plan.valid_end_date, plan.top_flag,
        plan.place_flag, plan.default_show_type, plan.default_flag, plan.reason_type, plan.reason_remark,
        plan.invalid_time, plan.valid_flag, plan.alias, plan.delete_flag, plan.creator, plan.create_time, plan.modifier,
        plan.modifier_no, plan.modified_time
        FROM operation_promotion_plan plan
        left join operation_promotion_plan_region pr on plan.id=pr.plan_id and pr.delete_flag=0
        where plan.delete_flag=0
        <if test="model.positionIds != null and model.positionIds.size()>0">
            and plan.position_id in
            <foreach collection="model.positionIds" item="positionId" index="index" open="(" separator="," close=")">
                #{positionId}
            </foreach>
        </if>
        <if test="model.positionId != null and model.positionId>0">
            and plan.position_id = #{model.positionId}
        </if>
        -- 823 是全部平台
        and(plan.platform=#{model.platform} or plan.platform = 823) and plan.place_flag =0
        and (plan.valid_date_type = 1 or (#{model.date} >= plan.valid_begin_date and #{model.date} &lt;=
        plan.valid_end_date)) and plan.valid_flag=1
        -- 覆盖区域不为境外
        <if test="model.areaCoverage == null or model.areaCoverage != 20">
            and (pr.region_id = #{model.cityId}
            or pr.region_id=#{model.provinceId}
            <if test="model.areaIds != null and model.areaIds.size>0">
                or pr.region_zone_id in
                <foreach item="item" index="index" collection="model.areaIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="model.areaCoverage != null and model.areaCoverage == 1">
                    <!-- 境内 -->
                    or plan.promote_type = 1
                </when>
                <when test="model.areaCoverage != null and model.areaCoverage == 2">
                    <!-- 境外(港澳) -->
                    or plan.promote_type = 2
                </when>
                <when test="model.areaCoverage != null and model.areaCoverage == 20">
                    <!-- 境外 -->
                    or plan.promote_type = 20
                </when>
            </choose>
            or plan.default_flag = 1
            )
        </if>
        -- promote_type 0 包含境外
        <choose>
            <when test="model.areaCoverage != null and model.areaCoverage == 1">
                <!-- 境内排除境外（港澳）和境外 -->
                and (plan.promote_type != 2 and plan.promote_type != 20)
            </when>
            <when test="model.areaCoverage != null and model.areaCoverage == 2">
                <!-- 境外（港澳）排除境内 和 境外 -->
                and (plan.promote_type != 1 and plan.promote_type != 20)
            </when>
            <when test="model.areaCoverage != null and model.areaCoverage == 20">
                <!-- 境外仅取境外 -->
                and plan.promote_type = 20
            </when>
        </choose>
        order by plan.id desc
    </select>
</mapper>
