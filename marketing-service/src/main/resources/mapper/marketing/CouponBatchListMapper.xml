<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchListMapper">
    <resultMap id="couponBatchListVoResultMap"
               type="com.ly.localactivity.marketing.domain.marketing.vo.CouponBatchListVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="status" property="status"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_comment" property="auditComment"/>
        <result column="invalid_reason" property="invalidReason"/>
        <result column="type" property="type"/>
        <result column="modifier" property="modifier"/>
        <result column="modified_time" property="modifiedTime"/>
        <result column="modifier_no" property="modifierNo"/>
        <result column="supplier_id" property="supplierId"/>
    </resultMap>

    <select id="pageSelectCouponList" resultMap="couponBatchListVoResultMap">
        select * from (
        select cbl.id,
               cbl.`name`,
               cbl.`code`,
               cbl.product_category_id,
               cbl.`status`,
               cbl.audit_status,
               cbl.audit_comment,
               cbl.type,
               cbl.supplier_id,
               cbl.invalid_reason,
               cbl.modifier,
               cbl.modified_time,
               event_valid_end_date,
               event_valid_begin_date,
               min(cbd.status) as minStatus,
               max(cbd.status) as maxStatus,
               cbl.modifier_no
        from coupon_batch_list cbl
                 left join coupon_batch_detail cbd on cbl.id = cbd.coupon_batch_id and cbd.delete_flag = 0
                 left join coupon_batch_operation cbo on cbl.id = cbo.coupon_batch_id and cbo.delete_flag = 0
        where cbl.delete_flag = 0
        <if test="request.type != null and request.type > -1">
            and cbl.type = #{request.type}
        </if>
        <if test="request.status != null and request.status > -1">
            and cbl.status = #{request.status}
        </if>
        <if test="request.productCategoryIdList != null and request.productCategoryIdList.size > 0">
            and cbl.product_category_id in
            <foreach collection="request.productCategoryIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.auditStatus != null and request.auditStatus > -1">
            and cbl.audit_status = #{request.auditStatus}
        </if>
        <if test="request.supplierIdList != null and request.supplierIdList.size() > 0">
            and cbl.supplier_id in
            <foreach collection="request.supplierIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.name != null and request.name != ''">
            and cbl.name like concat('%', #{request.name}, '%')
        </if>
        <if test="request.batchNo != null and request.batchNo != ''">
            and cbd.batch_no = #{request.batchNo}
        </if>
        <if test="request.batchName != null and request.batchName != ''">
            and cbd.batch_name like concat('%', #{request.batchName}, '%')
        </if>
        <if test="request.batchCname != null and request.batchCname != ''">
            and cbd.batch_cname like concat('%', #{request.batchCname}, '%')
        </if>
        <if test="request.couponTypeList != null and request.couponTypeList.size() > 0">
            and cbd.coupon_type in
            <foreach collection="request.couponTypeList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="request.eventValidDateStart != null">
            and cbd.event_valid_date_type = 1
            <if test="request.type != null and request.type == 2">
                and cbl.audit_status = 4
            </if>
            and cbd.event_valid_end_date &gt;= #{request.eventValidDateStart}
        </if>
        <if test="request.eventValidDateEnd != null">
            and cbd.event_valid_date_type = 1
            <if test="request.type != null and request.type == 2">
                and cbl.audit_status = 4
            </if>
            and cbd.event_valid_begin_date &lt;= #{request.eventValidDateEnd}
        </if>
        <if test="request.sceneType != null and request.sceneType > -1">
            and cbo.scene_type = #{request.sceneType}
        </if>
        <if test="request.operationPositionCode != null and request.operationPositionCode != ''">
            and cbo.operation_position_code = #{request.operationPositionCode}
        </if>
        group by cbl.id
        order by cbl.id desc
        ) as temp
        <where>
            <if test="request.marketingStatusList != null and request.marketingStatusList.size() > 0">
                <foreach collection="request.marketingStatusList" item="item" open="(" separator="or" close=")">
                    <choose>
                        <when test="item == 1">
                            (temp.status in (1, 2) and (temp.minStatus is null or temp.minStatus &lt;= 1)) -- 审核状态非下架，失效
                        </when>
                        <when test="item == 2">
                            (temp.status = 3 and temp.minStatus = 2 and temp.event_valid_end_date &gt;= #{request.currentDate} and temp.event_valid_begin_date &lt;= #{request.currentDate}) -- 审核状态非下架，失效 并且活动有效期包含当前时间
                        </when>
                        <when test="item == 3">
                            (temp.status = 3 and temp.minStatus = 2 and temp.event_valid_begin_date &gt; #{request.currentDate}) -- 审核状态非下架，失效 并且活动未开始
                        </when>
                        <when test="item == 4">
                            (temp.status = 6 or (temp.status = 3 and #{request.currentDate} &gt; temp.event_valid_end_date and temp.minStatus &lt;= 2)) -- 审核状态非下架，失效 并且活动已结束
                        </when>
                        <when test="item == 5">
                            (temp.status in (4, 5) or (temp.status in (2, 3) and temp.minStatus &gt;= 4 and temp.maxStatus &lt;=6 )) -- 审核状态非下架，失效
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryCouponStatistics"
            resultType="com.ly.localactivity.marketing.domain.marketing.vo.CouponStatisticsVo">
        select countTable.type                                       type,
               count(if(countTable.marketIngStatus = 1, 1, null)) as invalidCount,
               count(if(countTable.marketIngStatus = 2, 1, null)) as activeCount,
               count(if(countTable.marketIngStatus = 3, 1, null)) as notStartCount,
               count(if(countTable.marketIngStatus = 4, 1, null)) as expiredCount,
               count(if(countTable.marketIngStatus = 5, 1, null)) as deListedCount,
               count(if(countTable.auditStatus = 1, 1, null))     as unCommitCount,
               count(if(countTable.auditStatus = 2, 1, null))     as waitAuditCount,
               count(if(countTable.auditStatus = 3, 1, null))     as auditingCount,
               count(if(countTable.auditStatus = 4, 1, null))     as passCount,
               count(if(countTable.auditStatus = 5, 1, null))     as rejectCount
        from (select cbl.id,
                     cbl.status,
                     event_valid_end_date,
                     event_valid_begin_date,
                     min(cbd.status) as minStatus,
                     max(cbd.status) as maxStatus,
                     CASE
                         WHEN NOT FIND_IN_SET(1, GROUP_CONCAT(IF(cbd.status > 1 or cbl.status > 2, 1, 0)))
                             THEN 1 -- 红包状态未生效 或者 批次审批中 都是未生效状态
                         WHEN FIND_IN_SET(1, GROUP_CONCAT(IF(
                                 cbl.status = 3 and cbd.status = 2 and cbd.event_valid_end_date &gt;= #{currentDate} and
                                 cbd.event_valid_begin_date &lt;= #{currentDate}, 1, 0)))
                             THEN 2 -- 红包状态生效中 并且有红包在有效期 则为生效中状态
                         WHEN FIND_IN_SET(1, GROUP_CONCAT(IF(
                                 cbl.status = 3 and cbd.status = 2 and cbd.event_valid_begin_date &gt; #{currentDate},
                                 1, 0))) THEN 3 -- 红包状态生效中 并且有红包未开始 则为未开始状态
                         WHEN cbl.status in (4, 5) or (cbl.status in (2, 3) and min(cbd.status) >= 4)
                             THEN 5 -- 红包状态已下架 或者 批次状态全失效 则为已失效状态
                         WHEN cbl.status = 6 or (cbl.status = 3 and #{currentDate} &gt;
                                                                    max(if(cbd.status in (0, 1, 2), cbd.event_valid_end_date, '2990-01-01')))
                             THEN 4 -- 红包状态已失效 或者 红包状态生效中但都过期 则为已过期状态
                         ELSE 1 END     marketIngStatus, -- 无红包批次 则未生效
                     cbl.audit_status   auditStatus,
                     cbl.type
              from coupon_batch_list cbl
                       left join coupon_batch_detail cbd on cbl.id = cbd.coupon_batch_id and cbd.delete_flag = 0
                       left join coupon_batch_operation cbo on cbl.id = cbo.coupon_batch_id and cbo.delete_flag = 0
        where cbl.delete_flag = 0
        <if test="type != null and type > -1">
            and cbl.type = #{type}
        </if>
        <if test="typeList != null and typeList.size > 0">
            and cbl.type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and status > -1">
            and cbl.status = #{status}
        </if>
        <if test="productCategoryIdList != null and productCategoryIdList.size > 0">
            and cbl.product_category_id in
            <foreach collection="productCategoryIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="auditStatus != null and auditStatus > -1">
            and cbl.audit_status = #{auditStatus}
        </if>
        <if test="supplierIdList != null and supplierIdList.size() > 0">
            and cbl.supplier_id in
            <foreach collection="supplierIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and cbl.name like concat('%', #{name}, '%')
        </if>
        <if test="batchNo != null and batchNo != ''">
            and cbd.batch_no = #{batchNo}
        </if>
        <if test="batchName != null and batchName != ''">
            and cbd.batch_name like concat('%', #{batchName}, '%')
        </if>
        <if test="batchCname != null and batchCname != ''">
            and cbd.batch_cname like concat('%', #{batchCname}, '%')
        </if>
        <if test="couponTypeList != null and couponTypeList.size() > 0">
            and cbd.coupon_type in
            <foreach collection="couponTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eventValidDateStart != null">
            and cbd.event_valid_date_type = 1
            and cbd.event_valid_end_date &gt;= #{eventValidDateStart}
        </if>
        <if test="eventValidDateEnd != null">
            and cbd.event_valid_date_type = 1
            and cbd.event_valid_begin_date &lt;= #{eventValidDateEnd}
        </if>
        <if test="sceneType != null and sceneType > -1">
            and cbo.scene_type = #{sceneType}
        </if>
        <if test="operationPositionCode != null and operationPositionCode != ''">
            and cbo.operation_position_code = #{operationPositionCode}
        </if>
        group by cbl.id
        order by cbl.id) countTable
        <where>
            <if test="marketingStatusList != null and marketingStatusList.size() > 0">
                <foreach collection="marketingStatusList" item="item" open="(" separator="or" close=")">
                    <choose>
                        <when test="item == 1">
                            (countTable.status in (1, 2) and (countTable.minStatus is null or countTable.minStatus &lt;= 1)) -- 审核状态非下架，失效
                        </when>
                        <when test="item == 2">
                            (countTable.status = 3 and countTable.minStatus = 2 and countTable.event_valid_end_date &gt;= #{currentDate} and countTable.event_valid_begin_date &lt;= #{currentDate}) -- 审核状态非下架，失效 并且活动有效期包含当前时间
                        </when>
                        <when test="item == 3">
                            (countTable.status = 3 and countTable.minStatus = 2 and countTable.event_valid_begin_date &gt; #{currentDate}) -- 审核状态非下架，失效 并且活动未开始
                        </when>
                        <when test="item == 4">
                            (countTable.status = 6 or (countTable.status = 3 and #{currentDate} &gt; countTable.event_valid_end_date and countTable.minStatus &lt;= 2)) -- 审核状态非下架，失效 并且活动已结束
                        </when>
                        <when test="item == 5">
                            (countTable.status in (4, 5) or (countTable.status in (2, 3) and countTable.minStatus &gt;= 4 and countTable.maxStatus &lt;=6 )) -- 审核状态非下架，失效
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
            group by countTable.type
    </select>
    <select id="selectCouponList" resultMap="couponBatchListVoResultMap">
        select cbl.id,
               cbl.`name`,
               cbl.`code`,
               cbl.product_category_id,
               cbl.`status`,
               cbl.audit_status,
               cbl.audit_comment,
               cbl.type,
               cbl.supplier_id,
               cbl.invalid_reason,
               cbl.modifier,
               cbl.modified_time,
               cbl.modifier_no
        from coupon_batch_list cbl
                 left join coupon_batch_detail cbd on cbl.id = cbd.coupon_batch_id and cbd.delete_flag = 0
                 left join coupon_batch_operation cbo on cbl.id = cbo.coupon_batch_id and cbo.delete_flag = 0
        where cbl.delete_flag = 0
        <if test="request.type != null and request.type > -1">
            and cbl.type = #{request.type}
        </if>
        <if test="request.status != null and request.status > -1">
            and cbl.status = #{request.status}
        </if>
        <if test="request.productCategoryIdList != null and request.productCategoryIdList.size > 0">
            and cbl.product_category_id in
            <foreach collection="request.productCategoryIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.auditStatus != null and request.auditStatus > -1">
            and cbl.audit_status = #{request.auditStatus}
        </if>
        <if test="request.supplierIdList != null and request.supplierIdList.size() > 0">
            and cbl.supplier_id in
            <foreach collection="request.supplierIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.name != null and request.name != ''">
            and cbl.name like concat('%', #{request.name}, '%')
        </if>
        <if test="request.batchNo != null and request.batchNo != ''">
            and cbd.batch_no = #{request.batchNo}
        </if>
        <if test="request.batchName != null and request.batchName != ''">
            and cbd.batch_name like concat('%', #{request.batchName}, '%')
        </if>
        <if test="request.batchCname != null and request.batchCname != ''">
            and cbd.batch_cname like concat('%', #{request.batchCname}, '%')
        </if>
        <if test="request.couponTypeList != null and request.couponTypeList.size() > 0">
            and cbd.coupon_type in
            <foreach collection="request.couponTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.eventValidDateStart != null">
            and cbd.event_valid_date_type = 1
            <if test="request.type != null and request.type == 2">
                and cbl.audit_status = 4
            </if>
            and cbd.event_valid_end_date &gt;= #{request.eventValidDateStart}
        </if>
        <if test="request.eventValidDateEnd != null">
            and cbd.event_valid_date_type = 1
            <if test="request.type != null and request.type == 2">
                and cbl.audit_status = 4
            </if>
            and cbd.event_valid_begin_date &lt;= #{request.eventValidDateEnd}
        </if>
        <if test="request.sceneType != null and request.sceneType > -1">
            and cbo.scene_type = #{request.sceneType}
        </if>
        <if test="request.operationPositionCode != null and request.operationPositionCode != ''">
            and cbo.operation_position_code = #{request.operationPositionCode}
        </if>
        <if test="request.marketingStatus != null">
            <choose>
                <when test="request.marketingStatus == 2">
                    and (cbl.status = 3 and cbd.status = 2 and cbd.event_valid_end_date &gt;= #{request.currentDate} and
                         cbd.event_valid_begin_date &lt;= #{request.currentDate}) -- 审核状态非下架，失效 并且活动有效期包含当前时间
                </when>
                <when test="request.marketingStatus == 3">
                    and (cbl.status = 3 and cbd.status = 2 and cbd.event_valid_begin_date &gt; #{request.currentDate}) -- 审核状态非下架，失效 并且活动未开始
                </when>
            </choose>
        </if>
        group by cbl.id
        having 1 = 1
        <if test="request.marketingStatus != null">
            <choose>
                <when test="request.marketingStatus == 1">
                    and (cbl.status in (1, 2) and (min(cbd.status) is null or min(cbd.status) &lt;= 1)) -- 审核状态非下架，失效
                </when>
                <when test="request.marketingStatus == 4">
                    and (cbl.status =  6 or (cbl.status = 3  and #{request.currentDate} &gt; max(if(cbd.status in (0,1,2), cbd.event_valid_end_date, '2990-01-01')))) -- 审核状态非下架，失效 并且活动已结束
                </when>
                <when test="request.marketingStatus == 5">
                    and (cbl.status in (4, 5) or (cbl.status in (2, 3) and min(cbd.status) &gt;= 4 and max(cbd.status) &lt;=6 ) ) -- 审核状态非下架，失效
                </when>
            </choose>
        </if>
        order by cbl.id desc
    </select>
</mapper>
