<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.marketing.CouponBatchOperationChannelMapper">

    <select id="listRefidByBatchNo" resultType="java.lang.String">
        -- 根据红包批次号查询有效的渠道
        select cboc.refid
        from coupon_batch_operation_channel cboc
                 inner join coupon_batch_detail cbd on cboc.coupon_batch_id = cbd.coupon_batch_id and cbd.delete_flag=0
        where cboc.delete_flag=0 and cbd.batch_no=#{batchNo}
    </select>
</mapper>
