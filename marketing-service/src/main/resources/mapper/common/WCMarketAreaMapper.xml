<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.common.WCMarketAreaMapper">
    <select id="getAreaIdByCityIdAndType" resultType="java.lang.Long">
        select area.MAId
        from WCMarketAreaCity city
                 inner join WCMarketArea area on city.MACMarketAreaId=area.MAId
        where city.MACCityId=#{cityId} and city.MACDataFlag=1 and area.MADataFlag=1
        and area.MAType = #{type} limit 1;
    </select>
</mapper>
