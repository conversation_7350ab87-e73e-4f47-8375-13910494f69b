<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.localactivity.marketing.mapper.resource.WRMainResourceMapper">
    <select id="getCanSaleProductByMainResourceSerialIds"
            resultType="com.ly.localactivity.marketing.domain.resource.WRMainResource">
        select
            MRId,MRTitle,MRSubTitle,MRSerialId,MRSupplierId
        from WRMainResource mr
        inner join WRMainResourceDetail mrd on mr.MRId = mrd.MRDMainResourceId
        where mr.MRAuditStatus = 2 and mr.MRDataFlag = 1 and mr.MRDelete = 0
          and mrd.MRDDataFlag = 1 and mrd.MRDIsCanAdvance = 1
          and mr.MRSerialId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getCanSaleProductBySupplierId"
            resultType="com.ly.localactivity.marketing.domain.resource.WRMainResource">
        select distinct mr.MRId,
                        mr.MRTitle,
                        mr.MRSubTitle,
                        mr.MRSerialId
        from WRMainResource mr
                 inner join WRMainResourceDetail mrd on mr.MRId = mrd.MRDMainResourceId
        where mr.MRAuditStatus = 2
          and mr.MRDataFlag = 1
          and mr.MRDelete = 0
          and mrd.MRDDataFlag = 1
          and mrd.MRDIsCanAdvance = 1
          and mr.MRSupplierId = #{supplierId}
          <if test="firstCategoryId != null">
              and mr.MRFirstCategoryId = #{firstCategoryId}
          </if>
    </select>

</mapper>
