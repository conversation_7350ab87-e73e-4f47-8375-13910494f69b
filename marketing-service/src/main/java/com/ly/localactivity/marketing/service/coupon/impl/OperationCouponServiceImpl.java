package com.ly.localactivity.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.mq.TurboMqUtils;
import com.ly.localactivity.framework.utils.SignUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.CouponConst;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPositionCouponDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchOperationChannelMapper;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponSearchService;
import com.ly.localactivity.marketing.service.coupon.IOperationCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationCouponServiceImpl
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
@Slf4j
@Service
public class OperationCouponServiceImpl implements IOperationCouponService {

    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private CouponBatchOperationChannelMapper couponBatchOperationChannelMapper;
    @Resource
    private TurboMqUtils turboMqUtils;
    @Resource
    private ICouponSearchService couponSearchService;

    @Override
    public void sendCoupon(OperationPositionCouponDto positionCouponDto) {
        SkyLogUtils.infoByMethod(log, "运营点位红包发放:" + JSON.toJSONString(positionCouponDto), "", "");
        // TODO 运营点位红包发放
        if (positionCouponDto == null
                || StringUtils.isBlank(positionCouponDto.getPositionCode())
                || StringUtils.isBlank(positionCouponDto.getPlatform())) {
            return;
        }


        //找到运营点位对应的红包
//        List<String> batchList = couponBatchOperationMapper.listBatchNoByOperationCode(LocalDateTime.now()
//                , positionCouponDto.getPlatform()
//                , positionCouponDto.getPositionCode());

//        List<String> batchList = couponSearchService.searchEsBatchNo("" ,positionCouponDto.getPlatform()
//                , CouponLimitRo.builder()
//                        .positionCode(positionCouponDto.getPositionCode())
//                        .refid(positionCouponDto.getRefid())
//                        .build());
//
//        SkyLogUtils.infoByMethod(log, "运营点位红包发放:找到批次号：" + StringUtils.join(",", batchList), positionCouponDto.getPositionCode(), "");
//        //找到对应红包批次号
//        if (batchList == null || batchList.isEmpty()) {
//            SkyLogUtils.warnByMethod(log, "运营点位红包发放:未找到对应红包批次号", positionCouponDto.getPositionCode(), "");
//            return;
//        }
//        //开始发放
//        for (String batchNo : batchList) {
//            sendCouponBatch(positionCouponDto, batchNo);
//        }
    }

    private boolean checkRefid(List<String> batchRefids,String refid) {
        if (CollectionUtil.isEmpty(batchRefids)) {
            //如果没有refid限制，直接返回true
            return true;
        }
        if (StringUtils.isBlank(refid)) {
            //如果有refid限制，但是refid为空，直接返回false
            return false;
        }
        if (batchRefids.contains(refid)) {
            //如果有refid限制，且refid在限制列表中，返回true
            return true;
        }
        return false;
    }

    private void sendCouponBatch(OperationPositionCouponDto positionCouponDto, String batchNo) {
        CouponBatchCacheDto batchDetail = couponQueryService.getBatchDetail(batchNo, false);
        if (batchDetail == null){
            SkyLogUtils.warnByMethod(log, "运营点位红包发放:未找到对应红包批次号", positionCouponDto.getPositionCode(), batchNo);
            return;
        }

        //检查refid是否满足
        List<String> refidList = getRefid(batchNo);
        SkyLogUtils.infoByMethod(log, "运营点位红包发放:refid列表:" + StringUtils.join(",", refidList), positionCouponDto.getPositionCode(), batchNo);

        if (!checkRefid(refidList,positionCouponDto.getRefid())) {
            SkyLogUtils.warnByMethod(log, "运营点位红包发放:refid不满足", positionCouponDto.getPositionCode(), batchNo);
            return;
        }
        //发放优惠券
        CouponRechargeDto rechargeDto = CouponRechargeDto.builder()
                .batchNo(CollectionUtil.newArrayList(batchNo))
                .refId(positionCouponDto.getRefid())
                .from("la-positionRecharge")
                .platform(positionCouponDto.getPlatform())
                .memberId(positionCouponDto.getMemberId())
                .openId(positionCouponDto.getOpenId())
                .unionId(positionCouponDto.getUnionId())
                .deviceId(positionCouponDto.getDeviceId())
                .sendTime(LocalDateTime.now())
                .tradeNo(IdUtil.fastSimpleUUID())
                .build();
        String sign = SignUtils.getSign(CouponConst.SIGN_KEY, (JSONObject) JSON.toJSON(rechargeDto));
        rechargeDto.setSign(sign);
        turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, JSON.toJSONString(rechargeDto));
        SkyLogUtils.infoByMethod(log, "运营点位红包发放:发放成功:" + JSON.toJSONString(rechargeDto), positionCouponDto.getPositionCode(), batchNo);
    }

    private List<String> getRefid(String batchNo) {
        return couponBatchOperationChannelMapper.listRefidByBatchNo(batchNo);
    }
}
