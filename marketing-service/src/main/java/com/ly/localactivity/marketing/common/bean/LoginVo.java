package com.ly.localactivity.marketing.common.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LoginVo
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginVo {
    private String token;
    @JSONField(name = "refresh_token")
    private String refreshToken;
}
