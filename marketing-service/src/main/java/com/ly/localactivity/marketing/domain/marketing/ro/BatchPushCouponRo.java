package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description BatchPushCouponRo
 * @date 2024-05-11
 */
@Data
@ApiModel(value = "BatchPushCouponRo", description = "批量推送红包")
public class BatchPushCouponRo {
    @ApiModelProperty(value = "红包批次ids",required = false)
    private List<Long> couponBatchIds;

    /**
     * 红包批次状态
     */
    private List<Integer> couponBatchDetailStatusList;

    /**
     * 红包状态
     */
    private List<Integer> couponBatchListStatusList;
}
