package com.ly.localactivity.marketing.service.coupon;

import com.ly.localactivity.marketing.application.model.dto.CouponBatchQueryDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsSearchDto;

import java.util.List;

/**
 * ICouponSearchService
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
public interface ICouponSearchService {

    /**
     * 搜索es红包批次，返回红包批次号
     *
     * @param requestId 请求id
     * @param platform  站台
     * @return {@link List}<{@link String}>
     */
    List<String> searchEsByPlatform(String requestId, String platform);

    /**
     * 搜索es红包批次，返回红包批次对象
     *
     * @param requestId 请求id
     * @param platform  站台
     * @return {@link List}<{@link String}>
     */
    List<CouponEsIndexDto> searchEsBatchListByPlatform(String requestId, String platform);

    /**
     * 根据批号搜索es红包批次信息
     *
     * @param batchNos 批号
     * @return {@link List}<{@link CouponEsIndexDto}>
     */
    List<CouponEsIndexDto> searchEsByBatchNos(List<String> batchNos);

    List<CouponEsIndexDto> searchEsBatchList(CouponBatchQueryDto queryDto);

    List<CouponEsIndexDto> searchEsByLimit(CouponEsSearchDto searchDto);

}
