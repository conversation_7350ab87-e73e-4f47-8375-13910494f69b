package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CouponBaseRequest
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponBaseRequest", description = "优惠券基础请求")
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CouponBaseRequest {
    /**
     * 微信unionId:1
     * 0体系memberId:2
     * cardNo:3
     * 非0体系的memberId:4
     * 支付宝unionId:5
     */
    @ApiModelProperty(value = "源", required = true)
    @NotNull(message = "源不能为空")
    private Integer source;
    /**
     * source=1传微信unionID
     * <p>
     * source=2传同程0体系memberID
     * <p>
     * source=3传艺龙卡号cardno（0体系）
     * <p>
     * source=4传非0体系memberid，例如：国际站（不走同程绑定关系，）
     * <p>
     * source=5传支付宝unionId（不支持礼包和酒店红包充值）
     */
    @ApiModelProperty(value = "用户标识", required = true)
    @NotBlank(message = "用户标识不能为空")
    @NotNull(message = "用户标识不能为空")
    private String userkey;
    /**
     * openid
     */
    @ApiModelProperty(value = "微信OpenId,微信渠道必填")
    private String openId;
    /**
     * 当前时区时间，格式：yyyy-MM-dd HH:mm:ss
     * (yyyy-MM-dd HH:mm:ss)当前时区时间（国际站必传）
     * <p>
     * 这个入参会关联红包有效期，按照业务方传进来的时间和时区，往后推，以业务方的时区时间为准。如果不传，那么有效期会以北京时间为准
     */
    @ApiModelProperty(value = "当前时区时间，格式：yyyy-MM-dd HH:mm:ss")
    private String currentTime;

    private String requestId;

    /**
     * 使用券项目
     */
    @ApiModelProperty(value = "使用券项目", required = true)
    @NotNull(message = "使用券项目不能为空")
    private Integer projectId;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private List<Integer> projectIds;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道", required = true)
    @NotNull(message = "渠道不能为空")
    private Integer channel;
}
