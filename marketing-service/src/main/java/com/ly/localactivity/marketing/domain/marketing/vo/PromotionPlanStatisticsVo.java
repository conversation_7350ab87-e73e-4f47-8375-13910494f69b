package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PromotionPlanStatisticsVo
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@ApiModel(value = "PromotionPlanStatisticsVo", description = "推广计划统计响应参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromotionPlanStatisticsVo {
    /**
     * 总数
     */
    @ApiModelProperty(value = "总数", required = true)
    private Integer total;
    /**
     * 占位数
     */
    @ApiModelProperty(value = "占位数", required = true)
    private Integer placeCount;
    /**
     * 占位未来两天到期数
     */
    @ApiModelProperty(value = "占位未来两天到期数", required = true)
    private Integer placeExpireCount;
    /**
     * 推广计划未来7天到期数
     */
    @ApiModelProperty(value = "推广计划未来7天到期数", required = true)
    private Integer planExpireCount;
}
