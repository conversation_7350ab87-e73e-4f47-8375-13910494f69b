package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.marketing.application.model.vo.CouponExportVo;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchListDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRelationResourceRuleDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.CheckCouponBatchVo;
import com.ly.localactivity.marketing.domain.marketing.vo.CouponBatchListVo;
import com.ly.localactivity.marketing.domain.marketing.vo.CouponSimpleInfoVo;
import com.ly.localactivity.marketing.domain.marketing.vo.CouponStatisticsVo;
import com.ly.localactivity.marketing.domain.resource.vo.ProductOptionVo;

import java.util.List;

/**
 * <p>
 * 优惠券列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchListService extends IService<CouponBatchList> {

    /**
     * 分页查询优惠券列表
     *
     * @param request
     * @return
     */
    CommonPage<CouponBatchListVo> pageQueryCouponBatchList(QueryCouponBatchListRo request);

    /**
     * 查询红包统计数据
     *
     * @param request
     * @return
     */
    List<CouponStatisticsVo> queryCouponStatistics(QueryCouponBatchListRo request);

    /**
     * 分页查询优惠券批次明细列表
     *
     * @param request
     * @return
     */
    CommonPage<CouponBatchDetailDto> pageQueryCouponBatchDetailList(QueryCouponBatchDetailListRo request);

    /**
     * 查询优惠券批次明细列表
     *
     * @param request
     * @return
     */
    CouponBatchListDto queryCouponBatchDetail(CouponBatchQueryRo request);

    /**
     * 查询优惠券简单信息
     *
     * @param couponBatchId 优惠券批次id
     * @return {@link CouponSimpleInfoVo}
     */
    CouponSimpleInfoVo queryCouponSimpleInfo(Long couponBatchId);

    /**
     * 更改优惠券审核状态
     *
     * @param request    请求
     * @return {@link Boolean}
     */
    Boolean changeCouponAuditStatus(ChangeCouponAuditStatusRo request);

    /**
     * 保存优惠券批次
     *
     * @param request
     * @return
     */
    Long saveCouponBatch(CouponBatchListDto request);

    /**
     * 查询优惠券批次规则
     *
     * @param request
     * @return
     */
    CouponBatchListDto queryCouponBatchRule(CouponBatchQueryRo request);

    /**
     * 查询优惠券批次操作
     *
     * @param request
     * @return
     */
    CouponBatchListDto queryCouponBatchOperation(CouponBatchQueryRo request);

    /**
     * 更改优惠券批次状态
     *
     * @param request
     * @return
     */
    Boolean changeStatus(CouponChangeStatusRo request);

    /**
     * 查询已绑定其他优惠券的refId列表
     * @param request
     * @return
     */
    List<String> queryHasBindOtherCouponRefIdList(QueryHasBindOtherCouponRefIdListRo request);

    /**
     * 检查红包批次是否能被正常使用
     * @param request
     * @return
     */
    List<CheckCouponBatchVo> checkCouponBatchList(CheckCouponBatchRo request);

    /**
     * 审核红包
     * @param request
     * @return
     */
    Boolean auditCouponBatch(AuditCouponBatchRo request);


    /**
     * 查询优惠券批量相关产品
     *
     * @param request 请求
     * @return {@link List}<{@link ProductOptionVo}>
     */
    List<ProductOptionVo> queryCouponBatchRelatedProductList(CouponRelatedProductRo request);

    /**
     * 删除优惠券批次列表
     *
     * @param request 请求
     * @return {@link Boolean}
     */
    Boolean deleteCouponBatchList(CouponBatchQueryRo request);

    /**
     * 刷新红包状态
     *
     * @param couponBatchIdList 优惠券批次id
     * @return {@link Boolean}
     */
    Boolean refreshCouponBatchListStatus(List<Long> couponBatchIdList);

    /**
     * 查询鲲鹏红包批次详情
     *
     * @param request 请求
     * @return {@link CouponBatchDetailDto}
     */
    List<CouponBatchDetailDto> queryKunPengCouponBatchDetail(KunPengCouponBatchDetailListRo request);

    /**
     * 查询鲲鹏红包批次详情并更新数据库
     *
     * @param request 请求
     * @return {@link CouponBatchDetailDto}
     */
    List<CouponBatchDetailDto> queryKunPengCouponBatchDetailAndUpdateDb(KunPengCouponBatchDetailListRo request);

    /**
     * 保存优惠券批次列表到数据库
     * @param couponBatchList
     * @return
     */
    CouponBatchList saveCouponBatchList(CouponBatchList couponBatchList);

    /**
     * 推送鲲鹏红包创建并提交审核
     * @param kunPengCouponApply
     * @return
     */
    Boolean pushKunPengCouponApplyAndApprove(CouponBatchDetailDto kunPengCouponApply);

    /**
     * 查询鲲鹏红包并刷新本地数据
     * @param request
     * @return
     */
    Boolean refreshWaitingAuditCouponBatchDetail(BatchPushCouponRo request);

    /**
     * 控制红包状态
     * @param couponBatchId
     * @param status
     */
    Boolean controlBatchList(String couponBatchId, Integer status);

    /**
     * 查询红包导出列表
     * @param request
     * @return
     */
    List<CouponExportVo> exportCouponBatchList(QueryCouponBatchListRo request);

    /**
     * 获取所有优惠券批量关系资源规则dto
     *
     * @return {@link CouponBatchRelationResourceRuleDto }
     */
    List<CouponBatchRelationResourceRuleDto> getAllCouponBatchRelationResourceRuleDto();
}
