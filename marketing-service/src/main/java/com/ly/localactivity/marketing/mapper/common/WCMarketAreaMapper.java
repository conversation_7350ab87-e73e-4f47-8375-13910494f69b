package com.ly.localactivity.marketing.mapper.common;

import com.ly.localactivity.marketing.domain.common.WCMarketArea;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 营销区域表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface WCMarketAreaMapper extends BaseMapper<WCMarketArea> {
    /**
     * 按城市id和区域类型查询所有区域id
     *
     * @param cityId 城市id
     * @param type   区域类型
     * @return {@link List}<{@link Long}>
     */
    Long getAreaIdByCityIdAndType(@Param("cityId") Long cityId, @Param("type") Integer type);
}
