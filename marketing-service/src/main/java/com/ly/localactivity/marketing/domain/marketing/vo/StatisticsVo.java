package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * StatisticsVo
 *
 * <AUTHOR>
 * @date 2024/2/5
 */
@ApiModel(value = "StatisticsVo", description = "统计信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StatisticsVo {
    /**
     * 频道页数量
     */
    @ApiModelProperty(value = "频道页数量")
    private Integer operationPositionPageCount;
    /**
     * 运营点位(推广创意)数量
     */
    @ApiModelProperty(value = "运营点位(推广创意)数量")
    private Integer operationPositionCount;
    /**
     * “必填”未维护数量
     */
    @ApiModelProperty(value = "【必填】运营点位未维护数量")
    private Integer requiredNotMaintainedCount;
    /**
     * 未来七天到期数量
     */
    @ApiModelProperty(value = "未来七天到期数量")
    private Integer expireCount;

}
