package com.ly.localactivity.marketing.domain.marketing.dto;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CouponRechargeDto
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRechargeDto {
    /**
     * 来源
     */
    private String from;
    /**
     * 赠送/预发放记录订单号
     */
    private String orderNo;
    /**
     * 平台，852，433，434
     */
    private String platform;

    /**
     * 批号
     */
    private List<String> batchNo;

    /**
     * refid
     */
    private String refId;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 是否礼包
     */
    private Boolean isPackage;

    /**
     * 发送时间，生效时间
     */
    private LocalDateTime sendTime;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 签名
     */
    private String sign;

    @Override
    public String toString(){
        return "CouponRechargeDto{" +
                "from='" + from + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", platform='" + platform + '\'' +
                ", batchNo=" + batchNo +
                ", refId='" + refId + '\'' +
                ", memberId=" + memberId +
                ", openId='" + openId + '\'' +
                ", unionId='" + unionId + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", isPackage=" + isPackage +
                '}';
    }

    @JSONField(serialize = false)
    public String getCacheKey() {
        return SecureUtil.md5(this.toString());
    }
}
