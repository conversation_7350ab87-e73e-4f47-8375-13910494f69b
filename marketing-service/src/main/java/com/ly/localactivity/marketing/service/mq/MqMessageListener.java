package com.ly.localactivity.marketing.service.mq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.mq.LAMqListener;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;
import com.ly.localactivity.marketing.service.coupon.ICouponRechargeService;
import com.ly.localactivity.marketing.service.coupon.IOperationCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;

/**
 * mq消息侦听器
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@ConditionalOnProperty(prefix = "local-activity", name = "appName", havingValue = "tcscenery.java.localactivity.marketing.api")
@Service
@Slf4j
public class MqMessageListener {
    @Resource
    private ICouponRechargeService couponRechargeService;
    @Resource
    private IOperationCouponService operationCouponService;

    /**
     * 红包充值MQ
     */
    @LAMqListener(topic = MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, consumerGroup = MQConst.GROUP_LA_MARKETING_DEFAULT)
    public void listenCouponRecharge(MessageExt message) {
        try {
            CouponRechargeDto couponRechargeDto = formatMsg(message, CouponRechargeDto.class);
            couponRechargeService.recharge(couponRechargeDto, true);
        } catch (ApiException ex) {
            SkyLogUtils.errorByMethod(log, MessageFormat.format("红包充值失败(ApiException)，消息内容:{0}，异常内容{1}", message, ex.getMessage()), MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, "", ex);
        } catch (Exception ex) {
            SkyLogUtils.errorByMethod(log, MessageFormat.format("红包充值失败，处理异常，消息内容:{0}，异常内容{1}", message, ex.getMessage()), MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, "", ex);
            throw ex;
        }
    }

    /**
     * 运营点位红包发放
     *
     * @param messageExt 信息ext
     */
    @LAMqListener(topic = MQConst.TOPIC_LA_MARKETING_POSITION_COUPON, consumerGroup = MQConst.GROUP_LA_MARKETING_DEFAULT)
    public void listenPositionCoupon(MessageExt messageExt){
        try {
//            OperationPositionCouponDto rechargeDto = formatMsg(messageExt, OperationPositionCouponDto.class);
//            operationCouponService.sendCoupon(rechargeDto);
        } catch (Exception ex) {
            SkyLogUtils.errorByMethod(log, MessageFormat.format("运营点位红包发放失败，处理异常，消息内容:{0}，异常内容{1}", messageExt, ex.getMessage()), MQConst.TOPIC_LA_MARKETING_POSITION_COUPON, "", ex);
            throw ex;
        }
    }

    /**
     * 格式化消息
     *
     * @param message
     * @return
     */
    public static <T> T formatMsg(MessageExt message, Class<T> clazz) {
        if (message == null) {
            throw new ApiException("MQ消息为空");
        }
        try {
            return JSON.parseObject(new String(message.getBody(), StandardCharsets.UTF_8), clazz);
        } catch (Exception ex) {
            throw new ApiException("MQ消息格式化错误");
        }
    }
}
