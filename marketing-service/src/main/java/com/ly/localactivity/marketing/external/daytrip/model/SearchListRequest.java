package com.ly.localactivity.marketing.external.daytrip.model;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * SearchListRequest
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
@Builder
public class SearchListRequest {
    /**
     * 调用来源
     */
    @NotNull(message = "caller不能为空")
    @NotBlank(message = "caller不能为空")
    private String caller;

    /**
     * 当前页数
     */
    private Integer pageIndex;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 产品编号ID，多个用逗号隔开
     */
    private String seriaIds;

    /**
     * 一级分类ID，多个用逗号隔开
     */
    private String categoryIds;

    /**
     * 二级分类ID，多个用逗号隔开
     */
    private String sCategoryIds;

    /**
     * 点评分范围，4.5-5.0
     */
    private String scoreRange;

    /**
     * 同程最小售价,100-150
     */
    private String sellPriceRange;

    /**
     * 同程价,100-150
     */
    private String tcPriceRange;

    /**
     * 是否退订无忧，1-是，0-否
     */
    private Integer isCancancel;

    /**
     * 是否立即确认，1-是，0-否
     */
    private Integer isConfirm;

    /**
     * 是否测试产品	1-是，0-否
     */
    private Integer isTest;

    /**
     * 可售开始日期，20230310
     */
    private Integer sSaleDay;

    /**
     * 可售结束日期,20230313
     */
    private Integer eSaleDay;

    /**
     * 区县ID，多个用逗号隔开
     */
    private String countyIds;

    /**
     * 城市ID，多个用逗号隔开
     */
    private String cityIds;

    /**
     * 目的城市ID	多个用逗号隔开
     */
    private String destCityIds;

    /**
     * 省份id,多个用逗号隔开
     */
    private String provinceIds;

    /**
     * 国家id,多个用逗号隔开
     */
    private String countryIds;

    /**
     * 洲ID,多个用逗号隔开
     */
    private String continetIds;

    /**
     * 景区ID,多个用逗号隔开
     */
    private String poiIds;

    /**
     * 等级ID,多个用逗号隔开
     */
    private String levelIds;

    /**
     * 标签ID,多个用逗号隔开
     */
    private String labelIds;

    /**
     * 主题id,多个用逗号隔开
     */
    private String themeIds;

    /**
     * 主题名称
     */
    private String themeNames;

    /**
     * 排序类型,price:asc
     * price:价格排序维度
     * score:点评排序维度
     * hasPreferred:是否优品
     * hotSale:是否热销
     * asc:升序
     * desc:降序
     */
    private String sortType;

    /**
     * 平台ID
     * 5:小程序
     * 1:APP
     */
    private Integer platForm;

    /**
     * 供应商id
     */
    private Long supplierId;

    private String section;
}
