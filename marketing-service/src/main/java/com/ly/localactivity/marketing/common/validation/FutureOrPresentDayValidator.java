package com.ly.localactivity.marketing.common.validation;

import com.ly.localactivity.marketing.common.validation.annotation.FutureOrPresentDay;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * FutureOrPresentDayValidator
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
public class FutureOrPresentDayValidator implements ConstraintValidator<FutureOrPresentDay, LocalDateTime> {
    @Override
    public void initialize(FutureOrPresentDay futureOrPresentDay) {
    }

    @Override
    public boolean isValid(LocalDateTime localDateTime, ConstraintValidatorContext constraintValidatorContext) {
        return localDateTime != null && !localDateTime.toLocalDate().isBefore(LocalDate.now());
    }
}
