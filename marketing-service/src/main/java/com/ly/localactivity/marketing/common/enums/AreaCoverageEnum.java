package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AreaCoverageEnum
 *
 * <AUTHOR>
 * @date 2024/2/6
 */
@AllArgsConstructor
@Getter
public enum AreaCoverageEnum {
    /**
     * 1-境内(全国)
     */
    DOMESTIC("1", "境内(全国)"),
    /**
     * 2-境外(港澳台)
     */
    HK_MACAO("2", "境外(港澳台)"),
    /**
     * 3-境外
     */
    FOREIGN("20", "境外(不含港澳台)"),
    ;
    private final String code;
    private final String name;

    public static String getName(String code) {
        for (AreaCoverageEnum c : AreaCoverageEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return null;
    }
}
