package com.ly.localactivity.marketing.common.validation;

import com.ly.localactivity.marketing.common.validation.annotation.FutureDay;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * FutureDayValidator
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
public class FutureDayValidator implements ConstraintValidator<FutureDay, LocalDateTime> {

    @Override
    public void initialize(FutureDay constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(LocalDateTime localDateTime, ConstraintValidatorContext constraintValidatorContext) {
        return localDateTime != null && localDateTime.toLocalDate().isAfter(LocalDate.now());
    }
}
