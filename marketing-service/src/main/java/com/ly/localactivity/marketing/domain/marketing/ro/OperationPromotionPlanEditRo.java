package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanChannelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanEditRo
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@ApiModel(value = "OperationPromotionPlanEditRo", description = "推广计划编辑对象")
@Data
public class OperationPromotionPlanEditRo {
    @ApiModelProperty(value = "推广地区ids(按照推广地区分开维护则为一条数据)", required = true)
    @NotEmpty(message = "推广地区ids不能为空")
    private List<Long> regionIds;
    @ApiModelProperty(value = "推广方式,数据字典 Code:promotion_plan_promote_type", required = true)
    @NotNull(message = "推广方式不能为空")
    private Integer promoteType;
    @ApiModelProperty(value = "推广计划编辑对象详情", required = true)
    @NotEmpty(message = "推广计划编辑对象详情不能为空")
    @Valid
    private List<OperationPromotionPlanEditDetailRo> details;

    @Data
    @ApiModel(value = "OperationPromotionPlanEditDetailRo", description = "推广计划编辑对象详情")
    public static class OperationPromotionPlanEditDetailRo {
        @ApiModelProperty(value = "顺位", required = true)
        @NotNull(message = "顺位不能为空")
        private Integer indexValue;
        @ApiModelProperty(value = "推广计划编辑对象详情按照index分组", required = true)
        @NotEmpty(message = "推广计划编辑对象详情按照index分组不能为空")
        @Valid
        private List<DetailGroupByIndexRo> detailsGroupByIndex;

        @ApiModel(value = "DetailGroupByIndexRo", description = "推广计划编辑对象详情按照index分组")
        @Data
        public static class DetailGroupByIndexRo {
            @ApiModelProperty(value = "id")
            private Long id;
            @ApiModelProperty(value = "推广计划名称", required = true)
            private String name;
            @ApiModelProperty(value = "自定义名称")
            private String alias;
            @ApiModelProperty(value = "是否占位", required = true)
            @NotNull(message = "是否占位不能为空")
            private Boolean placeFlag;
            @ApiModelProperty(value = "有效期类型，1-长期有效，2-固定日期段", required = true)
            @NotNull(message = "有效期类型不能为空")
            private Integer validDateType;
            @ApiModelProperty(value = "有效开始日期")
            private LocalDateTime validBeginDate;
            @ApiModelProperty(value = "有效结束日期")
            private LocalDateTime validEndDate;
            @ApiModelProperty(value = "是否置顶/锁定", required = true)
            @NotNull(message = "是否置顶/锁定不能为空")
            private Boolean topFlag;
            @ApiModelProperty(value = "是否兜底", required = true)
            @NotNull(message = "是否兜底不能为空")
            private Boolean defaultFlag;
            @ApiModelProperty(value = "默认展示展示方式，1-最后顺位，2-兜底展示", required = true)
            @NotNull(message = "默认展示展示方式不能为空")
            @Range(min = 1, max = 2, message = "默认展示展示方式只能为1或2")
            private Integer defaultShowType;
            @ApiModelProperty(value = "推广原因 数据字典:promotion_reason")
            private String reasonType;
            @ApiModelProperty(value = "推广原因备注")
            private String reasonRemark;
            @ApiModelProperty(value = "有效标记")
            private Boolean validFlag;
            @ApiModelProperty(value = "图片地址")
            private String imageUrl;
            @ApiModelProperty(value = "图片名称")
            private String imageName;
            @ApiModelProperty(value = "图片上传信息")
            private String imageInfo;
            @ApiModelProperty(value = "关键字(推广标题)")
            private String keyword;
            @ApiModelProperty(value = "副标题")
            private String subTitle;

            @ApiModelProperty(value = "是否指定渠道")
            private Boolean designatedChannels;
            @ApiModelProperty(value = "指定渠道")
            private List<OperationPromotionPlanChannelVo> channels;
            @ApiModelProperty(value = "自定义排序")
            private Integer customSort;
            @NotEmpty(message = "链接不能为空")
            @ApiModelProperty(value = "链接")
            private List<OperationPromotionPlanLinkDto> links;
            //            @ApiModelProperty(value = "推广链接前缀")
//            private String linkUrlPrefix;
//            @ApiModelProperty(value = "链接类型，1-原生链接，2-webview链接")
//            private Integer linkUrlType;
//            @ApiModelProperty(value = "推广链接")
//            private String linkUrl;
//            @ApiModelProperty(value = "链接方式，1-手动输入，2-模板,3- 固定链接")
//            private Integer linkType;
//            @ApiModelProperty(value = "推广链接模版id")
//            private Long linkTemplateId;
//            @ApiModelProperty(value = "推广链接参数")
//            private List<LinkParamRo> linkParams;
//            @ApiModelProperty(value = "是否自动验证下架")
//            private Boolean autoVerifyFlag;
//            @ApiModelProperty(value = "自动验证下架参数code")
//            private List<String> autoVerifyCodes;
            @ApiModelProperty(value = "是否需要验证,默认false,true-需要验证")
            private Boolean needVerify;
        }
    }
}
