package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * UserIdentityInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "UserIdentityInfoDto", description = "用户身份信息")
public class UserIdentityInfoDto {

    /**
     * 中文姓名
     */
    @ApiModelProperty(value = "中文姓名")
    private String chineseGuestName;
    /**
     * 拼音姓名，多音字多个
     */
    @ApiModelProperty(value = "姓名拼音")
    private List<String> pinyinGuestName;
}
