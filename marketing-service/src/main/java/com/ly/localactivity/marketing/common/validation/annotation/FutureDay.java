package com.ly.localactivity.marketing.common.validation.annotation;


import com.ly.localactivity.marketing.common.validation.FutureDayValidator;

import javax.validation.Constraint;
import java.lang.annotation.*;

/**
 * 校验未来(精确到天)
 * @date 2024/02/28
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = FutureDayValidator.class)
@Target( { ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface FutureDay {
    String message() default "The date must be in the future day";
    Class<?>[] groups() default {};
    Class<?>[] payload() default {};
}
