package com.ly.localactivity.marketing.service.coupon;

import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;

/**
 * ICouponBatchStockControlService
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
public interface ICouponBatchStockControlService {

    /**
     * 设置优惠券库存限制
     *
     * @param batchNo 批号
     */
    void setCouponStockLimit(String batchNo);

    /**
     * 设置优惠券日限制
     *
     * @param batchNo 批号
     */
    void setCouponDayLimit(String batchNo);

    /**
     * 判断批次是否受限
     *
     * @param batchNo 批号
     * @return {@link Boolean}
     */
    Boolean isStockLimit(String batchNo);

    /**
     * 刷新限制
     *
     * @param couponBatchExtDto 优惠券批量ext dto
     * @param clearDayLimitFlag 清除每日限制标志
     */
    void refreshLimit(CouponBatchExtDto couponBatchExtDto, Boolean clearDayLimitFlag);
}
