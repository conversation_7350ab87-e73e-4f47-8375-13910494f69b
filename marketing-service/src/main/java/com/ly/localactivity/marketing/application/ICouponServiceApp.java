package com.ly.localactivity.marketing.application;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.application.model.ro.*;
import com.ly.localactivity.marketing.application.model.vo.*;

import java.util.List;

/**
 * ICouponService
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public interface ICouponServiceApp {

    CommonResult<CouponQueryResponse> queryCouponListV2(CouponQueryRo queryRo);

    CommonResult<CouponQueryResponse> queryCouponListV3(CouponQueryRo queryRo);

    /**
     * 查询优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    CommonResult<CouponQueryResponse> queryCouponList(CouponQueryRo queryRo);

    /**
     * 充值券
     *
     * @param rechargeRo 充值ro
     * @return {@link CommonResult}<{@link CouponRechargeResponse}>
     */
    CommonResult<CouponRechargeResponse> rechargeCoupon(CouponRechargeRo rechargeRo);

    /**
     * 下单查询
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponOrderQueryResponse}>
     */
    CommonResult<CouponOrderQueryResponse> queryCouponOrderList(CouponOrderQueryRo queryRo);

    /**
     * 占用优惠券
     *
     * @param occupyRo 占领ro
     * @return {@link CommonResult}
     */
    CommonResult occupyCoupon(CouponOccupyRo occupyRo);

    /**
     * 恢复占用
     *
     * @param recoverRo 恢复ro
     * @return {@link Boolean}
     */
    CommonResult recoverCoupon(CouponRecoverRo recoverRo);

    /**
     * 消费优惠券
     *
     * @param consumeRo 消耗ro
     * @return {@link Boolean}
     */
    CommonResult consumeCoupon(CouponConsumeRo consumeRo);

    /**
     * 退还优惠券
     *
     * @param returnRo 返回ro
     * @return {@link Boolean}
     */
    CommonResult returnCoupon(CouponReturnRo returnRo);

    /**
     * 搜索列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}
     */
    CommonResult<List<CouponVo>> searchList(CouponListQueryRo queryRo);

    /**
     * 获取优惠券详细信息
     *
     * @param detailRo 细节ro
     * @return {@link CommonResult}
     */
    CommonResult<List<CouponVo>> getCouponDetail(CouponDetailRo detailRo);

    /**
     * 获取用户优惠券列表
     *
     * @param userQueryRo 用户查询ro
     * @return {@link CommonResult}<{@link List}<{@link CouponVo}>>
     */
    CommonResult<List<CouponVo>> getUserCoupon(CouponUserQueryRo userQueryRo);
    CommonResult<List<CouponVo>> getUserCouponV2(CouponUserQueryRo userQueryRo);

    /**
     * 获取点位优惠券
     *
     * @param couponPositionQueryRo 位置查询ro
     * @return {@link CommonResult}<{@link List}<{@link CouponPositionVo}>>
     */
    CommonResult<CouponPositionVo> couponPosition(CouponPositionQueryRo couponPositionQueryRo);

    /**
     * 查询所有符合条件的优惠券列表
     * @param queryRo
     * @return
     */
    CommonResult<List<CouponBatchVo>> queryAllCouponList(AllCouponQueryRo queryRo);
}
