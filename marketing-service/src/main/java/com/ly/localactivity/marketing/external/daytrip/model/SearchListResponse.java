package com.ly.localactivity.marketing.external.daytrip.model;

import lombok.Data;

import java.util.List;

/**
 * SearchListResponse
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
public class SearchListResponse {
    /**
     * 返回结果总数
     */
    private Integer total;

    /**
     * 接口状态码
     * <p>
     * 1:正常
     * <p>
     * 0:异常
     */
    private String status;
    /**
     * 服务IP
     */
    private String serverIP;

    private List<SearchItems> items;

    private List<SceneryStat> sceneryStat;

    private List<ClassifyStat> classifyStat;

    private List<LabelStat> labelStat;

    private List<DepartCityStat> departCityStat;

    /**
     * 埋点数据
     */
    private String buryData;

    /**
     * 当日最晚可预定时间
     *
     * 注:单位是秒
     */
    private Integer canBookToday;
    /**
     * 明日是否可预定
     *
     * 1:可预定
     *
     * 0:不可预定
     */
    private Integer canBookTomorrow;
    @Data
    public static class SceneryStat {
        /**
         * 景区ID
         */
        private Integer id;
        /**
         * 景区名称
         */
        private String name;
        /**
         * 数量
         */
        private Integer count;
    }

    @Data
    public static class DepartCityStat {
        /**
         *
         */
        private String id;
        /**
         *
         */
        private String name;

        private String count;
    }
    @Data
    public static class LabelStat {
        /**
         * 标签父ID
         */
        private Integer id;
        /**
         * 标签父名称
         */
        private String name;
        /**
         *
         */
        private List<stateEntity> stateEntityList;
    }

    @Data
    public static class stateEntity {
        /**
         * 标签ID
         */
        private Integer id;
        /**
         * 标签名称
         */
        private String name;
        /**
         * 数量
         */
        private Integer count;

        /**
         * 运营标签排序值
         */
        private Integer sort;
    }

    @Data
    public static class ClassifyStat {
        /**
         * 主题ID
         */
        private Integer id;
        /**
         * 主题名称
         */
        private String name;
        /**
         * 数量
         */
        private Integer count;
    }

    @Data
    public static class SearchItems {
        /**
         * 主键ID
         */
        private Long mrId;
        /**
         * 资源ID
         */
        private Long id;
        /**
         * 产品编号ID
         */
        private String seriaId;

        /**
         * 一级分类ID
         */
        private Integer categoryId;
        /**
         * 一级分类名称
         */
        private String categoryName;

        private String canSaleDaily;
        /**
         * 二级分类ID
         */
        private Integer sCategoryId;
        /**
         * 二级分类名称
         */
        private String sCategoryName;


        /**
         * 资源名称
         */
        private String title;
        /**
         * 资源标题
         */
        private String subTitle;
        /**
         * 点评得分
         */
        private Double score;
        /**
         * 同程最小售价
         * <p>
         * 注:单位为分
         */
        private Integer sellPrice;
        /**
         * 同程价
         * <p>
         * 注:单位为分
         */
        private Integer tcPrice;
        /**
         * 头图
         */
        private String image;

        /**
         * 每日最晚预订时间
         */
        private int lastAdvTime;

        private int beforeDays;
        /**
         * 是否退订无忧
         */
        private Integer isCanCancel;
        /**
         * 是否立即确认
         */
        private Integer isConfirm;
        /**
         * 是否测试产品
         */
        private Integer isTest;
        /**
         * 创单量
         */
        private Integer saleCount;
        /**
         * 整型
         */
        private List<DestinationItems> destinationItems;

        private List<HotCityItems> hotCityItems;

        private List<PoiItems> poiItems;

        private List<LabelItems> labelItems;

        private List<ThemeItems> themeItems;

        /**
         * 埋点数据(各项)
         */
        private String tag;
        /**
         * 是否优品
         */
        private int hasPreferred;
        /**
         * 是否热销
         */
        private int hotSale;
    }

    @Data
    public static class DestinationItems {
        /**
         * 洲ID
         */
        private Integer continetId;
        /**
         * 洲名称
         */
        private String continetName;
        /**
         * 国家ID
         */
        private Integer countryId;
        /**
         * 国家名称
         */
        private String countryName;
        /**
         * 省份ID
         */
        private Integer provinceId;
        /**
         * 省份名称
         */
        private String provinceName;
        /**
         * 城市ID
         */
        private String cityId;
        /**
         * 城市名称
         */
        private String cityName;
        /**
         * 区县ID
         */
        private Integer countyId;
        /**
         * 区县名称
         */
        private String countyName;
        /**
         *
         * 出发地、目的地标识
         *
         * 0:出发地
         *
         * 1:目的地
         */
        private Integer type;
    }

    @Data
    public static class HotCityItems {
        /**
         * 热销城市ID
         */
        private String id;
        /**
         * 热销城市名称
         */
        private String name;
    }

    @Data
    public static class PoiItems {
        /**
         * 同程景区ID
         */
        private Long id;
        /**
         * 原始名称
         */
        private String name;

        /**
         * 同程名称
         */
        private String tcName;
        /**
         * 等级
         */
        private String level;
        /**
         * 评分
         */
        private String rating;
        /**
         * 主题ID
         */
        private String themeIds;
        /**
         * 主题名称
         */
        private String themeNames;
    }

    @Data
    public static class LabelItems {
        /**
         * 标签ID
         */
        private int id;
        /**
         * 标签名称
         */
        private String name;

        /**
         * 标签描述
         */
        private String remark;

        /**
         * 标签Code
         */
        private String tagCode;
        /**
         * 标签排序值
         */
        private String rtcSort;

    }

    @Data
    public static class ThemeItems {
        /**
         * 主题ID
         */
        private Integer themeId;
        /**
         * 主题名称
         */
        private String themeName;

        /**
         * 关联行程数
         */
        private String poiSum;
    }
}
