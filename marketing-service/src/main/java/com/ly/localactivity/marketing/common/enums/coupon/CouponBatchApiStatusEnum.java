package com.ly.localactivity.marketing.common.enums.coupon;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 优惠券批次状态，API对外返回使用
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
@AllArgsConstructor
@Getter
public enum CouponBatchApiStatusEnum {
    /**
     * 可用
     */
    Available(1, "可领取"),
    /**
     * 联合国可用
     */
    UnAvailable(2, "不可领取"),
    /**
     * 已过期
     */
    Expired(3, "已过期"),
    ;
    private final Integer code;
    private final String desc;

    public static CouponBatchApiStatusEnum getByExtStatus(Integer code) {
        CouponBatchExtStatusEnum extStatus = EnumUtil.getBy(CouponBatchExtStatusEnum::getCode, code);
        if (extStatus == null) {
            return CouponBatchApiStatusEnum.UnAvailable;
        }
        if (extStatus == CouponBatchExtStatusEnum.Normal) {
            return CouponBatchApiStatusEnum.Available;
        }

        return CouponBatchApiStatusEnum.UnAvailable;
    }

    public static CouponBatchApiStatusEnum getByExtStatus(Integer code, LocalDateTime lastUseTime) {
        CouponBatchExtStatusEnum extStatus = EnumUtil.getBy(CouponBatchExtStatusEnum::getCode, code);
        if (extStatus == null) {
            return CouponBatchApiStatusEnum.UnAvailable;
        }

        if (extStatus == CouponBatchExtStatusEnum.USED_EXPIRED){
            return CouponBatchApiStatusEnum.Expired;
        }

        if (lastUseTime != null && lastUseTime.isBefore(LocalDateTime.now())) {
            return CouponBatchApiStatusEnum.Expired;
        }

        if (extStatus == CouponBatchExtStatusEnum.Normal) {
            return CouponBatchApiStatusEnum.Available;
        }

        return CouponBatchApiStatusEnum.UnAvailable;
    }
}
