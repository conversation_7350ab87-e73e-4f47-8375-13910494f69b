package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OrderInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "OrderInfoDto", description = "售卖券订单信息")
@Data
public class OrderInfoDto {
    /**
     * 售卖金额
     */
    @ApiModelProperty(value = "售卖金额")
    private String saleAmount;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private String activityId;
    /**
     * 券的承担项目
     */
    @ApiModelProperty(value = "券的承担项目")
    private String couponProject;
    /**
     * 券的承担项目主体
     */
    @ApiModelProperty(value = "券的承担项目主体")
    private String couponProjectMainstay;
    /**
     * 券抵扣的产品段
     */
    @ApiModelProperty(value = "券抵扣的产品段")
    private String couponDeductedProduct;
    /**
     * 收款产品
     */
    @ApiModelProperty(value = "收款产品")
    private String collectionProducts;
}
