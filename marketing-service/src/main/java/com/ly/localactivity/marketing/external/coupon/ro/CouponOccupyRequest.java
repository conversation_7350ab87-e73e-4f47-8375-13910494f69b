package com.ly.localactivity.marketing.external.coupon.ro;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * CouponOccupyRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CouponOccupyRo", description = "占用优惠券")
public class CouponOccupyRequest extends CouponBaseRequest {
    /**
     * 券号
     */
    @ApiModelProperty(value = "券号", required = true)
    @NotNull(message = "券号不能为空")
    @NotBlank(message = "券号不能为空")
    private String couponNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 特殊规则
     */
    @ApiModelProperty(value = "特殊规则")
    private JSONObject extRuleInfo;
    /**
     * 乘机人信息
     */
    @ApiModelProperty(value = "乘机人信息")
    private List<PersonInfo> consumeUsers;
    /**
     * 当地时间
     */
    @ApiModelProperty(value = "当地时间")
    private String consumeTime;
    /**
     * 消费币种
     */
    @ApiModelProperty(value = "消费币种")
    private String consumeCurrency;
    /**
     * 项目特殊规则是否校验：默认 0 校验；1 不校验
     */
    @ApiModelProperty(value = "项目特殊规则是否校验：默认 0 校验；1 不校验")
    private Integer isCheckRule;
}
