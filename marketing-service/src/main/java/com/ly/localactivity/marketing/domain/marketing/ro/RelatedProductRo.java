package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.common.validation.group.PlatformValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description RelatedProductRo
 * @date 2024-04-22
 */
@ApiModel("关联商品")
@Data
public class RelatedProductRo {
    @ApiModelProperty(value = "批次id", required = true)
    @NotNull(message = "批次id不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private Long couponBatchId;
    @ApiModelProperty(value = "ruleId")
    private Long ruleId;
    @ApiModelProperty(value = "商品限制类型，1-全部(不限)，2-指定出发地，3-指定二级品类，4-指定商品", required = true)
    @Range(min = 1, max = 4, message = "适用产品范围配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer limitType;
    @ApiModelProperty(value = "数据ids")
    private List<Long> dataIds;
    @ApiModelProperty(value = "佣金限制类型，1-不限，2-佣金>0，3-佣金>=0")
    @Range(min = 1, max = 3, message = "佣金限制类型配置错误", groups = {PlatformValidation.class})
    private Integer commissionType;

}
