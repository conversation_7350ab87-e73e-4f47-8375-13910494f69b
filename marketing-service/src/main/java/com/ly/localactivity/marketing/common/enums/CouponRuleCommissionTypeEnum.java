package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 佣金限制，1-不限，2-佣金>0，3-佣金>=0
 * @date 2024-04-22
 */
@AllArgsConstructor
@Getter
public enum CouponRuleCommissionTypeEnum {
    /**
     * 1-不限
     */
    UNLIMITED(1, "不限"),
    /**
     * 2-佣金>0
     */
    GT_ZERO(2, "佣金>0"),
    /**
     * 3-佣金>=0
     */
    GE_ZERO(3, "佣金>=0")
    ;

    private final Integer code;
    private final String desc;
}
