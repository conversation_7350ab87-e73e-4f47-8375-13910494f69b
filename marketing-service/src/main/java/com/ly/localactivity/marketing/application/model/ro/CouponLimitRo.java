package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponLimitRo
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponLimitRo {
    /**
     * 出发城市id，多个逗号分隔
     */
    @ApiModelProperty(value = "出发城市id，多个逗号分隔")
    private String cityIds;
    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id，多个逗号分隔")
    private String resourceIds;
    /**
     * 第二类
     */
    @ApiModelProperty(value = "二级品类，多个逗号分隔")
    private String secondCategorys;
    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id 和supplierIds二选一")
    private Long supplierId;
    /**
     * 商家id
     */
    @ApiModelProperty(value = "商家id，多个逗号分隔 和supplierId二选一")
    private String supplierIds;

    @ApiModelProperty(value = "一级品类")
    private Integer firstCategoryId;


//    /**
//     * 运营点位code
//     */
//    @ApiModelProperty(value = "运营点位code",hidden = true)
//    private String positionCode;

//    @ApiModelProperty(value = "refid",hidden = true)
//    private String refid;
//
//    @ApiModelProperty(value = "requestId",hidden = true)
//    private String requestId;
////    @ApiModelProperty(value = "是否过滤时间",hidden = true)
////    private Boolean filterDate;
////    @ApiModelProperty(value = "过滤状态，null不过滤",hidden = true)
////    private Boolean filterStatus;
//    @ApiModelProperty(value = "是否过滤可用",hidden = true)
//    private Boolean filterAvaliabled;
}
