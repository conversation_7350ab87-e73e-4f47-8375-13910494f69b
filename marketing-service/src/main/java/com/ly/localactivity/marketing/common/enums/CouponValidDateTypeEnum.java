package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 使用有效期类型，1-固定日期段，2-动态有效期，3-倒计时(小时)，4-倒计时(分钟)
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponValidDateTypeEnum {
    /**
     * 1-固定日期段
     */
    FIXED(1, "固定有效期"),
    /**
     * 2-动态有效期
     */
    DYNAMIC(2, "动态有效期"),
    /**
     * 3-倒计时(小时)
     */
    COUNTDOWN_HOUR(3, "倒计时(小时)"),
    /**
     * 4-倒计时(分钟)
     */
    COUNTDOWN_MINUTE(4, "4-倒计时(分钟)");
    private final Integer code;
    private final String desc;
}
