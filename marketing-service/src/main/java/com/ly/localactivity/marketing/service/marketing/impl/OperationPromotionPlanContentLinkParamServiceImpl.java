package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContentLinkParam;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentLinkParamDto;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentLinkParamMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanContentLinkParamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营推广链接参数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@Service
public class OperationPromotionPlanContentLinkParamServiceImpl extends ServiceImpl<OperationPromotionPlanContentLinkParamMapper, OperationPromotionPlanContentLinkParam> implements IOperationPromotionPlanContentLinkParamService {


    /**
     * 保存或更新批处理
     *
     * @param linkParamDtos 链接参数dtos
     * @param planContentId 计划内容id
     * @param modifier      修饰符
     */
    @Override
    public void saveOrUpdateBatch(List<OperationPromotionPlanContentLinkParamDto> linkParamDtos, Long planContentId, String modifier) {
        List<OperationPromotionPlanContentLinkParam> contentLinkParams = getInfoByPlanContentId(planContentId);

        // 需要删除的ids
        List<Long> needDeleteIds = contentLinkParams.stream().map(OperationPromotionPlanContentLinkParam::getId).collect(Collectors.toList());

        // 需要插入的
        List<OperationPromotionPlanContentLinkParam> needInsert = linkParamDtos.stream().map(linkParamDto -> {
            OperationPromotionPlanContentLinkParam contentLinkParam = new OperationPromotionPlanContentLinkParam();
            BeanUtil.copyProperties(linkParamDto, contentLinkParam);
            contentLinkParam.setCreator(modifier);
            contentLinkParam.setModifier(modifier);
            contentLinkParam.setModifiedTime(LocalDateTime.now());
            contentLinkParam.setCreateTime(LocalDateTime.now());
            contentLinkParam.setDeleteFlag(Boolean.FALSE);
            return contentLinkParam;
        }).collect(Collectors.toList());
        //删除
        if (CollectionUtil.isNotEmpty(needDeleteIds)) {
            baseMapper.deleteByIds(needDeleteIds, modifier.substring(0, modifier.indexOf(StrUtil.BRACKET_START)), modifier.substring(modifier.indexOf(StrUtil.BRACKET_START) + 1, modifier.indexOf(StrUtil.BRACKET_END)));
        }

        //插入
        if (CollectionUtil.isNotEmpty(needInsert)) {
            needInsert.forEach(baseMapper::insert);
        }

    }

    private List<OperationPromotionPlanContentLinkParam> getInfoByPlanContentId(Long planContentId) {
        return this.lambdaQuery()
                .eq(OperationPromotionPlanContentLinkParam::getPlanContentId, planContentId)
                .eq(OperationPromotionPlanContentLinkParam::getDeleteFlag, Boolean.FALSE)
                .list();
    }
}
