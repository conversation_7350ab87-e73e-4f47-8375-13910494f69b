package com.ly.localactivity.marketing.common.constants;

/**
 * 所有统一配置的键值
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
public class ConfigConst {
    /**
     * api测试签名
     */
    public static final String Api_Test_Sign = "Api_Test_Sign";

    public static final String RESOURCE_BLANK_IMAGE_CONFIG = "Resource_Blank_Image_Url";

    public static final String Page_AdPopupConfig = "Page_Ad_Popup_Config";

    /**
     * 创建鲲鹏红包url
     */
    public static final String COUPON_CREATE_ACTIVITY_URL = "Coupon_Create_Activity_Url";

    /**
     * 创建鲲鹏红包url
     */
    public static final String COUPON_CREATE_ACTIVITY_TOKEN = "Coupon_Create_Activity_Token";

    /**
     * 创建鲲鹏红包admin
     */
    public static final String COUPON_CREATE_ACTIVITY_ADMIN = "Coupon_Create_Activity_Admin";

    /**
     * 提交鲲鹏红包审批url
     */
    public static final String COUPON_START_APPROVE_URL = "Coupon_Start_Approve_Url";

    /**
     * 鲲鹏红包关闭url
     */
    public static final String COUPON_CLOSE_URL = "Coupon_Close_Url";

    /**
     * 鲲鹏红包关闭默认token
     */
    public static final String COUPON_CLOSE_DEFAULT_TOKEN = "Coupon_Close_Default_Token";

    /**
     * 创建鲲鹏红包请求模板
     */
    public static final String COUPON_CREATE_REQUEST_TEMPLATE = "Coupon_Create_Request_Template";


    public static final String Admin_API_Token = "Api_Token";

    /**
     * 会员扩展信息url
     */
    public static final String MEMBER_EXTEND_INFO_URL = "Member_Extend_Info_Url";
    /**
     * 会员主url
     */
    public static final String MEMBER_MAIN_URL = "Member_Main_Url";
    /**
     * 会员接口appKey
     */
    public static final String MEMBER_API_APP_KEY = "Member_Api_AppKey";
    /**
     * 会员接口appSecret
     */
    public static final String MEMBER_API_APP_SECRET = "Member_Api_AppSecret";
    /**
     * 微信绑定关系url
     */
    public static final String MEMBER_BINDING_RELATION_URL = "Member_Binding_Relation_Url";
    /**
     * 会员解密地址
     */
    public static final String MEMBER_SECURITY_URL = "Member_Security_Url";

    public static final String COUPON_RECHARGE_REPEAT_INTERVAL = "Coupon_Recharge_Repeat_Interval";

    /**
     * 红包跳转链接
     */
    public static final String COUPON_LINK_URL = "Coupon_Link_Url";
    public static final String EBK_POSITION_FILTER_FLAG = "Ebk_Position_Filter_Flag";

    /**
     * 红包详情点位code列表
     */
    public static final String COUPON_DETAIL_POSITION_CODE = "Coupon_Detail_Position_Code";

    /**
     * es模板配置
     */
    public static final String ES_TEMPLATE_CONFIG = "Es_Template_Config";

    /**
     * 红包接口切换控制
     */
    public static final String COUPON_API_VERSION = "Coupon_Api_Version";

    /**
     * 红包接口切换控制
     */
    public static final String COUPON_QUERY_ES_OR_REDIS = "Coupon_Query_Es_Or_Redis";

    /**
     * Es接口是否 同步请求控制
     */
    public static final String COUPON_SYNC_QUERY_ES = "Coupon_Sync_Query_Es";

    /**
     * 红包接口切换控制
     */
    public static final String COUPON_LIST_API_VERSION = "Coupon_List_Api_Version";

    /**
     * 已领红包接口控制阈值
     */
    public static final String COUPON_LIST_API_AUTO_CHANGE_THRESHOLD = "Coupon_List_Api_Auto_Change_Threshold";

    /**
     * 红包接口切换控制
     */
    public static final String COUPON_USER_USE_ORDER_API = "Coupon_User_Use_Order_Api";

    /**
     * 接口重试超时时间
     */
    public static final String HTTP_CLIENT_RETRY_TIME_OUT = "Http_Client_Retry_Time_Out";
}
