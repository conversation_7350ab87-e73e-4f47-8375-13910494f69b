package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CouponRechargeRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CouponRechargeRo", description = "充值券请求参数")
public class CouponRechargeRequest extends CouponBaseRequest {
    /**
     * 批号
     */
    @ApiModelProperty(value = "批次号", required = true)
    @NotBlank(message = "批次号不能为空")
    @NotNull(message = "批次号不能为空")
    private String batchNo;
    /**
     * 是否为礼包 0-否,1-是
     */
    @ApiModelProperty(value = "是否为礼包 0-否,1-是")
    private Integer isPackage;
    /**
     * 幂等校验，主要功能如下：
     * 1、防止同一请求领取多次；
     * 2、客户端异常后，支持原trandNo重试，返回结果一致；
     * 重点备注：
     * 1、tradeNo在2小时内有效，建议网络异常后立即重试；
     * 2、建议设为必传值，需全局唯一，建议用UUID或useKey+batchNo+第几次领取；
     */
    @ApiModelProperty(value = "幂等校验", required = true)
    private String tradeNo;

    /**
     * 二要素，用券人（如果传则限定券使用人，不传则不限制）
     */
    @ApiModelProperty(value = "用卷人")
    private List<PersonInfo> couponUsers;
    /**
     * 商品id(里程商城专用）
     */
    @ApiModelProperty(value = "商品id(里程商城专用)")
    private List<String> productIds;

    /**
     * 是否预发券 0-否,1-是 默认否
     */
    @ApiModelProperty(value = "是否预发券 0-否,1-是,默认否")
    private Integer appointment;
    /**
     * 赠送/预发订单号
     */
    @ApiModelProperty(value = "赠送/预发订单号")
    private String orderNo;
    /**
     * 设备号id（单设备领取次数限制）
     * 备注：
     * 如果是机票项目的风控，该值必传
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 用户ip
     * 机票项目风控则，该值必传
     */
    @ApiModelProperty(value = "用户ip")
    private String userIp;
    /**
     * 发放渠道refid
     */
    @ApiModelProperty(value = "发放渠道refid")
    private String refid;
    /**
     * 金额,如果是酒店的「浮动红包」，这个值必传
     */
    @ApiModelProperty(value = "金额")
    private Long amount;
    /**
     * 身份证号，密文，透传给酒店接口，校验身份证领取次数（澳门红包new）
     */
    @ApiModelProperty(value = "身份证号，密文，透传给酒店接口，校验身份证领取次数（澳门红包new）")
    private String idCardNumber;
    /**
     * 姓名，密文（澳门红包new）
     */
    @ApiModelProperty(value = "姓名，密文（澳门红包new）")
    private String guestName;
    /*
     *站外平台id - 会员thirdpartyId（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外平台id - 会员thirdpartyId（支付宝小程序登陆后必传）")
    private Integer outsidePartyId;
    /**
     * 站外会员id（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外会员id（支付宝小程序登陆后必传")
    private String outsideUnionId;

}
