package com.ly.localactivity.marketing.common.handler.promotionPlanParam;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.LinkUrlTypeEnum;
import com.ly.localactivity.marketing.common.enums.PromoteTypeEnum;
import com.ly.localactivity.marketing.common.enums.PromotionLinkTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionPlanSingleEditRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanChannelVo;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @description NullValueCheckHandler
 * @date 2024-04-08
 */
@Component
@Slf4j
public class NullValueCheckHandler extends AbstractCheckHandler {


    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private NullValueCheckHandler nullValueCheckHandler;

    @Autowired
    public void init(OutputCheckHandler outputCheckHandler) {
        //从容器中获取下一个处理器
        nextHandler = outputCheckHandler;
    }

    @Override
    public void handle(OperationPromotionPlanSingleEditRo param, Set<String> accumulationSet) {
        if (!accumulationSet.add("NullValueCheckHandler")) {
            return;
        }
        SkyLogUtils.infoByMethod(log, StringUtils.format("NullValueCheck[start],param:{}", JSON.toJSONString(param)), "", "");
        boolean isEdit = ObjectUtil.isNotNull(param.getId()) && param.getId() > 0;
        OperationPosition operationPosition = operationPositionService.getById(param.getPositionId());
        if (isEdit && ObjectUtil.isNull(operationPosition)) {
            throw new ApiException("推广位不存在");
        }
        //设置关键字最大长度
        param.setKeyWordMaxLength(operationPosition.getKeywordMaxLength());
        param.setSubTitleMaxLength(operationPosition.getSubTitleMaxLength());

        Integer promoteType = param.getPromoteType();
        PromoteTypeEnum typeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, promoteType);
        if (ObjectUtil.isNull(typeEnum)) {
            throw new ApiException("推广方式不存在");
        }
        //名称校验
        if (ObjectUtil.isNull(param.getName())) {
            throw new ApiException("推广计划名称不能为空");
        }
        //非兜底
        if (!param.getDefaultFlag()) {
            //推广计划开始时间
            if (ObjectUtil.isNull(param.getValidBeginDate())) {
                throw new ApiException("推广计划开始时间不能为空");
            }
            //推广计划结束时间
            if (ObjectUtil.isNull(param.getValidEndDate())) {
                throw new ApiException("推广计划结束时间不能为空");
            }
            if (ObjectUtil.isNull(param.getReasonType()) || StringUtils.isBlank(param.getReasonRemark())) {
                throw new ApiException("推广原因不能为空");
            }
            if (ObjectUtil.isNull(param.getTopFlag())) {
                throw new ApiException("锁定不能为空");
            }
        }
        Boolean placeFlag = param.getPlaceFlag();
        if (ObjectUtil.isNull(placeFlag)) {
            throw new ApiException("是否占位不能为空");
        }
        //占位则不需要校验
        if (!placeFlag) {
            if (CollectionUtil.isEmpty(param.getLinks())) {
                throw new ApiException("链接类型不能为空");
            }
            for (OperationPromotionPlanLinkDto paramLink : param.getLinks()) {

                String linkUrl = paramLink.getLinkUrl();
                Integer linkType = paramLink.getLinkType();
                PromotionLinkTypeEnum linkTypeEnum = EnumUtil.getBy(PromotionLinkTypeEnum::getCode, linkType);
                if (ObjectUtil.isNull(linkTypeEnum)) {
                    throw new ApiException("链接类型不能为空");
                }
                if (ObjectUtil.notEqual(linkTypeEnum, PromotionLinkTypeEnum.EMPTY) && StringUtils.isBlank(linkUrl)) {
                    throw new ApiException("链接地址不能为空");
                }
                LinkUrlTypeEnum urlTypeEnum = EnumUtil.getBy(LinkUrlTypeEnum::getCode, paramLink.getLinkUrlType());
                //链接判断
                if (linkTypeEnum.equals(PromotionLinkTypeEnum.DIRECT_INPUT) && ObjectUtil.isNotNull(urlTypeEnum)) {
                    if (LinkUrlTypeEnum.WEBVIEW.equals(urlTypeEnum) && StringUtils.isBlank(paramLink.getLinkUrlPrefix())) {
                        throw new ApiException("固定链接前缀不能为空");
                    } else if (LinkUrlTypeEnum.NATIVE.equals(urlTypeEnum)) {
                        paramLink.setLinkUrlPrefix(StringUtils.EMPTY);
                    }
                } else if (linkTypeEnum.equals(PromotionLinkTypeEnum.TEMPLATE) && ObjectUtil.isNull(paramLink.getLinkTemplateId())) {
                    throw new ApiException("链接模板不能为空");
                }

            }

            //校验渠道
            if (param.getDesignatedChannels()) {
                if (CollectionUtil.isEmpty(param.getChannels())) {
                    throw new ApiException("渠道不能为空");
                }
                for (OperationPromotionPlanChannelVo channel : param.getChannels()) {
                    if (StringUtils.isBlank(channel.getRefid())) {
                        throw new ApiException("refid不能为空");
                    }
                }
            }

        }
        if (ObjectUtil.isNull(param.getValidFlag())) {
            throw new ApiException("有效标记不能为空");

        }
        // 调用下一个处理器
        nullValueCheckHandler.next("NullValueCheckHandler", param,accumulationSet);
    }
}
