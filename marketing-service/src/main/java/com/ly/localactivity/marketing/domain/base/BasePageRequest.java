package com.ly.localactivity.marketing.domain.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * BasePageRequest
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BasePageRequest {
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "pageIndex最小为1")
    private Integer pageIndex;
    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小", required = true)
    @NotNull(message = "页大小不能为空")
    @Min(value = 1, message = "pageSize最小为1")
    @Max(value = 100, message = "pageSize最大为100")
    private Integer pageSize;
}
