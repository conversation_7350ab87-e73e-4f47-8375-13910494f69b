package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanRegion;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanRegionMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanRegionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营推广告计划地区 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Service
public class OperationPromotionPlanRegionServiceImpl extends ServiceImpl<OperationPromotionPlanRegionMapper, OperationPromotionPlanRegion> implements IOperationPromotionPlanRegionService {

    /**
     * 保存区域列表
     *
     * @param planId              计划id
     * @param regionList          ids
     * @param isRegionZone        是否市场区域
     * @param isChangePromoteType
     * @param modifier            修改人
     */
    @Override
    public void saveRegionList(Long planId, List<Long> regionList, Boolean isRegionZone, Boolean isChangePromoteType, String modifier) {

        List<OperationPromotionPlanRegion> existRegions = getByPlanId(planId);
        List<Long> needDeleteIds = new ArrayList<>();
        // 判断需要删除的数据
        if (isChangePromoteType) {
            needDeleteIds.addAll(existRegions.stream().map(OperationPromotionPlanRegion::getId).collect(Collectors.toList()));
        } else {
            existRegions.parallelStream().forEach(s -> {
                if (isRegionZone) {
                    if (regionList.contains(s.getRegionZoneId())) {
                        needDeleteIds.add(s.getId());
                    }
                } else {
                    if (regionList.contains(s.getRegionId())) {
                        needDeleteIds.add(s.getId());
                    }
                }
            });
        }
        //删除数据
        if (CollectionUtil.isNotEmpty(needDeleteIds)) {
            this.baseMapper.deleteBatchIds(needDeleteIds);
        }

        //新增数据
        regionList.parallelStream().forEach(s -> {
            OperationPromotionPlanRegion region = new OperationPromotionPlanRegion();
            region.setPlanId(planId);
            if (isRegionZone) {
                region.setRegionZoneId(s);
            } else {
                region.setRegionId(s);
            }
            region.setDeleteFlag(Boolean.FALSE);
            region.setModifier(modifier);
            region.setCreator(modifier);
            region.setCreateTime(LocalDateTime.now());
            region.setModifiedTime(LocalDateTime.now());
            baseMapper.insert(region);
        });
    }

    @Override
    public List<OperationPromotionPlanRegion> getByPlanId(Long planId) {
        return list(
                new LambdaQueryWrapper<OperationPromotionPlanRegion>()
                        .eq(OperationPromotionPlanRegion::getPlanId, planId)
                        .eq(OperationPromotionPlanRegion::getDeleteFlag, Boolean.FALSE)
        );
    }
}
