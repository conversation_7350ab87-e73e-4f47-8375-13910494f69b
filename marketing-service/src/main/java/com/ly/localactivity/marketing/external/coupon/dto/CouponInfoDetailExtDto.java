package com.ly.localactivity.marketing.external.coupon.dto;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * CouponInfoDetailDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
@ApiModel(value = "CouponInfoDetailDto", description = "代金劵")
public class CouponInfoDetailExtDto extends CouponInfoExtDto {
    /**
     * 券作废时间
     */
    @ApiModelProperty(value = "券作废时间")
    private String deleteTime;
    /**
     * 券状态,1-未使用；2-已占用；3-已使用；4-已作废; 5-已过期
     */
    @ApiModelProperty(value = "券状态")
    private Integer couponStatus;

    /**
     * 用途
     */
    @ApiModelProperty(value = "用途")
    private String purpose;
    /**
     * 0 新建 1 审批中 2 审批通过 3 已驳回 4 已取消 5 无效
     */
    @ApiModelProperty(value = "批次状态")
    private Integer batchStatus;
    /**
     * 消费时间
     */
    @ApiModelProperty(value = "消费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime consumeTime;
    /**
     * 消费订单号
     */
    @ApiModelProperty(value = "消费订单号")
    private String consumeOrderNo;
    /**
     * 使用渠道
     */
    @ApiModelProperty(value = "使用渠道")
    private String useChannel;
    /**
     * 二要素
     */
    @ApiModelProperty(value = "二要素")
    private JSONArray ext;
    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private Integer sourceDept;
    /**
     * 成本分摊部门
     */
    @ApiModelProperty(value = "成本分摊部门")
    private List<CostDeptDto> costDept;

    /**
     * 用户类型: 0 不限用户
     * 1 新用户可领
     * 2 老用户可领
     */
    @ApiModelProperty(value = "用户类型")
    private Integer memberType;
    /**
     * 是否可退， 0 可退， 1 不可退
     */
    @ApiModelProperty(value = "是否可退")
    private Integer isRefund;
    /**
     * 是否付出 0不需要付出，1需要付出
     */
    @ApiModelProperty(value = "是否付出")
    private Integer isUserPay;
    /**
     * 是否无敌红包
     */
    @ApiModelProperty(value = "是否无敌红包")
    private Integer isSuppercoupon;
    /**
     * 是否关联银行卡
     */
    @ApiModelProperty(value = "是否关联银行卡")
    private Integer isLinkBank;
    /**
     * 银行卡类型
     */
    @ApiModelProperty(value = "银行卡类型")
    private Integer bankType;
    /**
     * 银行卡code
     */
    @ApiModelProperty(value = "银行卡code")
    private String bankCode;
    /**
     * 微信卡券状态 -1:无资格0:未加入1:已加入(微信卡券专用)
     */
    @ApiModelProperty(value = "微信卡券状态")
    private Integer wechatStatus;
    /**
     * 微信卡券cardId
     */
    @ApiModelProperty(value = "微信卡券cardId")
    private String wechatCardId;
    /**
     * 1:可使用(券可用，在可用时间范围内）
     * <p>
     * 2:未生效（券不在可用时间范围内，举例，红包配置发放后三天生效，发放日期9.19日，生效日期9.21日，当9.19日查询的时候，接口会返回2，代表未生效）
     */
    @ApiModelProperty(value = "券状态")
    private Integer couponSubStatus;
    /**
     * 商品id(里程商城专用)
     */
    @ApiModelProperty(value = "商品id(里程商城专用)")
    private String productId;
    /**
     * 商品券信息(里程商城专用)
     * 0-普通券;1-单品券;2-跨品券
     */
    @ApiModelProperty(value = "商品券信息")
    private Integer productType;
    /**
     * 拓展属性列表
     */
    @ApiModelProperty(value = "拓展属性列表")
    private List<ExtendInfoDto> extInfoList;
    /**
     * 限制规则
     */
    @ApiModelProperty(value = "限制规则")
    private UseRuleInfoDto useRuleInfo;
    /**
     * 特殊规则
     */
    @ApiModelProperty(value = "特殊规则")
    private UseRuleListDto useRuleList;

    /**
     * 是否限制同一手机使用
     * 0-否,1-是
     */
    @ApiModelProperty(value = "是否限制同一手机使用")
    private String phoneLimit;
    /**
     * 领用限制同一手机号
     */
    @ApiModelProperty(value = "领用限制同一手机号")
    private String phoneNumber;
    /**
     * 券码流水号-火车票使用
     */
    @ApiModelProperty(value = "券码流水号")
    private Long dbrGuid;
    /**
     * 商店优惠券类型
     */
    @ApiModelProperty(value = "商店优惠券类型")
    private Integer shopCouponType;
    /**
     * 艺龙APP使用地址
     */
    @ApiModelProperty(value = "艺龙APP使用地址")
    private String eAppLink;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortId;
    /**
     * 追踪标识
     */
    @ApiModelProperty(value = "追踪标识")
    private String refid;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 红包使用说明（目前仅酒店红包有值）
     */
    @ApiModelProperty(value = "红包使用说明")
    private List<String> useDescList;
    /**
     * 用户实名信息
     */
    @ApiModelProperty(value = "用户实名信息")
    private UserIdentityInfoDto userIdentityInfo;
    /**
     * 海外/港澳台折扣券最大面额
     */
    @ApiModelProperty(value = "海外/港澳台折扣券最大面额")
    private BigDecimal discountMaxAmount;
    /**
     * 是否政府补贴券：空/0为普通红包、1为政府补贴券
     */
    @ApiModelProperty(value = "是否政府补贴券")
    private Integer governmentSubsidy;
    /**
     * 1.面额 2：票量
     */
    @ApiModelProperty(value = "1.面额 2：票量")
    private Integer ladderType;
    /**
     * 优惠券类型
     * 0:普通券 1：商家券 2：私域券
     */
    @ApiModelProperty(value = "优惠券类型 0:普通券 1：商家券 2：私域券")
    private Integer couponsType;
    /**
     * 领取入口
     * 不限制（0）/列表页（1）/详情页（2）/订单填写页（3）/销售系统（4）
     */
    @ApiModelProperty(value = "领取入口")
    private List<Integer> takeEntrance;
    /**
     * 是否红包白名单: 0否1是
     */
    @ApiModelProperty(value = "是否红包白名单")
    private Integer isCouponWhitelist;
    /**
     * 是否互斥立减:0否1是
     */
    @ApiModelProperty(value = "是否互斥立减")
    private Integer isMutualExclusion;
    /**
     * 是否外部采购:0否1是
     */
    @ApiModelProperty(value = "是否外部采购")
    private Integer isOutPurchase;
    /**
     * 个人领取限制
     */
    @ApiModelProperty(value = "个人领取限制")
    private Integer personLimit;
    /**
     * 有效期类型
     * 1 固定有效期，2动态有效期
     */
    @ApiModelProperty(value = "有效期类型")
    private Integer expType;

    /**
     * 有效开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "有效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expBeginDate;
    /**
     * 有效截止时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expEndDate;
    /**
     * 发放形式 0：线上 1：兑换码 2：礼包兑换码
     */
    @ApiModelProperty(value = "发放形式")
    private Integer isOffline;
    /**
     * 倒计时小时或者分钟
     */
    @ApiModelProperty(value = "倒计时小时或者分钟")
    private Integer expEndHours;
    /**
     * 动态有效开始天
     */
    @ApiModelProperty(value = "动态有效开始天")
    private Integer expBeginDays;
    /**
     * 动态有效持续天数
     */
    @ApiModelProperty(value = "动态有效持续天数")
    private Integer expEndDays;
    /**
     * 最高立减金额
     */
    @ApiModelProperty(value = "最高立减金额")
    private BigDecimal maxDiscountAmount;
    /**
     * 红包密文
     */
    @ApiModelProperty(value = "红包密文")
    private String couponCipher;

    /**
     * 每日领取限制
     */
    @ApiModelProperty(value = "每日领取限制")
    private Long dayLimit;

    /**
     * 总库存
     */
    @ApiModelProperty(value = "总库存")
    private Integer totalStock;
    /**
     * 剩余库存
     */
    @ApiModelProperty(value = "剩余库存")
    private Integer remainStock;
    /**
     * 内部名称
     */
    @ApiModelProperty(value = "内部名称")
    private String code;

}
