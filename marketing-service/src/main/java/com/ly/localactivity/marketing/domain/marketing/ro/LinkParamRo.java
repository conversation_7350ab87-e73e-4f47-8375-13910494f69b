package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * LinkParamRo
 *
 * <AUTHOR>
 * @date 2024/2/6
 */
@Data
@ApiModel(value = "LinkParamRo", description = "链接参数对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LinkParamRo  {
    /**
     * 主键 id
     */
    @ApiModelProperty(value = "链接参数 id")
    private Long id;
    /**
     * 链接参数
     */
    @ApiModelProperty(value = "链接参数Code", required = true)
    @NotBlank(message = "链接参数Code不能为空")
    @NotNull(message = "链接参数Code不能为空")
    private String paramCode;
    /**
     * 链接参数值
     */
    @ApiModelProperty(value = "链接参数值", required = false)
    private String paramValue;
    /**
     * 链接参数类型
     */
    @ApiModelProperty(value = "参数类型，1-继承点位页面参数，2-固定参数值，3-人工页面配置")
    @NotNull(message = "参数类型不能为空")
    @NotBlank(message = "参数类型不能为空")
    private Integer valueType;
}
