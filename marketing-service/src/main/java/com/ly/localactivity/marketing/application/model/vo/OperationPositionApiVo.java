package com.ly.localactivity.marketing.application.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * PromotionPositionVo
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@Data
@ApiModel(value = "OperationPositionVo", description = "运营点位Api接口Vo")
public class OperationPositionApiVo {
    @ApiModelProperty(value = "运营位置code")
    private String code;
    @ApiModelProperty(value = "运营位置名称")
    private String name;
    @ApiModelProperty(value = "运营点位自定义名称")
    private String alias;
    @ApiModelProperty(value = "模块")
    private Integer module;
    @ApiModelProperty(value = "可弹推广计划数")
    private Integer popupPlanCount;
    @ApiModelProperty(value = "计划可弹层次数，1-每天一次，2-仅一次，3-每N天一次")
    private Integer popupFrequencyType;
    @ApiModelProperty(value = "每N天一次")
    private Integer popupFrequencyDays;
    @ApiModelProperty(value = "输出类型，1-幻灯片，2-图片, 3-icon, 4-icon+关键词，5-关键词，6-红包容器")
    private Integer outputType;
    @ApiModelProperty(value = "图片宽度")
    private Integer imageWidth;
    @ApiModelProperty(value = "图片高度")
    private Integer imageHeight;
    @ApiModelProperty(value = "推广计划")
    private List<PromotionPlanApiVo> plans;
    @ApiModelProperty(value = "随机key")
    private String randomKey;
}
