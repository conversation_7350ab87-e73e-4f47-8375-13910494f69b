package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.bean.DictDetailInfo;
import com.ly.localactivity.marketing.common.bean.DictInfo;
import com.ly.localactivity.marketing.common.bean.SelectItemVo;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.constants.ExpireDaysConst;
import com.ly.localactivity.marketing.common.constants.OperationPositionRuleConst;
import com.ly.localactivity.marketing.common.enums.*;
import com.ly.localactivity.marketing.common.service.DictService;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.OperationPositionPage;
import com.ly.localactivity.marketing.domain.marketing.OperationPositionPlatform;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPositionDto;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionDetailRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionEditRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.*;
import com.ly.localactivity.marketing.mapper.marketing.OperationPositionMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionPageService;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionPlatformService;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 * 营销运营位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
@Slf4j
public class OperationPositionServiceImpl extends ServiceImpl<OperationPositionMapper, OperationPosition> implements IOperationPositionService {

    @Resource
    private BizLogUtils bizLogUtils;

    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private IOperationPositionPlatformService operationPositionPlatformService;
    @Resource
    private IOperationPositionPageService operationPositionPageService;
    @Resource
    private IOperationPromotionPlanService operationPromotionPlanService;
    @Resource
    private DictService dictService;
    @Resource
    private IOperationPositionService operationPositionService;


    /**
     * 查询运营位列表
     *
     * @param request 请求
     * @return {@link CommonPage}<{@link OperationPositionVo}>
     */
    @Override
    public CommonPage<OperationPositionVo> selectOperationPositionPage(OperationPositionListRo request) {
        Page<OperationPositionVo> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<OperationPositionVo> positionVoPage = baseMapper.listOperationPositionPage(page, request);
        for (OperationPositionVo operationPositionVo : positionVoPage.getRecords()) {
            //处理输出形式
            operationPositionVo.setOutputForm(OperationPositionOutputTypeEnum.getName(operationPositionVo.getOutputType()));
            //处理覆盖区域
            ArrayList<String> covers = CollectionUtil.newArrayList(operationPositionVo.getCoverRegionType().split(","));
            ArrayList<String> coverAreas = new ArrayList<>(covers.size());
            for (String cover : covers) {
                coverAreas.add(AreaCoverageEnum.getName(cover));
            }
            operationPositionVo.setCoverAreas(coverAreas);
        }
        return CommonPage.restPage(positionVoPage);
    }


    /**
     * 编辑运营点位
     *
     * @param request 请求
     */
    @Override
    @MultipleTransaction(name = {TransactionConstant.TcZBActivityMarketing})
    public void editOperationPosition(OperationPositionEditRo request) {
        //弹层特殊规则判断
        OperationPositionModelEnum modelEnum = EnumUtil.getBy(OperationPositionModelEnum::getCode, Integer.valueOf(request.getModuleValue()));
        boolean isPopup = OperationPositionModelEnum.POPUP.equals(modelEnum) || OperationPositionModelEnum.RED_PACKET_POPUP.equals(modelEnum);
        if (isPopup) {
            request.setImageMaxCount(request.getPopupPlanCount());
        }

        String modifier = adminTokenUtils.getEmployeeName();
        String modifierNo = adminTokenUtils.getEmployeeNo();
        LocalDateTime currTime = LocalDateTime.now();
        //old
        OperationPositionEditRo oldOperationPositionDetail;
        if (ObjectUtil.isNotNull(request.getId())) {
            OperationPositionDetailVo operationPositionDetailVo = selectOperationPositionDetail(OperationPositionDetailRo.builder().id(request.getId()).build());
            oldOperationPositionDetail = BeanUtil.copyProperties(operationPositionDetailVo, OperationPositionEditRo.class);
        } else {
            oldOperationPositionDetail = new OperationPositionEditRo();
        }
        //没做任何修改
        if (oldOperationPositionDetail.equals(request)) {
            return;
        }
        //运营点位
        String location = getLocation(request);
        //Code判重
        String positionCode = getCode(request.getPositionPageId(), location, request.getOutputType());
        int sameCodeCount = count(new LambdaQueryWrapper<OperationPosition>()
                .eq(OperationPosition::getCode, positionCode)
                .ne(ObjectUtil.isNotNull(request.getId()), OperationPosition::getId, request.getId())
        );
        if (sameCodeCount > 0) {
            throw new ApiException("运营点位重复");
        }

        //自定义名称判重
        if (StringUtils.isNotBlank(request.getAlias())) {
            int aliasCount = count(new LambdaQueryWrapper<OperationPosition>()
                    .eq(OperationPosition::getAlias, request.getAlias())
                    .ne(ObjectUtil.isNotNull(request.getId()), OperationPosition::getId, request.getId())
            );
            if (aliasCount > 0) {
                throw new ApiException("自定义名称重复");
            }
        }

        OperationPosition operationPosition = new OperationPosition();
        BeanUtil.copyProperties(request, operationPosition);
        operationPosition.setLocation(location);
        //code 不允许修改
        if (ObjectUtil.isNotNull(request.getId())) {
            operationPosition.setCode(null);
        } else {
            operationPosition.setCode(positionCode);
        }
        if (CollectionUtil.isNotEmpty(request.getCoverAreas())) {
            operationPosition.setCoverRegionType(String.join(StringUtils.COMMA, request.getCoverAreas()));
        } else {
            operationPosition.setCoverRegionType(StrUtil.EMPTY);

        }
        if (CollectionUtil.isNotEmpty(request.getPositionUsers())) {
            operationPosition.setPositionUser(String.join(StringUtils.COMMA, request.getPositionUsers()));
        } else {
            operationPosition.setPositionUser(StrUtil.EMPTY);

        }
        operationPosition.setModifier(modifier);
        operationPosition.setModifierNo(modifierNo);
        operationPosition.setModifiedTime(currTime);
        if (ObjectUtil.isNull(request.getId())) {
            operationPosition.setCreator(modifier);
            operationPosition.setCreatorNo(modifierNo);
            operationPosition.setCreateTime(currTime);
            operationPosition.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
        }
        operationPositionService.saveOrUpdate(operationPosition,
                new LambdaUpdateWrapper<OperationPosition>()
                        .eq(OperationPosition::getId, operationPosition.getId()));
        Long positionId = operationPosition.getId();

        //获取运营点位平台
        //按照平台code分组 key:平台code(platform)  value:平台code对应的运营点位平台
        List<OperationPositionPlatform> platformList = operationPositionPlatformService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<OperationPositionPlatform>()
                        .eq(OperationPositionPlatform::getPositionId, positionId)
                        .eq(OperationPositionPlatform::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        Map<String, OperationPositionPlatform> platformMapping = ObjectUtil.isNull(platformList) ? null : platformList.stream()
                .collect(Collectors.groupingBy(OperationPositionPlatform::getPlatform,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.get(0))));
        //处理平台
        List<String> newPlatforms = request.getPlatforms();
        //如果同时存在平台ID=434、433 把两条合并,并且去除434、433
        if (CollectionUtil.isNotEmpty(newPlatforms)) {
            if (newPlatforms.contains(PlatformEnum.App_Android.getCode()) && newPlatforms.contains(PlatformEnum.App_IOS.getCode())) {
                newPlatforms.add(PlatformEnum.App_IOS_Android.getCode());
                newPlatforms.remove(PlatformEnum.App_Android.getCode());
                newPlatforms.remove(PlatformEnum.App_IOS.getCode());
            }
        }
        //获取需要删除的平台
        List<Long> deletePlatformIds = ObjectUtil.isNull(platformList) ? null : platformList.stream()
                .filter(e -> CollectionUtil.isEmpty(newPlatforms) || !newPlatforms.contains(e.getPlatform()))
                .map(OperationPositionPlatform::getId)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deletePlatformIds)) {
            operationPositionPlatformService.getBaseMapper().deleteBatchIds(deletePlatformIds);
        }
        //获取需要新增的平台
        List<String> addPlatforms = ObjectUtil.isNull(newPlatforms) ? null : newPlatforms.stream()
                .filter(e -> CollectionUtil.isEmpty(platformMapping) || !platformMapping.containsKey(e))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(addPlatforms)) {
            operationPositionPlatformService.insertBatch(
                    addPlatforms.stream().map(e ->
                            OperationPositionPlatform.builder()
                                    .id(positionId)
                                    .platform(e)
                                    .positionId(positionId)
                                    .creator(modifier)
                                    .modifier(modifier)
                                    .deleteFlag(DeleteFlagEnum.NOT_DELETE.getCode())
                                    .createTime(currTime)
                                    .modifiedTime(currTime)
                                    .build()
                    ).collect(Collectors.toList()));
        }

        CompletableFuture.runAsync(() -> {
            updateRequiredMaintainedFlag(positionId, modifier);
            updateRandomKey(adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo(), positionId);
            //记录日志
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(ObjectUtil.isNull(request.getId()) ? LogActionEnum.New : LogActionEnum.Update)
                    .logType1("运营点位")
                    .logType2(ObjectUtil.isNull(request.getId()) ? "新增" : "编辑" + "运营点位")
                    .filter(positionId.toString())
                    .content(BeanCompareUtils.compareStr(oldOperationPositionDetail, request))
                    .operateTime(LocalDateTime.now())
                    .operator(modifier)
                    .operatorNum(modifierNo)
                    .build());
        });
    }


    /**
     * 选择操作位置详细信息
     *
     * @param request 请求
     * @return {@link OperationPositionDetailVo }
     */
    @Override
    public OperationPositionDetailVo selectOperationPositionDetail(OperationPositionDetailRo request) {
        OperationPosition operationPosition = baseMapper.selectById(request.getId());
        if (ObjectUtil.isNull(operationPosition)) {
            throw new ApiException("运营点位不存在");
        }
        OperationPositionDetailVo operationPositionDetailVo = new OperationPositionDetailVo();
        BeanUtil.copyProperties(operationPosition, operationPositionDetailVo);
        List<OperationPositionPlatform> platformList = operationPositionPlatformService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<OperationPositionPlatform>()
                        .eq(OperationPositionPlatform::getPositionId, request.getId())
                        .eq(OperationPositionPlatform::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        //处理平台
        List<String> platforms = ObjectUtil.isNull(platformList) ? null : platformList.stream()
                .map(OperationPositionPlatform::getPlatform)
                .collect(Collectors.toList());

        //处理使用方
        if (StringUtils.isNotBlank(operationPosition.getPositionUser())) {
            operationPositionDetailVo.setPositionUsers(CollectionUtil.newArrayList(operationPosition.getPositionUser().split(StringUtils.COMMA)));
        } else {
            operationPositionDetailVo.setPositionUsers(CollectionUtil.newArrayList());
        }

        operationPositionDetailVo.setPlatforms(platforms);
        //处理指定覆盖区域
        List<String> coverAreas = ObjectUtil.isNull(operationPosition.getCoverRegionType()) ? null : CollectionUtil.newArrayList(operationPosition.getCoverRegionType().split(","));
        operationPositionDetailVo.setCoverAreas(coverAreas);
        //处理位置
        parseLocation(operationPosition.getLocation(), operationPositionDetailVo);
        //频道页
        OperationPositionPage positionPage = operationPositionPageService.getById(operationPosition.getPositionPageId());
        operationPositionDetailVo.setPageName(ObjectUtil.isNotNull(positionPage) ? positionPage.getName() : null);
        return operationPositionDetailVo;
    }

    @Override
    public StatisticsVo getStatistics() {
        //总运营点位
        List<OperationPosition> operationPositionList = list(new LambdaQueryWrapper<OperationPosition>()
                .eq(OperationPosition::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
        );
        //必填的运营点位
        List<Long> requiredPositionIdList = operationPositionList.stream().filter(e -> !e.getEmptyFlag().equals(1) && e.getStatus().equals(1)).map(OperationPosition::getId).distinct().collect(Collectors.toList());
        //必填运营点位相关推广计划
        List<OperationPromotionPlan> requiredPromotionPlans = operationPromotionPlanService.list(new LambdaQueryWrapper<OperationPromotionPlan>().select(OperationPromotionPlan::getPositionId)
                .eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .in(CollectionUtil.isNotEmpty(requiredPositionIdList), OperationPromotionPlan::getPositionId, requiredPositionIdList)
        );
        //必填相关运营点位id
        List<Long> requiredPositionId = requiredPromotionPlans.stream().map(OperationPromotionPlan::getPositionId).distinct().collect(Collectors.toList());
        int requiredNotMaintainedCount = 0;
        if (CollectionUtil.isNotEmpty(requiredPositionId)) {
            requiredNotMaintainedCount = requiredPositionIdList.size() - requiredPositionId.size();
        }
        int pageCount = operationPositionPageService.count(new LambdaQueryWrapper<OperationPositionPage>().
                eq(OperationPositionPage::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));

        return StatisticsVo.builder()
                .operationPositionPageCount(pageCount)
                .operationPositionCount(operationPositionList.size())
                .requiredNotMaintainedCount(requiredNotMaintainedCount)
                .expireCount(count(new LambdaQueryWrapper<OperationPosition>()
                        .eq(OperationPosition::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                        .eq(OperationPosition::getStatus, OperationPositionStatusEnum.VALID.getCode())
                        .le(OperationPosition::getExpireDays, ExpireDaysConst.SEVEN_DAYS)
                ))
                .build();
    }

    @Override
    public OperationPositionVo getOperationPosition(Long id) {
//        获取运营点位平台
        OperationPositionVo operationPositionVo = selectOperationPositionPage(OperationPositionListRo.builder()
                .id(id).pageIndex(NumberUtils.INTEGER_ONE)
                .pageSize(NumberUtils.INTEGER_ONE)
                .build())
                .getList().stream().findFirst().orElse(null);

        if (ObjectUtil.isNull(operationPositionVo)) {
            return operationPositionVo;
        }
        operationPositionVo.setDefaultFlag(Boolean.FALSE);
        //补充兜底推广计划信息
        fillDefaultInfo(operationPositionVo);
        //处理推广方式
        List<SelectItemVo> dicSelectItems = dictService.getDicSelectItems(DictTypeEnum.PROMOTE_TYPE.getType());
        dicSelectItems = dicSelectItems.stream().filter(e -> !PromoteTypeEnum.DEFAULT.getCode().toString().equals(e.getValue())).collect(Collectors.toList());
        operationPositionVo.setPromoteTypes(dicSelectItems);
        operationPositionVo.setEditPromoteTypes(dicSelectItems);
        operationPositionVo.setEditPlatforms(operationPositionVo.getPlatforms());

        //如果是兜底推广计划
        if (operationPositionVo.getDefaultFlag()) {
            //处理平台
            List<PlatformVo> platforms = operationPositionVo.getPlatforms();
            platforms = platforms.stream().filter(e -> operationPositionVo.getDefaultInfos().containsKey(e.getCode())).collect(Collectors.toList());
            operationPositionVo.setPlatforms(platforms);

            //处理推广方式
            List<Integer> needDefaultCoverAreas = operationPositionVo.getDefaultInfos()
                    .entrySet()
                    .stream()
                    .flatMap(e -> e.getValue().entrySet().stream()).map(Map.Entry::getKey).collect(Collectors.toList());
            operationPositionVo.setPromoteTypes(dicSelectItems.stream().filter(e -> needDefaultCoverAreas.contains(Integer.valueOf(e.getValue()))).collect(Collectors.toList()));
        }


        //顺位数量
        int indexCount = Math.max(operationPositionVo.getImageMaxCount(), operationPositionVo.getKeywordMaxCount());
        operationPositionVo.setIndexCount(operationPositionVo.getDefaultFlag() ? NumberUtils.INTEGER_ZERO : indexCount == NumberUtils.INTEGER_ONE ? NumberUtils.INTEGER_ZERO : indexCount);
        operationPositionVo.setIndexFlag(!operationPositionVo.getDefaultFlag() && indexCount > NumberUtils.INTEGER_ONE);

        return operationPositionVo;
    }

    private void fillDefaultInfo(OperationPositionVo operationPositionVo) {
        List<OperationPromotionPlan> defaultPlanList = getDefaultPlanList(operationPositionVo.getId(), null, null, null);
        //<平台, 覆盖区域>, 数量>>
        Map<Pair<String, String>, Integer> defaultCountMap;
        if (CollectionUtil.isNotEmpty(defaultPlanList)) {
            defaultCountMap = defaultPlanList.stream().collect(Collectors.groupingBy(e -> Pair.of(e.getPromoteType().toString(), e.getPlatform()), Collectors.summingInt(e -> 1)));
        } else {
            defaultCountMap = new HashMap<>(2);
        }
        //存在不分开维护的推广计划
        if (defaultCountMap.containsKey(new Pair<>(PromoteTypeEnum.DEFAULT.getCode().toString(), PlatformEnum.ALL_PLATFORM.getCode()))) {
            return;
        }
        List<String> coverAreas = Optional.ofNullable(operationPositionVo.getCoverAreas()).orElse(new ArrayList<>(0)).stream().filter(e -> !AreaCoverageEnum.FOREIGN.getCode().equals(e)).collect(Collectors.toList());
        List<PlatformVo> platforms = operationPositionVo.getPlatforms();
        if (CollectionUtil.isNotEmpty(coverAreas) && CollectionUtil.isNotEmpty(platforms) && operationPositionVo.getEmptyFlag().equals(0)) {
            platforms.forEach(platformVo ->
                    coverAreas.forEach(coverArea -> {
                        PromoteTypeEnum promoteType = EnumUtil.getBy(PromoteTypeEnum::getName, coverArea);
                        //兜底推广计划 <平台code, <覆盖区域, 顺位>>
                        Map<Integer, Map<Integer, List<Integer>>> defaultInfos = operationPositionVo.getDefaultInfos();
                        defaultInfos = ObjectUtil.isNull(defaultInfos) ? new HashMap<Integer, Map<Integer, List<Integer>>>(2) : defaultInfos;
                        Map<Integer, List<Integer>> coverInfo = defaultInfos.get(platformVo.getCode());
                        if (CollectionUtil.isEmpty(coverInfo)) {
                            coverInfo = new HashMap<>(1);
                        }
                        List<Integer> indexList = new ArrayList<>(NumberUtils.INTEGER_ONE);
                        int defaultCount = Math.max(defaultCountMap.getOrDefault(Pair.of(promoteType.getCode().toString(), platformVo.getCode().toString()), 0),
                                Math.max(defaultCountMap.getOrDefault(Pair.of(PromoteTypeEnum.DEFAULT.getCode().toString(), platformVo.getCode().toString()), 0),
                                        Math.max(defaultCountMap.getOrDefault(Pair.of(PromoteTypeEnum.DEFAULT.getCode().toString(), PlatformEnum.ALL_PLATFORM.getCode()), 0),
                                                defaultCountMap.getOrDefault(Pair.of(coverArea, PlatformEnum.ALL_PLATFORM.getCode()), 0)
                                        )));
                        if (defaultCount < 1) {
                            //不存在兜底推广计划
                            indexList.add(1);
                            operationPositionVo.setDefaultFlag(Boolean.TRUE);
                        }

                        if (CollectionUtil.isNotEmpty(indexList)) {
                            coverInfo.put(promoteType.getCode(), indexList);
                        }
                        if (CollectionUtil.isNotEmpty(coverInfo)) {
                            defaultInfos.put(platformVo.getCode(), coverInfo);
                        }
                        operationPositionVo.setDefaultInfos(defaultInfos);

                    })
            );

        }
    }

    /**
     * 获取兜底推广计划数
     *
     * @param positionId 位置id
     * @param platformVo 平台vo
     * @param typeEnum   类型枚举
     * @param indexValue 顺位
     * @return int
     */
    private List<OperationPromotionPlan> getDefaultPlanList(Long positionId, PromoteTypeEnum typeEnum, PlatformVo platformVo, Integer indexValue) {
        return operationPromotionPlanService.list(new LambdaQueryWrapper<OperationPromotionPlan>()
                .eq(OperationPromotionPlan::getPositionId, positionId)
                .eq(ObjectUtil.isNotNull(indexValue), OperationPromotionPlan::getIndexValue, indexValue)
                .and(ObjectUtil.isNotNull(platformVo) && ObjectUtil.notEqual(platformVo, PlatformEnum.ALL_PLATFORM), subSQL -> subSQL.eq(OperationPromotionPlan::getPlatform, ObjectUtil.isNotNull(platformVo) ? platformVo.getCode() : null)
                        .or()
                        .eq(OperationPromotionPlan::getPlatform, PlatformEnum.ALL_PLATFORM.getCode())
                )
                .eq(ObjectUtil.isNotNull(typeEnum), OperationPromotionPlan::getPromoteType, ObjectUtil.isNotNull(typeEnum) ? typeEnum.getCode() : null)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getDefaultFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
    }


    /**
     * 根据规则生成位置
     *
     * @param request 请求
     * @return {@link String}
     */
    private String getLocation(OperationPositionEditRo request) {
        return StringUtils.format(OperationPositionRuleConst.LOCATION_FORMAT_RULE,
                request.getModuleValue(),
                ObjectUtil.isNull(request.getVerticalPosition()) ? NumberUtils.INTEGER_ONE : request.getVerticalPosition(),
                ObjectUtil.isNull(request.getVerticalIndex()) ? NumberUtils.INTEGER_ONE : request.getVerticalIndex(),
                ObjectUtil.isNull(request.getHorizontalPosition()) ? NumberUtils.INTEGER_ONE : request.getHorizontalPosition(),
                ObjectUtil.isNull(request.getHorizontalIndex()) ? NumberUtils.INTEGER_ONE : request.getHorizontalIndex());
    }

    /**
     * 解析位置
     */
    private void parseLocation(String location, OperationPositionDetailVo positionDetailVo) {
        String[] locationArr = location.split(StrUtil.UNDERLINE);
        positionDetailVo.setModuleValue(locationArr[0]);
        positionDetailVo.setVerticalPosition(Integer.valueOf(locationArr[1]));
        positionDetailVo.setVerticalIndex(Integer.valueOf(locationArr[2]));
        positionDetailVo.setHorizontalPosition(Integer.valueOf(locationArr[3]));
        positionDetailVo.setHorizontalIndex(Integer.valueOf(locationArr[4]));
    }

    /**
     * 根据规则生成Code
     *
     * @return {@link String}
     */
    private String getCode(Long positionPageId, String location, Integer outputType) {
        return StringUtils.format(OperationPositionRuleConst.CODE_FORMAT_RULE, positionPageId, location, outputType);
    }

    /**
     * 按code获取
     *
     * @param code code
     * @return {@link OperationPosition}
     */
    @LACacheable(key = "'OperationPosition:' + #code", expire = 60 * 5)
    @Override
    public OperationPosition getByCode(String code) {
        return this.getOne(new LambdaQueryWrapper<OperationPosition>()
                .eq(OperationPosition::getCode, code)
                .eq(OperationPosition::getDeleteFlag, false)
                .orderByDesc(OperationPosition::getId));
    }

    /**
     * 需要更新维护标志
     *
     * @param positionId 位置id
     */
    @Override
    public void updateRequiredMaintainedFlag(Long positionId, String modifier) {
        if (ObjectUtil.isNull(positionId)) {
            return;
        }
        //查询点位推平台和覆盖区域
        OperationPosition operationPosition = getOne(new LambdaQueryWrapper<OperationPosition>()
                .select(OperationPosition::getCoverRegionType, OperationPosition::getImageMaxCount, OperationPosition::getRequiredMaintainedFlag)
                .eq(OperationPosition::getId, positionId));
        List<OperationPositionPlatform> platforms = operationPositionPlatformService.list(new LambdaQueryWrapper<OperationPositionPlatform>()
                .eq(OperationPositionPlatform::getPositionId, positionId)
                .eq(OperationPositionPlatform::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (CollectionUtil.isEmpty(platforms) || ObjectUtil.isNull(operationPosition)) {
            return;
        }
        List<String> coverAreas = CollectionUtil.newArrayList(operationPosition.getCoverRegionType().split(","));
        //需要更新的状态
        AtomicBoolean needUpdateFlag = new AtomicBoolean(true);
        //原有状态
        Boolean requiredMaintainedFlag = operationPosition.getRequiredMaintainedFlag();

        SkyLogUtils.infoByMethod(log, StringUtils.format("needDefaultPlan,positionId:{},coverAres:{},platforms:{}", positionId,
                JSON.toJSONString(coverAreas),
                JSON.toJSONString(platforms.stream().map(OperationPositionPlatform::getPlatform).collect(Collectors.toList()))), "", "");

        //<覆盖区域, 平台>, 数量>>
        Map<Pair<String, String>, Integer> defaultCountMap;
        // 预先查询所有需要的数据，并存储在一个Map中
        List<OperationPromotionPlan> defaultPlanList = getDefaultPlanList(positionId, null, null, null);
        if (CollectionUtil.isNotEmpty(defaultPlanList)) {
            defaultCountMap = defaultPlanList.stream().collect(Collectors.toMap(e -> Pair.of(e.getPromoteType().toString(), e.getPlatform()), e -> 1, Integer::sum));

        } else {
            defaultCountMap = new HashMap<>();
        }
        // 遍历判断兜底是否均已维护
        coverAreas.forEach(coverArea -> {
            platforms.forEach(platform -> {
                int defaultCount = Math.max(defaultCountMap.getOrDefault(Pair.of(coverArea, platform.getPlatform()), 0),
                        Math.max(defaultCountMap.getOrDefault(Pair.of(PromoteTypeEnum.DEFAULT.getCode().toString(), platform.getPlatform()), 0),
                                Math.max(defaultCountMap.getOrDefault(Pair.of(PromoteTypeEnum.DEFAULT.getCode().toString(), PlatformEnum.ALL_PLATFORM.getCode()), 0),
                                        defaultCountMap.getOrDefault(Pair.of(coverArea, PlatformEnum.ALL_PLATFORM.getCode()), 0)
                                )));
                SkyLogUtils.infoByMethod(log, StringUtils.format("defaultPlanInfo,positionId:{},coverAre:{},platform:{},count:{}", positionId, coverArea, platform.getPlatform(), defaultCount), "", "");
                if (defaultCount < 1) {
                    needUpdateFlag.set(false);
                }
            });
        });
        if (needUpdateFlag.get() == requiredMaintainedFlag) {
            return;
        }
        //全部兜底推广计划已维护
        boolean flag = update(new LambdaUpdateWrapper<OperationPosition>()
                .set(OperationPosition::getRequiredMaintainedFlag, needUpdateFlag.get())
                .eq(OperationPosition::getId, positionId));
        if (flag) {
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(LogActionEnum.Update)
                    .logType1("运营点位")
                    .logType2("修改兜底维护标识")
                    .filter(positionId.toString())
                    .content("required_maintained_flag：" + requiredMaintainedFlag + "->" + needUpdateFlag)
                    .operateTime(LocalDateTime.now())
                    .operator(modifier.substring(0, modifier.indexOf("[")))
                    .operatorNum(modifier.substring(modifier.indexOf("[") + 1, modifier.indexOf("]")))
                    .build());
        }
    }

    /**
     * 需要更新维护标志
     *
     * @param modifier   修饰符
     * @param positionId 位置id
     */
    @Override
    public void updateRequiredMaintainedFlag(String modifier, Long positionId) {
        //查询点位推平台和覆盖区域
        OperationPosition operationPosition = getOne(new LambdaQueryWrapper<OperationPosition>()
                .select(OperationPosition::getCoverRegionType, OperationPosition::getImageMaxCount, OperationPosition::getRequiredMaintainedFlag)
                .eq(OperationPosition::getId, positionId));
        List<OperationPositionPlatform> platforms = operationPositionPlatformService.list(new LambdaQueryWrapper<OperationPositionPlatform>()
                .eq(OperationPositionPlatform::getPositionId, positionId)
                .eq(OperationPositionPlatform::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (CollectionUtil.isEmpty(platforms) || ObjectUtil.isNull(operationPosition)) {
            return;
        }
        List<String> coverAreas = CollectionUtil.newArrayList(operationPosition.getCoverRegionType().split(StringUtils.COMMA));
        //需要更新的状态
        AtomicBoolean needUpdateFlag = new AtomicBoolean(true);
        //原有状态
        Boolean requiredMaintainedFlag = operationPosition.getRequiredMaintainedFlag();

        SkyLogUtils.infoByMethod(log, StringUtils.format("needDefaultPlan,positionId:{},coverAres:{},platforms:{},indexes:{}", positionId,
                JSON.toJSONString(coverAreas),
                JSON.toJSONString(platforms.stream().map(OperationPositionPlatform::getPlatform).collect(Collectors.toList())),
                operationPosition.getImageMaxCount()), "", "");
        //遍历判断兜底是否均已维护
        coverAreas.forEach(coverArea -> {
            platforms.forEach(platform -> {
                int count = operationPromotionPlanService.count(new LambdaQueryWrapper<OperationPromotionPlan>()
                        .eq(OperationPromotionPlan::getPositionId, positionId)
                        .and(s -> s.eq(OperationPromotionPlan::getPromoteType, coverArea)
                                .or(sb -> sb.eq(OperationPromotionPlan::getPromoteType, PromoteTypeEnum.DEFAULT.getCode()))
                        )
                        .and(subSQL -> subSQL.eq(OperationPromotionPlan::getPlatform, platform.getPlatform())
                                .or()
                                .eq(OperationPromotionPlan::getPlatform, PlatformEnum.ALL_PLATFORM.getCode())
                        )
                        .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                        .eq(OperationPromotionPlan::getDefaultFlag, Boolean.TRUE)
                        .eq(OperationPromotionPlan::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
                SkyLogUtils.infoByMethod(log, StringUtils.format("defaultPlanInfo,positionId:{},coverAre:{},platform:{},count:{}", positionId, coverArea, platform.getPlatform(), count), "", "");
                if (count < 1) {
                    needUpdateFlag.set(false);
                }
            });
        });
        if (needUpdateFlag.get() == requiredMaintainedFlag) {
            return;
        }
        //全部兜底推广计划已维护
        boolean flag = update(new LambdaUpdateWrapper<OperationPosition>()
                .set(OperationPosition::getRequiredMaintainedFlag, needUpdateFlag.get())
                .eq(OperationPosition::getId, positionId));
        if (flag) {
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(LogActionEnum.Update)
                    .logType1("运营点位")
                    .logType2("修改兜底维护标识")
                    .filter(operationPosition.getId().toString())
                    .content("required_maintained_flag：" + requiredMaintainedFlag + "->" + needUpdateFlag)
                    .operateTime(LocalDateTime.now())
                    .operator(modifier.substring(0, modifier.indexOf(StrUtil.BRACKET_START)))
                    .operatorNum(modifier.substring(modifier.indexOf(StrUtil.BRACKET_START) + 1, modifier.indexOf(StrUtil.BRACKET_END)))
                    .build());
        }
    }

    @Override
    public void updateRandomKey(String modifier, String modifierNo, Long positionId) {
        OperationPosition operationPosition = getById(positionId);
        if (ObjectUtil.isNull(operationPosition)) {
            return;
        }
        String randomKey = UUID.randomUUID().toString(Boolean.TRUE);
        LocalDateTime currTime = LocalDateTime.now();
        boolean flag = update(new LambdaUpdateWrapper<OperationPosition>()
                .set(OperationPosition::getRandomKey, randomKey)
                .set(OperationPosition::getModifier, modifier)
                .set(OperationPosition::getModifierNo, modifierNo)
                .set(OperationPosition::getModifiedTime, currTime)
                .eq(OperationPosition::getId, positionId));
        if (flag) {
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(LogActionEnum.Update)
                    .logType1("运营点位")
                    .logType2("修改随机key刷新缓存")
                    .filter(positionId.toString())
                    .content("random_key：" + operationPosition.getRandomKey() + "->" + randomKey)
                    .operateTime(currTime)
                    .operator(modifier)
                    .operatorNum(modifierNo)
                    .build());
        }

    }

    /**
     * 列出优惠券
     *
     * @param type                     类型
     * @param operationPositionModules 操作位置模块
     * @return {@link List}<{@link OperationPositionDto}>
     */
    @Override
    public Map<Integer, List<OperationPositionDto>> listCouponPosition(List<Integer> operationPositionModules, Integer type) {
        if (!operationPositionModules.stream().allMatch(value -> OperationPositionModelEnum.RED_PACKET_OPERATION_POSITION.getCode().equals(value)
                || OperationPositionModelEnum.IM.getCode().equals(value))) {
            return null;
        }

        return list(new LambdaQueryWrapper<OperationPosition>()
                .eq(OperationPosition::getStatus, NumberUtils.INTEGER_ONE)
                .and(e -> e.likeRight(OperationPosition::getLocation, operationPositionModules.get(NumberUtils.INTEGER_ZERO).toString())
                        .or().eq(operationPositionModules.size() > NumberUtils.INTEGER_ONE, OperationPosition::getLocation, operationPositionModules.get(NumberUtils.INTEGER_ONE).toString()))
                .eq(OperationPosition::getOutputType, OperationPositionOutputTypeEnum.RED_PACKET_CONTAINER.getCode())
                .like(OperationPosition::getPositionUser, type))
                .stream()
                .map(e -> {
                    String codeStr = e.getLocation().split(StrUtil.UNDERLINE)[NumberUtils.INTEGER_ZERO];
                    if (StringUtils.isBlank(codeStr)) {
                        return null;
                    }
                    Integer code = Integer.valueOf(codeStr);
                    return OperationPositionDto.builder()
                            .positionCode(e.getCode())
                            .positionId(e.getId())
                            .moduleCode(code)
                            .moduleName(EnumUtil.getBy(OperationPositionModelEnum::getCode, code).getName())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(OperationPositionDto::getModuleCode));
    }

    /**
     * 查询列表
     *
     * @param request
     * @return
     */
    @Override
    public List<OperationPositionVo> queryList(OperationPositionListRo request) {
        LambdaQueryWrapper<OperationPosition> operationPositionQueryWrapper = new LambdaQueryWrapper<>();
        operationPositionQueryWrapper.eq(OperationPosition::getOutputType, request.getOutputType());
        operationPositionQueryWrapper.eq(OperationPosition::getStatus, OperationPositionStatusEnum.VALID.getCode());
        if (request.getPositionUser() != null) {
            operationPositionQueryWrapper.like(OperationPosition::getPositionUser, request.getPositionUser());
        }
        List<OperationPosition> operationPositions = baseMapper.selectList(operationPositionQueryWrapper);
        if (CollectionUtil.isEmpty(operationPositions)) {
            return Collections.emptyList();
        }
        List<Long> positionIdList = operationPositions.stream().map(OperationPosition::getId).collect(Collectors.toList());
        LambdaQueryWrapper<OperationPositionPlatform> operationPositionPlatformQueryWrapper = new LambdaQueryWrapper<>();
        operationPositionPlatformQueryWrapper.in(OperationPositionPlatform::getPositionId, positionIdList);
        List<OperationPositionPlatform> operationPositionPlatforms = operationPositionPlatformService.getBaseMapper().selectList(operationPositionPlatformQueryWrapper);
        Map<Long, List<OperationPositionPlatform>> positionIdMap = operationPositionPlatforms.stream().collect(Collectors.groupingBy(OperationPositionPlatform::getPositionId));

        DictInfo dicInfo = dictService.getDicInfo(DictTypeEnum.PLATFORM.getType());
        Map<String, String> dictValueMap = new HashMap<>();
        if (dicInfo != null && CollectionUtils.isNotEmpty(dicInfo.getDetail())) {
            dictValueMap = dicInfo.getDetail().stream().collect(Collectors.toMap(DictDetailInfo::getValue, DictDetailInfo::getLabel, (a, b) -> a));
        }

        List<OperationPositionVo> operationPositionVos = new ArrayList<>();
        for (OperationPosition operationPosition : operationPositions) {
            OperationPositionVo operationPositionVo = BeanUtil.copyProperties(operationPosition, OperationPositionVo.class);
            List<OperationPositionPlatform> operationPositionPlatformList = positionIdMap.get(operationPosition.getId());
            if (CollectionUtils.isNotEmpty(operationPositionPlatformList)) {
                List<PlatformVo> platformVos = new ArrayList<>();
                for (OperationPositionPlatform operationPositionPlatform : operationPositionPlatformList) {
                    PlatformVo platformVo = new PlatformVo();
                    platformVo.setCode(Integer.valueOf(operationPositionPlatform.getPlatform()));
                    platformVo.setName(dictValueMap.get(operationPositionPlatform.getPlatform()));
                    platformVos.add(platformVo);
                    platformVos.sort(Comparator.comparing(PlatformVo::getCode));
                }
                operationPositionVo.setPlatforms(platformVos);
            }

            operationPositionVos.add(operationPositionVo);
        }
        return operationPositionVos;
    }

    /**
     * 查询红包点位列表
     *
     * @param request 请求
     * @return {@link List}<{@link OperationPositionSelectVo}>
     */
    @Override
    public List<OperationPositionSelectVo> queryCouponPositionList(OperationPositionListRo request) {
        List<OperationPosition> operationPositions = list(new LambdaQueryWrapper<OperationPosition>()
                .eq(OperationPosition::getOutputType, OperationPositionOutputTypeEnum.RED_PACKET_CONTAINER.getCode())
                .eq(OperationPosition::getStatus, OperationPositionStatusEnum.VALID.getCode())
                .like(OperationPosition::getPositionUser, request.getPositionUser()));
        List<OperationPositionSelectVo> operationPositionSelectVos = new ArrayList<>(NumberUtils.INTEGER_TWO);

        if (CollectionUtil.isEmpty(operationPositions)) {
            return operationPositionSelectVos;
        }
        //运营位
        operationPositions.stream().filter(e -> e.getLocation().startsWith(String.valueOf(OperationPositionModelEnum.RED_PACKET_OPERATION_POSITION.getCode()))).forEach(e -> {
            OperationPositionSelectVo operationPositionSelectVo = OperationPositionSelectVo.builder()
                    .label(StringUtils.isNotBlank(e.getAlias()) ? e.getAlias() : e.getName())
                    .value(e.getCode())
                    .build();
            operationPositionSelectVos.add(operationPositionSelectVo);
        });

        String configJson = ConfigUtils.get(ConfigConst.EBK_POSITION_FILTER_FLAG);
        if (StringUtils.isNotBlank(configJson) && Boolean.parseBoolean(configJson)) {
            return operationPositionSelectVos;
        }
        //IM咨询位
        operationPositions.stream().filter(e -> e.getLocation().startsWith(String.valueOf(OperationPositionModelEnum.IM.getCode()))).forEach(e -> {
                    OperationPositionSelectVo operationPositionSelectVo = OperationPositionSelectVo.builder()
                            .label(StringUtils.isNotBlank(e.getAlias()) ? e.getAlias() : e.getName())
                            .value(e.getCode())
                            .isIM(true)
                            .build();
                    operationPositionSelectVos.add(operationPositionSelectVo);
                }
        );
        return operationPositionSelectVos;
    }
}
