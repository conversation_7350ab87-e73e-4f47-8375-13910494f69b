package com.ly.localactivity.marketing.mapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.ClassUtils;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.core.ResolvableType;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description MyBaseMapper
 * @date 2024-04-28
 */
@SuppressWarnings("all")
public interface MyBaseMapper<T> extends LaBaseMapper<T> {

    Integer insertBatchSomeColumn(Collection<T> var1);

    default Boolean deleteByIds(List ids, String modifier, String modifierNo) {
        Class clazz = (Class) ResolvableType.forClass(ClassUtils.getUserClass(this.getClass())).as(MyBaseMapper.class).getGeneric().getType();
        Field[] fields = clazz.getFields();

        //判断是否存在modifire_no
        boolean needNo = Arrays.stream(fields).anyMatch(field -> {
            TableField tableField = field.getAnnotation(TableField.class);
            if (ObjectUtil.isNotNull(tableField) && tableField.exist()) {
                return tableField.value().equals("modifier_no");
            }
            return false;
        });

        //不存在modifire_no 则 modifire = modifire[modifire_no]
        if (!needNo) {
            modifier = modifier + StrUtil.BRACKET_START + modifierNo + StrUtil.BRACKET_END;
        }

        //更新SQL
        StringBuilder sql = new StringBuilder("delete_flag = 1");
        sql.append(StrUtil.C_COMMA)
                .append("modifier = ")
                .append("\"")
                .append(modifier)
                .append("\"")
                .append(StrUtil.C_COMMA)
                .append("modified_time = ")
                .append("\"")
                .append(LocalDateTime.now().toString())
                .append("\"")
                .append(StrUtil.C_COMMA);

        //更新modifire_no
        if (needNo) {
            sql.append("modifier_no").append(modifierNo);
        }

        //条件SQL
        StringBuilder lastSQl = new StringBuilder("and id in (");

        for (int i = 0; i < ids.size(); i++) {
            lastSQl.append(ids.get(i));
            if (i < ids.size() - 1) {
                lastSQl.append(StrUtil.C_COMMA);
            }
        }
        lastSQl.append(")");
        LambdaUpdateWrapper lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.setSql(String.valueOf(sql));
        lambdaUpdateWrapper.last(String.valueOf(lastSQl));
        int update = update(null, lambdaUpdateWrapper);
        return update > 0;

    }

    default Boolean deleteByIds(List ids, String modifier) {
        //处理修改人修改人工号
        String modifier_ = modifier.substring(0, modifier.indexOf(StrUtil.BRACKET_START));
        String modifierNo = modifier.substring(modifier.indexOf(StrUtil.BRACKET_START) + NumberUtils.INTEGER_ONE, modifier.indexOf(StrUtil.BRACKET_END));
        return deleteByIds(ids, modifier_, modifierNo);
    }

    ;

}