package com.ly.localactivity.marketing.external.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * CouponOccupyDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponOccupyDto", description = "占用优惠券返回结果")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponOccupyExtDto {

    private List<CostDeptDto> assumCode;
    /**
     * 券批号
     */
    @ApiModelProperty(value = "券批号")
    private String batchNo;

    /**
     * 使用方法code
     */
    @ApiModelProperty(value = "使用方法code")
    private String usedCode;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 是退款
     */
    @ApiModelProperty(value = "是退款")
    private Integer isRefund;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private Integer sourceDept;
    /**
     * 是否关联银行卡
     */
    @ApiModelProperty(value = "是否关联银行卡")
    private Integer isLinkBank;
    /**
     * 银行卡类型
     */
    @ApiModelProperty(value = "银行卡类型")
    private Integer bankType;
    /**
     * 银行卡code
     */
    @ApiModelProperty(value = "银行卡code")
    private String bankCode;

    /**
     * 折扣
     */
    @ApiModelProperty(value = "折扣")
    private Integer discount;




}
