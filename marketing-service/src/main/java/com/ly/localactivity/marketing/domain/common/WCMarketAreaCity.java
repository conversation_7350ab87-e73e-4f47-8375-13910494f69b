package com.ly.localactivity.marketing.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 营销区域城市表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WCMarketAreaCity")
public class WCMarketAreaCity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "MACId", type = IdType.AUTO)
    private Long MACId;

    /**
     * 区域id
     */
    @TableField("MACMarketAreaId")
    private Long MACMarketAreaId;

    /**
     * 城市id
     */
    @TableField("MACCityId")
    private Long MACCityId;

    /**
     * 城市名称
     */
    @TableField("MACCityName")
    private String MACCityName;

    /**
     * 省id
     */
    @TableField("MACProvinceId")
    private Long MACProvinceId;

    /**
     * 省名称
     */
    @TableField("MACProvinceName")
    private String MACProvinceName;

    /**
     * 国家id
     */
    @TableField("MACCountryId")
    private Long MACCountryId;

    /**
     * 国家名称
     */
    @TableField("MACCountryName")
    private String MACCountryName;

    /**
     * 有效性
     */
    @TableField("MACDataFlag")
    private Integer MACDataFlag;

    /**
     * 创建人姓名
     */
    @TableField("MACCreateName")
    private String MACCreateName;

    /**
     * 创建人工号
     */
    @TableField("MACCreateJobNum")
    private String MACCreateJobNum;

    /**
     * 创建时间
     */
    @TableField("MACCreateDate")
    private LocalDateTime MACCreateDate;

    /**
     * 更新人姓名
     */
    @TableField("MACUpdateName")
    private String MACUpdateName;

    /**
     * 更新人工号
     */
    @TableField("MACUpdateJobNum")
    private String MACUpdateJobNum;

    /**
     * 更新时间
     */
    @TableField("MAUpdateDate")
    private LocalDateTime MAUpdateDate;


}
