package com.ly.localactivity.marketing.common.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.marketing.common.bean.DictDetailInfo;
import com.ly.localactivity.marketing.common.bean.DictInfo;
import com.ly.localactivity.marketing.common.bean.SelectItemVo;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.SysDict;
import com.ly.localactivity.marketing.domain.marketing.SysDictDetail;
import com.ly.localactivity.marketing.mapper.marketing.SysDictDetailMapper;
import com.ly.localactivity.marketing.mapper.marketing.SysDictMapper;
import com.ly.tcbase.config.ConfigCenterClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * DicService
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Component
public class DictService {

    @Resource
    private SysDictDetailMapper sysDictDetailMapper;
    @Resource
    private SysDictMapper sysDictMapper;

    /**
     * 获取dic信息
     *
     * @param typeCode 类型代号
     * @return {@link DictInfo}
     */
    public DictInfo getDicInfo(String typeCode) {
        SysDict sysDict = sysDictMapper
                .selectOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getType, typeCode)
                        .eq(SysDict::getStatus, 1)
                        .eq(SysDict::getDeleteFlag, false)
                        .last("limit 1"));
        if (sysDict == null) {
            throw new ApiException("字典类型不存在");
//            return null;
        }

        List<DictDetailInfo> detailList = sysDictDetailMapper
                .selectList(new LambdaQueryWrapper<SysDictDetail>()
                        .eq(SysDictDetail::getDictType, typeCode)
                        .eq(SysDictDetail::getStatus, 1)
                        .eq(SysDictDetail::getDeleteFlag, false)
                        .orderByDesc(SysDictDetail::getDictSort))
                .stream().map(sysDictDetail -> {
                    DictDetailInfo dictInfo = new DictDetailInfo();
                    dictInfo.setLabel(sysDictDetail.getDictLabel());
                    dictInfo.setValue(sysDictDetail.getDictValue());
                    dictInfo.setSortNo(sysDictDetail.getDictSort());
                    return dictInfo;
                }).collect(java.util.stream.Collectors.toList());

        return DictInfo.builder()
                .name(sysDict.getName())
                .typeCode(sysDict.getType())
                .detail(detailList)
                .build();
    }

//    @LACacheable(key = "'getDicSelectItems:'+#typeCode", expire = 60 * 60)
    public List<SelectItemVo> getDicSelectItems(String typeCode) {
        List<SysDictDetail> sysDictDetails = sysDictDetailMapper
                .selectList(new LambdaQueryWrapper<SysDictDetail>()
                        .eq(SysDictDetail::getDictType, typeCode)
                        .eq(SysDictDetail::getStatus, 1)
                        .eq(SysDictDetail::getDeleteFlag, false)
                        .orderByDesc(SysDictDetail::getDictSort));
        return sysDictDetails.stream().map(sysDictDetail -> SelectItemVo.builder()
                .label(sysDictDetail.getDictLabel())
                .value(sysDictDetail.getDictValue())
                .build()).collect(java.util.stream.Collectors.toList());
    }

    public Object getCommonConfig(String type) {
        String string = ConfigUtils.get(type);
        if (StringUtils.isNotBlank(string)) {
            return JSON.parseObject(string);
        }
        return null;
    }

}
