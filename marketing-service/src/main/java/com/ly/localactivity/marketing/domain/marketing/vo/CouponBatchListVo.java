package com.ly.localactivity.marketing.domain.marketing.vo;

import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRuleDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * CouponBatchListVo
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@ApiModel(value = "CouponBatchListVo", description = "红包列表信息")
@Data
public class CouponBatchListVo implements Serializable {
    private static final long serialVersionUID = 5376956161421209364L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包类型，1-平台红包，2-商家红包
     */
    @ApiModelProperty(value = "红包类型，1-平台红包，2-商家红包")
    private Integer type;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 日游产品品类
     */
    @ApiModelProperty(value = "日游产品品类")
    private Integer productCategoryId;

    /**
     * 红包名称
     */
    @ApiModelProperty(value = "红包名称")
    private String name;

    /**
     * 红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架
     */
    @ApiModelProperty(value = "红包状态，1-未提交，2-未生效（待审核），3-已生效，4-同程下架，5-商家下架")
    private Integer status;

    /**
     * 营销状态 1-未生效，2-活动中，3-未开始，4-已过期，5-已下架
     */
    @ApiModelProperty(value = "营销状态 1-未生效，2-活动中，3-未开始，4-已过期，5-已下架")
    private Integer marketingStatus;

    /**
     * 审核状态，1-未提交 2-待审核，3-审核通过，4-审核驳回
     */
    @ApiModelProperty(value = "审核状态，1-未提交 2-待审核，3-审核通过，4-审核驳回")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditComment;

    /**
     * 审核人工号
     */
    @ApiModelProperty(value = "审核人工号")
    private String auditUserNo;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String auditUser;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 供应商id，对应common库的supplier表.supplierId
     */
    @ApiModelProperty(value = "供应商id，对应common库的supplier表.supplierId")
    private Long supplierId;

    private String supplierName;

    /**
     * 无效/下架原因
     */
    @ApiModelProperty(value = "无效/下架原因")
    private String invalidReason;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 修改人工号
     */
    @ApiModelProperty(value = "修改人工号")
    private String modifierNo;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifiedTime;

    /**
     * 关联在售产品数量
     */
    @ApiModelProperty(value = "关联在售产品数量")
    private Integer canSaleProductCount;

    @ApiModelProperty(value = "限制类型 1-全部商品 2-指定出发地 3-指定二级品类 4-指定商品")
    private Integer limitType;

    @ApiModelProperty("红包批次详情数量")
    private Integer couponBatchDetailCount;

    @ApiModelProperty("红包批次详情")
    private List<CouponBatchDetailDto> couponBatchDetailList;

    @ApiModelProperty("红包点位")
    private List<CouponBatchOperationDto> couponBatchOperationList;

    @ApiModelProperty("红包规则")
    private CouponBatchRuleDto couponBatchRule;

}
