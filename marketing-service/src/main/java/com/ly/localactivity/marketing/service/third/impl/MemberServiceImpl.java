package com.ly.localactivity.marketing.service.third.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.marketing.domain.third.member.MemberInfoEntity;
import com.ly.localactivity.marketing.domain.third.member.MemberResultEntity;
import com.ly.localactivity.marketing.external.coupon.service.HttpClientUtil;
import com.ly.localactivity.marketing.service.third.IMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MemberServiceImpl implements IMemberService {
    /**
     * 会员三方url前缀
     */
    @Value("${tcMember.thirdParty.api.url}")
    private String MEMBER_THIRD_PARTY_API_URL;
    @Value("${tcMember.thirdParty.api.appKey}")
    private String MEMBER_THIRD_PARTY_API_APP_KEY;
    @Value("${tcMember.thirdParty.api.appSecret}")
    private String MEMBER_THIRD_PARTY_API_APP_SECRET;


    /**
     * 获取 会员集合
     *
     * @param unionId
     * @param memberId
     * @return
     */
    public List<MemberInfoEntity> getAllMemberInfoList(String unionId, Long memberId) {
        List<MemberInfoEntity> memberInfoList = new ArrayList<>();
        if (StringUtils.isNotBlank(unionId)) {
            memberInfoList = getListByUnionId(unionId);
        } else if (memberId != null && memberId.compareTo(0L) > 0) {
            memberInfoList = getListByMemberId(memberId);
        }
        if (CollectionUtils.isEmpty(memberInfoList)) {
            return null;
        }
        //从小到大排序
        memberInfoList.sort(Comparator.comparingInt(MemberInfoEntity::getMemberSystem));
        return memberInfoList;
    }

    /**
     * 通过unionId 获取会员信息
     *
     * @param unionId
     * @return
     */
    private List<MemberInfoEntity> getListByUnionId(String unionId) {
        if (StringUtils.isEmpty(unionId)) {
            return null;
        }
        // 调用会员研发组的接口获取用户绑定关系
        String url = MEMBER_THIRD_PARTY_API_URL + "/wechat/getRelationInfoList" + "?unionId=" + unionId;
        Map<String, String> headers = CollectionUtils.newHashMap(2);
        headers.put("AppKey", MEMBER_THIRD_PARTY_API_APP_KEY);
        headers.put("AppSecret", MEMBER_THIRD_PARTY_API_APP_SECRET);
        String responseJson = HttpClientUtil.get(url, headers, null);
        if (StringUtils.isEmpty(responseJson)) {
            log.warn("unionId换取用户信息失败,接口返回空，unionId:" + unionId);
            return null;
        }
        MemberResultEntity<List<MemberInfoEntity>> memberResultEntity = null;
        try {
            memberResultEntity = JSON.parseObject(responseJson, new TypeReference<MemberResultEntity<List<MemberInfoEntity>>>() {});
        } catch (Exception ex) {
            log.warn("getListByUnionId error", ex);
            return null;
        }
        if (memberResultEntity == null || !StringUtils.equals(memberResultEntity.getCode(), "0") || memberResultEntity.getData().size() == 0) {
            log.warn("unionId换取用户信息失败,接口状态码!=0或者没有会员信息。响应：" + responseJson);
            return null;
        }
        return memberResultEntity.getData();
    }

    /**
     * 通过memberId 获取会员信息
     * @param memberId
     * @return
     */
    public List<MemberInfoEntity> getListByMemberId(Long memberId) {
        if (memberId == null) {
            return null;
        }
        // 调用会员研发组的接口获取用户绑定关系
        String url = MEMBER_THIRD_PARTY_API_URL + "/wechat/getListByMemberId" + "?memberId=" + memberId;
        Map<String, String> headers = CollectionUtils.newHashMap(2);
        headers.put("AppKey", MEMBER_THIRD_PARTY_API_APP_KEY);
        headers.put("AppSecret", MEMBER_THIRD_PARTY_API_APP_SECRET);
        String responseJson = HttpClientUtil.get(url, headers, null);
        if (StringUtils.isEmpty(responseJson)) {
            return null;
        }
        MemberResultEntity<List<MemberInfoEntity>> memberResultEntity = null;
        try {
            memberResultEntity = JSON.parseObject(responseJson, new TypeReference<MemberResultEntity<List<MemberInfoEntity>>>() {});
        } catch (Exception e) {
            log.warn("getListByMemberId error", e);
        }
        List<MemberInfoEntity> memberInfoList;
        if (memberResultEntity == null || !StringUtils.equals(memberResultEntity.getCode(), "0") || memberResultEntity.getData().size() == 0) {
            log.warn("unionId换取用户信息失败,接口状态码!=0或者没有会员信息。响应：" + responseJson);
            memberInfoList = new ArrayList<>();
        } else {
            memberInfoList = memberResultEntity.getData();
        }
        // 补全member信息
        if (CollectionUtils.isEmpty(memberInfoList)) {
            MemberInfoEntity memberInfo = new MemberInfoEntity();
            memberInfo.setMemberId(memberId);
            memberInfoList.add(memberInfo);
        }
        return memberInfoList;
    }
}
