package com.ly.localactivity.marketing.service.coupon;

import com.ly.localactivity.marketing.common.bean.CommonResultDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;

/**
 * 红包发送服务
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
public interface ICouponRechargeService {
    /**
     * 发送红包
     *
     * @param couponRechargeDto
     * @param repeatCheck
     */
    CommonResultDto recharge(CouponRechargeDto couponRechargeDto, Boolean repeatCheck);
}
