package com.ly.localactivity.marketing.common.handler.promotionPlanParam;

import cn.hutool.core.util.ObjectUtil;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionPlanSingleEditRo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;

/**
 * <AUTHOR>
 * @description AbstractCheckHandler
 * @date 2024-04-08
 */
@Getter
@Component
@Slf4j
public abstract class AbstractCheckHandler {
    /**
     * 当前处理器持有下一个处理器的引用
     */
    @Setter
    protected AbstractCheckHandler nextHandler;



    /**
     * 处理器执行方法
     *
     * @param param param
     */
    public abstract void handle(OperationPromotionPlanSingleEditRo param, Set<String> accumulationSet);

    /**
     * 链路传递
     *
     * @param thisHandlerName 此处理程序名称
     * @param param           param
     */
    protected void next(String thisHandlerName, OperationPromotionPlanSingleEditRo param,Set<String> accumulationSet) {
        //下一个链路没有处理器了，直接返回
        if (ObjectUtil.isNull(nextHandler)) {
            SkyLogUtils.infoByMethod(log, StringUtils.format("{}[end],next->null ,paramCheckedEnd)", thisHandlerName), "", "");
            return;
        }
        SkyLogUtils.infoByMethod(log, StringUtils.format("{}[end],next->)", thisHandlerName, nextHandler.getClass().getSimpleName()), "", "");
        //执行下一个处理器
        nextHandler.handle(param,accumulationSet);
    }
}
