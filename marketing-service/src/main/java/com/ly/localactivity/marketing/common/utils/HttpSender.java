package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * HttpSendUtils
 *
 * <AUTHOR>
 * @date 2024/5/27
 */
@Slf4j
public class HttpSender {
    private String requestId;
    private static final int DEFAULT_TIMEOUT = 5000;
    private String url;
    private Object body;
    private int timeout;
    private Map<String, String> headers;
    private String typeCode;
    private Map<String, Object> form;

    public static HttpSender create() {
        return new HttpSender();
    }

    public HttpSender url(String url) {
        this.url = url;
        return this;
    }

    public HttpSender body(Object body) {
        this.body = body;
        return this;
    }

    public HttpSender timeout(int timeout) {
        this.timeout = timeout;
        return this;
    }

    public HttpSender headers(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    public HttpSender typeCode(String typeCode) {
        this.typeCode = typeCode;
        return this;
    }

    public HttpSender form(Map<String, Object> form) {
        this.form = form;
        return this;
    }

    public HttpSender requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public <T> T doPost(TypeReference<T> typeReference) {
        String result = doPost();
        if (StringUtils.isBlank(result)) {
            return null;
        }
        try {
            return JSON.parseObject(result, typeReference);
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, StringUtils.format("HttpSender#doPostData parse error type:{}###request:{}###url:{}", typeCode, JSON.toJSONString(body), url), typeCode, requestId, e);
            return null;
        }
    }

    public <T> T doGet(TypeReference<T> typeReference) {
        String result = doGet();
        if (StringUtils.isBlank(result)) {
            return null;
        }
        try {
            return JSON.parseObject(result, typeReference);
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, StringUtils.format("HttpSender#doGetData parse error type:{}###request:{}###url:{}", typeCode, JSON.toJSONString(body), url), typeCode, requestId, e);
            return null;
        }
    }

    public String doPost() {
        String bodyStr = "";
        long start = System.currentTimeMillis();
        try {
            HttpRequest postRequest = HttpUtil.createPost(url);
            if (ObjectUtil.isNotNull(body)) {
                bodyStr = JSON.toJSONString(body);
                postRequest.body(bodyStr);
            }
            if (timeout > 0) {
                postRequest.timeout(timeout);
            } else {
                postRequest.timeout(DEFAULT_TIMEOUT);
            }
            if (MapUtil.isNotEmpty(headers)) {
                postRequest.addHeaders(headers);
            }
            if (MapUtil.isNotEmpty(form)) {
                postRequest.form(form);
            }
            String result = postRequest.execute().body();
            SkyLogUtils.infoByMethod(log, StringUtils.format("HttpSender#post type:{}###time:{}ms###request:{}###response:{}###url:{}", typeCode, System.currentTimeMillis() - start, bodyStr, result, url), typeCode, requestId);
            return result;
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, StringUtils.format("HttpSender#post error type:{}###request:{}###url:{}", typeCode, bodyStr, url), typeCode, requestId, e);
            return null;
        }
    }

    public String doGet() {
        String bodyStr = "";
        long start = System.currentTimeMillis();
        try {
            HttpRequest getRequest = HttpUtil.createGet(url);
            if (ObjectUtil.isNotNull(body)) {
                bodyStr = JSON.toJSONString(body);
                getRequest.body(bodyStr);
            }
            if (timeout > 0) {
                getRequest.timeout(timeout);
            } else {
                getRequest.timeout(DEFAULT_TIMEOUT);
            }
            if (MapUtil.isNotEmpty(headers)) {
                getRequest.addHeaders(headers);
            }
            if (MapUtil.isNotEmpty(form)) {
                getRequest.form(form);
            }
            String result = getRequest.execute().body();
            SkyLogUtils.infoByMethod(log, StringUtils.format("HttpSender#get type:{}###time:{}ms###request:{}###response:{}###url:{}", typeCode, System.currentTimeMillis() - start, bodyStr, result, url), typeCode, requestId);
            return result;
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, StringUtils.format("HttpSender#get error type:{}###request:{}###url:{}", typeCode, bodyStr, url), typeCode, requestId, e);
            return null;
        }
    }
}
