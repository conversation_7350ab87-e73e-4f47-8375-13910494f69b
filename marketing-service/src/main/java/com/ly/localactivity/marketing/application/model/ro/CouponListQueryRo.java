package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * CouponSearchRo
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponListQueryRo extends BaseCouponRo{
    @ApiModelProperty(value = "限制条件，resourceIds:资源id,cityIds:出发城市id,secondCategorys:二级品类，多个逗号分隔")
    private CouponLimitRo limitData;
    @ApiModelProperty(value = "批次号列表，传了就只查这些批次号的红包，不传会查询本地所有可使用的红包")
    private List<String> batchNos;
}
