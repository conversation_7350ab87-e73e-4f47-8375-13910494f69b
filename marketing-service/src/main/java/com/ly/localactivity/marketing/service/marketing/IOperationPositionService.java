package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPositionDto;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionDetailRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionEditRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionDetailVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionSelectVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import com.ly.localactivity.marketing.domain.marketing.vo.StatisticsVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 营销运营位 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface IOperationPositionService extends IService<OperationPosition> {

    /**
     * 查询运营位列表
     *
     * @param request 请求
     * @return {@link CommonPage}<{@link OperationPositionVo}>
     */
    CommonPage<OperationPositionVo> selectOperationPositionPage(OperationPositionListRo request);

    /**
     * 运营点位编辑
     *
     * @param request 请求
     */
    void editOperationPosition(OperationPositionEditRo request);


    /**
     * 选择操作位置详细信息
     * 查询运营点位详情详细信息
     *
     * @param request 请求
     * @return {@link OperationPositionDetailVo}
     */
    OperationPositionDetailVo selectOperationPositionDetail(OperationPositionDetailRo request);

    /**
     * 获取统计信息
     *
     * @return {@link StatisticsVo}
     */
    StatisticsVo getStatistics();

    /**
     * 获取运营点位
     *
     * @param id id
     * @return {@link OperationPositionVo}
     */
    OperationPositionVo getOperationPosition(Long id);


    /**
     * 按code获取
     *
     * @param code code
     * @return {@link OperationPosition}
     */
    OperationPosition getByCode(String code);

    /**
     * 需要更新维护标志
     *
     * @param positionId 位置id
     * @param modifier   修饰符
     */
    void updateRequiredMaintainedFlag(Long positionId,String modifier);

    /**
     * 需要更新维护标志
     *
     * @param positionId 位置id
     * @param modifier   修饰符
     */
    void updateRequiredMaintainedFlag(String modifier,Long positionId);

    void updateRandomKey(String modifier, String modifierNo, Long positionId);

    /**
     * 列出优惠券
     *
     * @param operationPositionModules 位置代码类型
     * @param type                     类型
     * @return {@link List}<{@link OperationPosition}>
     */
    Map<Integer, List<OperationPositionDto>> listCouponPosition(List<Integer> operationPositionModules, Integer type);

    /**
     * 查询列表
     *
     * @param request
     * @return
     */
    List<OperationPositionVo> queryList(OperationPositionListRo request);

    /**
     * 查询红包点位列表
     *
     * @param request 请求
     * @return {@link List}<{@link OperationPositionSelectVo}>
     */
    List<OperationPositionSelectVo> queryCouponPositionList(OperationPositionListRo request);
}
