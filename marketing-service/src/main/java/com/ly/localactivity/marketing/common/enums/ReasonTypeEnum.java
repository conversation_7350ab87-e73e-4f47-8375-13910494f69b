package com.ly.localactivity.marketing.common.enums;

import com.ly.localactivity.marketing.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * ReasonTypeEnum
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Getter
@AllArgsConstructor
public enum ReasonTypeEnum {
    CASH_AD("cash_ad", "现金广告"),
    SEASON_PROMOTION("season_promotion", "时令大促"),
    CATEGORY_PROMOTION("category_promotion", "品类小促"),
    MERCHANT_PERMUTE("merchant_permute", "商家置换"),
    CROSS_PERMUTE("cross_permute", "交叉置换"),
    COMMON_OPERATE("common_operate", "常规运营"),

    ;
    private String code;
    private String name;

    public static String codeToName(String code) {
        ReasonTypeEnum reasonTypeEnum = codeOf(code);
        return Objects.isNull(reasonTypeEnum) ? null : reasonTypeEnum.getName();
    }
    public static ReasonTypeEnum codeOf(String code) {
        if (StringUtils.isEmpty(code)){
            return null;
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }

}
