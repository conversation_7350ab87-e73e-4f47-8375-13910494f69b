package com.ly.localactivity.marketing.external.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BatchListInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
public class CouponBatchExtDto {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private List<ProjectDto> projectList;

    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    private Integer couponType;

    /**
     * 批次名称
     */
    @ApiModelProperty(value = "批次名称")
    private String batchName;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 门槛
     */
    @ApiModelProperty(value = "门槛")
    private BigDecimal sill;

    /**
     * 券简述 使用提醒
     */
    @ApiModelProperty(value = "券简述")
    private String brief;
    /**
     * 券详情 使用说明
     */
    @ApiModelProperty(value = "券详情")
    private String detail;

    /**
     * 有效开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "有效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expBeginDate;
    /**
     * 有效截止时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expEndDate;

    /**
     * 个人领取限制
     */
    @ApiModelProperty(value = "个人领取限制")
    private Integer personLimit;

    /**
     * 总库存
     */
    @ApiModelProperty(value = "总库存")
    private Integer totalStock;
    /**
     * 剩余库存
     */
    @ApiModelProperty(value = "剩余库存")
    private Integer remainStock;

    /**
     * 有效期类型
     * 1 固定有效期，2动态有效期,3.倒计时（按小时）4.倒计时（按分钟）
     */
    @ApiModelProperty(value = "有效期类型")
    private Integer expType;

    /**
     * 动态有效开始天
     */
    @ApiModelProperty(value = "动态有效开始天")
    private Integer expBeginDays;
    /**
     * 动态有效持续天数
     */
    @ApiModelProperty(value = "动态有效持续天数")
    private Integer expEndDays;

    /**
     * 0 新建 1 审批中 2 审批通过 3 已驳回 4 已取消 5 无效
     */
    @ApiModelProperty(value = "批次状态")
    private Integer batchStatus;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private Integer sourceDept;

    /**
     * 成本分摊部门
     */
    @ApiModelProperty(value = "成本分摊部门")
    private List<CostDeptDto> costDept;

    /**
     * 是否可退， 0 可退， 1 不可退
     */
    @ApiModelProperty(value = "是否可退")
    private Integer isRefund;

    /**
     * 是否付出 0不需要付出，1需要付出
     */
    @ApiModelProperty(value = "是否付出")
    private Integer isUserPay;

    /**
     * 折扣值70代表7折
     */
    @ApiModelProperty(value = "折扣值70代表7折")
    private Integer discount;

    /**
     * 用户一共可以领几次券,不是当前还可以领几次
     */
    @ApiModelProperty(value = "用户一共可以领几次券,不是当前还可以领几次")
    private Integer times;

    /**
     * 内部名称
     */
    @ApiModelProperty(value = "内部名称")
    private String code;

    /**
     * 扩展属性列表
     */
    @ApiModelProperty(value = "扩展属性列表")
    private List<ExtendInfoDto> extendList;

    /**
     * 是否限制同一手机使用
     * 0-否,1-是
     */
    @ApiModelProperty(value = "是否限制同一手机使用")
    private String phoneLimit;

    /**
     * 特殊规则
     */
    @ApiModelProperty(value = "特殊规则")
    private UseRuleListDto useRuleList;

    /**
     * 批次有效开始时间
     */
    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginDate;
    /**
     * 批次失效时间
     */
    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 倒计时小时或者分钟
     */
    @ApiModelProperty(value = "倒计时小时或者分钟")
    private Integer expEndHours;

    /**
     * 是否无敌红包
     */
    private Integer isSupercoupon;

    /**
     * 发放形式 0：线上 1：兑换码 2：礼包兑换码
     */
    private Integer isOffline;

    /**
     * 是否关联银行卡
     */
    private Integer isLinkBank;

    /**
     * 银行卡类型0：不限 1：储蓄卡 2：信用卡
     */
    private Integer bankType;

    /**
     * 银行code
     */
    private String bankCode;

    /**
     * 备注
     */
    private String remark;

    private String wechatCardId;

    /**
     * 同程APP使用地址
     */
    private String appHref;

    /**
     * H5使用地址
     */
    private String h5Href;

    /**
     * 小程序使用地址
     */
    private String mapphref;

    /**
     * 商品券类型
     */
    private Integer productType;

    /**
     * 新老客类型
     */
    private Integer memberType;

    /**
     * 每日领取限制
     */
    private Long dayLimit;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 0:普通券 1：商家券 2：私域券
     */
    private Integer couponsType;

    /**
     * 领取入口:不限制（0）/列表页（1）/详情页（2）/订单填写页（3）/销售系统（4）
     */
    private List<Integer> takeEntrance;

    /**
     * 是否红包白名单: 0否1是
     */
    private Integer isCouponWhitelist;

    /**
     * 是否互斥立减:0否1是
     */
    private Integer isMutualExclusion;

    /**
     * 是否外部采购:0否1是
     */
    private Integer isOutPurchase;

    /**
     * 最高立减金额
     */
    @ApiModelProperty(value = "最高立减金额")
    private BigDecimal maxDiscountAmount;
}
