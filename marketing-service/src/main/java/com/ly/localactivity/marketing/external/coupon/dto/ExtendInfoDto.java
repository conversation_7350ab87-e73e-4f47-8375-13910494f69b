package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel(value = "ExtendInfoDto", description = "扩展信息")
public class ExtendInfoDto {
    /**
     * 属性id(1 币种)(2 酒店阶梯红包)（3.鲲鹏阶梯）
     */
    @ApiModelProperty(value = "属性id(1 币种)(2 酒店阶梯红包)（3.鲲鹏阶梯）")
    private Integer attrId;
    /**
     * attrId=1:币种值——USD 美元，THB泰铢
     * <p>
     * attrId=3: 1：面额 2：票量
     */
    @ApiModelProperty(value = "attrId=1:币种值——USD 美元，THB泰铢attrId=3: 1：面额 2：票量")
    private String attrValue;
    /**
     * attrId=1:对应币种
     * <p>
     * attrId=3 : ( attrValue=1 面额门槛，attrValue=2 票量门槛）
     */
    @ApiModelProperty(value = "attrId=1:对应币种 attrId=3 : ( attrValue=1 面额门槛，attrValue=2 票量门槛）")
    private String leftValue;

    /**
     * attrId=1:对应币种抵扣金额
     * <p>
     * attrId=3 （attrValue=1 对应红包金额/折扣率,attrValue=2 红包金额/折扣率
     */
    @ApiModelProperty(value = "attrId=1:对应币种抵扣金额 attrId=3 （attrValue=1 对应红包金额/折扣率,attrValue=2 红包金额/折扣率")
    private String rightValue;
    /**
     * attrId=1:对应币种红包名称
     * <p>
     * attrId=3: 最高折扣金额
     */
    @ApiModelProperty(value = "attrId=1:对应币种红包名称 attrId=3: 最高折扣金额")
    private String ext1;
    /**
     * attrId=1:对应币种使用说明
     */
    @ApiModelProperty(value = "attrId=1:对应币种使用说明")
    private String ext2;


}