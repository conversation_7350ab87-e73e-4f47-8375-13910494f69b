package com.ly.localactivity.marketing.domain.third.member;


/**
 * 会员信息
 *
 */
public class MemberInfoEntity {
    /**
     * unionId
     */
    private String unionId = "";

    /**
     * openId
     */
    private String openId = "";

    /**
     * 绑定的同程会员Id
     */
    private Long memberId = 0L;

    /**
     * 会员体系 0:同程 33:微信
     */
    private Integer memberSystem = -1;

    /**
     * 绑定平台
     */
    private String bindPlat = "";

    /**
     * 绑定时间
     */
    private String bindDate = "";

    public String getUnionId() {
        return this.unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOpenId() {
        return this.openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getMemberId() {
        return this.memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getMemberSystem() {
        return this.memberSystem;
    }

    public void setMemberSystem(Integer memberSystem) {
        this.memberSystem = memberSystem;
    }

    public String getBindPlat() {
        return this.bindPlat;
    }

    public void setBindPlat(String bindPlat) {
        this.bindPlat = bindPlat;
    }

    public String getBindDate() {
        return this.bindDate;
    }

    public void setBindDate(String bindDate) {
        this.bindDate = bindDate;
    }
}
