package com.ly.localactivity.marketing.service.coupon;

import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;

import java.util.List;

/**
 * ICouponQueryService
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
public interface ICouponQueryService {

    /**
     * 获取红包批次信息
     *
     * @param batchNo 批号
     * @return {@link CouponBatchCacheDto}
     */
    CouponBatchCacheDto getCouponBatch(String batchNo);

    /**
     * 获取红包批次信息-查询鲲鹏接口
     *
     * @param batchNo 批号
     * @return {@link CouponBatchExtDto}
     */
    CouponBatchExtDto getExternalCoupon(String batchNo);

    /**
     * 获取优惠券批次
     *
     * @param batchNos 批号
     * @param useCache
     * @return {@link List}<{@link CouponBatchCacheDto}>
     */
    List<CouponBatchCacheDto> getCouponBatch(List<String> batchNos, boolean useCache);

    /**
     * 刷新缓存
     *
     * @param batchNo 批号
     */
    int refreshCache(String batchNo);

    /**
     * 刷新缓存
     *
     * @param batchNo 批号
     * @param env     环境
     */
    void refreshCache(String batchNo, String env);

    /**
     * 获取外部优惠券批次列表-查询鲲鹏接口
     *
     * @param batchNos 批号
     * @return {@link List}<{@link CouponBatchExtDto}>
     */
    List<CouponBatchExtDto> getExternalCouponBatchList(List<String> batchNos);

    /**
     * 获取db红包批次详情
     *
     * @param batchNo
     * @return
     */
    CouponBatchCacheDto getBatchDetail(String batchNo, Boolean ignoreCache);

    void refreshCacheByBatchNo(String batchNo, String env);
}
