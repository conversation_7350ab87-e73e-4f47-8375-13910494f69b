package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * 红包占用恢复
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRecoverRo extends BaseCouponRo{
    @NotBlank(message = "优惠券码不能为空")
    @ApiModelProperty(value = "优惠券码")
    private String couponCode;

}
