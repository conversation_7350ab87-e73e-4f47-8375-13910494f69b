package com.ly.localactivity.marketing.external.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * UseRuleInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "UseRuleInfoDto", description = "使用规则")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UseRuleInfoDto {
    private String weekday;
    private String dTimeType;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dTimeBegin;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String dTimeEnd;

}
