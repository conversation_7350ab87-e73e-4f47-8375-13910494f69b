package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 红包使用规则明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_batch_rule_detail")
public class CouponBatchRuleDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包批次id
     */
    @TableField("coupon_batch_id")
    private Long couponBatchId;

    /**
     * 红包规则id
     */
    @TableField("coupon_batch_rule_id")
    private Long couponBatchRuleId;

    /**
     * 商品限制类型，2-指定出发地，2-指定二级品类，3-指定商品
     */
    @TableField("limit_type")
    private Integer limitType;

    /**
     * 数据id，对应地区id、品类id、商品id
     */
    @TableField("data_id")
    private String dataId;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
