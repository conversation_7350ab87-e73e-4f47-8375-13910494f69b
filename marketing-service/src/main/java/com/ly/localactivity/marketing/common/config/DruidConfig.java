package com.ly.localactivity.marketing.common.config;

import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * DruidConfig
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@Configuration
public class DruidConfig {
    /**
     * 解决discard long time none received connection异常
     */
    @PostConstruct
    public void setProperties() {
        System.setProperty("druid.mysql.usePingMethod", "false");
    }
}
