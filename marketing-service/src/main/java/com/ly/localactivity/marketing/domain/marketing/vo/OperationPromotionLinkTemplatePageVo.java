package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * OperationPromotionLinkTemplatePageVo
 *
 * <AUTHOR>
 * @date 2024-4-9
 */
@Data
@ApiModel(value = "OperationPromotionLinkTemplatePageVo",description = "链接模板分页返回参数")
public class OperationPromotionLinkTemplatePageVo {

    /**
     * 链接模板列表
     */
    @ApiModelProperty(value = "链接模板列表")
    private List<OperationPromotionLinkTemplateVo> operationPromotionLinkTemplateVos;

    /**
     * 链接模板数
     */
    @ApiModelProperty(value = "链接模板数")
    private Long total;

}
