package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 运营推广链接模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("operation_promotion_link_template")
public class OperationPromotionLinkTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 自定义名称
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 页面，数据字典
     */
    @TableField("page_code")
    private String pageCode;

    /**
     * 平台code，数据字典
     */
    @TableField("platform")
    private String platform;

    /**
     * 推广链接
     */
    @TableField("link_url")
    private String linkUrl;

    /**
     * 是否自动验证下架，1-是，0-否
     */
    @TableField("auto_invalid_flag")
    private Integer autoInvalidFlag;

    /**
     * 自动验证下架的参数code，多个逗号间隔
     */
    @TableField("auto_invalid_param_codes")
    private String autoInvalidParamCodes;

    /**
     * 状态，1-有效，0-无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 有效期类型，1-长期有效，2-固定日期段
     */
    @TableField("valid_date_type")
    private Integer validDateType;

    /**
     * 有效期开始
     */
    @TableField("valid_begin_date")
    private LocalDateTime validBeginDate;

    /**
     * 有效期结束
     */
    @TableField("valid_end_date")
    private LocalDateTime validEndDate;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;

    /**
     * 链接类型，1-原生链接，2-webview链接
     */
    @TableField("link_url_type")
    private Integer linkUrlType;

    /**
     * 链接前缀
     */
    @TableField("link_url_prefix")
    private String linkUrlPrefix;


}
