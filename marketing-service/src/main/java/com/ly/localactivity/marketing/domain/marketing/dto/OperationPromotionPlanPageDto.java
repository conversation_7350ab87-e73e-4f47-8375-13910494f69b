package com.ly.localactivity.marketing.domain.marketing.dto;

import com.ly.localactivity.marketing.domain.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanPageDto
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@Data
@ApiModel("推广计划分页查询对象")
public class OperationPromotionPlanPageDto extends BasePageRequest {
    @ApiModelProperty(value = "运营点位id")
    @NotNull(message = "运营点位id不能为空")
    private Long positionId;
    @ApiModelProperty(value = "关键词")
    private String keyword;
    @ApiModelProperty(value = "平台")
    private List<String> platforms;
    @ApiModelProperty(value = "推广方式")
    private Integer promoteType;
    @ApiModelProperty(value = "城市ids")
    private List<Long> cityIds;
    @ApiModelProperty(value = "省份ids")
    private List<Long> provinceIds;
    @ApiModelProperty(value = "区域ids")
    private List<Long> areaIds;
    @ApiModelProperty(value = "别名")
    private String alias;
    @ApiModelProperty(value = "开始日期")
    private LocalDate validStartDate;
    @ApiModelProperty(value = "结束日期")
    private LocalDate validEndDate;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "推广原因")
    private String reasonType;
    @ApiModelProperty(value = "推广原因详情")
    private String reasonRemark;
    @ApiModelProperty(value = "是否兜底")
    private Integer defaultFlag;
    @ApiModelProperty(value = "是否置顶（锁位）")
    private Integer topFlag;
    @ApiModelProperty(value = "是否占位")
    private Integer placeFlag;
    @ApiModelProperty(value = "占位有效期告警")
    private Integer placeWarnDays;
    @ApiModelProperty(value = "有效期告警")
    private Integer expireWarnDays;
    @ApiModelProperty(value = "修改人")
    private String modifier;
    @ApiModelProperty(value = "状态列表")
    private List<Integer> statusList;

    @ApiModelProperty(value = "regionIds", hidden = true)
    private List<Long> regionIds;

    @ApiModelProperty(value = "推广计划id", hidden = true)
    private Long id;
    @ApiModelProperty(value = "当前日期", hidden = true)
    private LocalDateTime currentDate;
    @ApiModelProperty(value = "占位结束日期", hidden = true)
    private LocalDateTime placeEndDate;
    @ApiModelProperty(value = "有效期结束日期", hidden = true)
    private LocalDateTime expireEndDate;
}
