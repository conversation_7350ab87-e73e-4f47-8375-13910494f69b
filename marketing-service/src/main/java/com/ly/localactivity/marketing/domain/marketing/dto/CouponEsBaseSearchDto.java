package com.ly.localactivity.marketing.domain.marketing.dto;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


/**
 * CouponEsBaseSearchDto
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponEsBaseSearchDto {
    /**
     * 从第几页开始，初始0开始
     */
    private Integer from;
    /**
     * 每页大小
     */
    private Integer size;

    public static String getListString(String... datas) {
        if (datas == null || datas.length == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (String data : datas) {
            sb.append(data).append(" OR ");
        }
        return sb.substring(0, sb.length() - 4);
    }

    public static String getStringList(List<String> datas) {
        if (CollectionUtil.isEmpty(datas)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (String data : datas) {
            sb.append(data).append(" OR ");
        }
        return sb.substring(0, sb.length() - 4);
    }

    public static String getList(List<Integer> datas) {
        if (CollectionUtil.isEmpty(datas)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (Integer data : datas) {
            sb.append(data).append(" OR ");
        }
        return sb.substring(0, sb.length() - 4);
    }

}
