package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡劵类型 0-满减，1-折扣
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponTypeEnum {
    /**
     * 0-满减
     */
    FULL_DISCOUNT(0, "满减"),
    /**
     * 1-折扣
     */
    DISCOUNT(1, "折扣"),

    FULL_DISCOUNT_GRADIENT(5, "满减阶梯"),
    ;
    private final Integer code;
    private final String desc;

    public static CouponTypeEnum getByCode(Integer code){
        if (code == null){
            return null;
        }
        for (CouponTypeEnum value : CouponTypeEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
