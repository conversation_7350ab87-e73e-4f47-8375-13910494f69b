package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CouponBatchInfoRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponBatchInfoRo", description = "批量获取券信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CouponBatchInfoRequest {
    private String requestId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", required = true)
    @NotNull(message = "批次号不能为空")
    @NotBlank(message = "批次号不能为空")
    private String batchNo;
    /**
     * 是否返回特殊规则，0：否 1：是
     */
    @ApiModelProperty(value = "是否返回特殊规则")
    private Boolean ruleStatus;

}
