package com.ly.localactivity.marketing.service.common;

import com.ly.localactivity.marketing.common.enums.common.MarketAreaType;
import com.ly.localactivity.marketing.domain.common.WCMarketArea;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 营销区域表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IWCMarketAreaService extends IService<WCMarketArea> {


    /**
     * 按城市id和类型获取区域id
     *
     * @param cityId         城市id
     * @param marketAreaType 市场区域类型
     * @return {@link Long }
     */
    Long getAreaIdByCityIdAndType(Long cityId, MarketAreaType marketAreaType);
}
