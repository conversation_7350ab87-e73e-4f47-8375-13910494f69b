package com.ly.localactivity.marketing.domain.marketing.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ly.localactivity.marketing.common.bean.SelectItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * OperationPositionVo
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "OperationPositionVo", description = "运营点位列表信息")
public class OperationPositionVo {
    /**
     * 运营位id
     */
    @ApiModelProperty(value = "运营位id")
    private Long id;
    /**
     * 运营位编码
     */
    @ApiModelProperty(value = "运营位编码")
    private String code;
    /**
     * 运营位名称
     */
    @ApiModelProperty(value = "运营位名称")
    private String name;
    /**
     * 自定义名称
     */
    @ApiModelProperty(value = "自定义名称")
    private String alias;
    /**
     * 所属频道页Id
     */
    @ApiModelProperty(value = "所属频道页Id")
    private Long positionPageId;
    /**
     * 所属频道页名称
     */
    @ApiModelProperty(value = "所属频道页名称")
    private String pageName;
    /**
     * 所属频道页版本
     */
    @ApiModelProperty(value = "所属频道页版本")
    private String pageVersion;

    /**
     * 站台
     */
    @ApiModelProperty(value = "所属运营平台")
    private List<PlatformVo> platforms;
    @ApiModelProperty(value = "所属运营平台编辑推广计划使用")
    private List<PlatformVo> editPlatforms;
    /**
     * 运营位状态
     */
    @ApiModelProperty(value = "运营位状态")
    private Integer status;
    /**
     * 输出形式
     */
    @ApiModelProperty(value = "输出形式")
    private String outputForm;
    /**
     * 输出形式
     */
    @ApiModelProperty(value = "输出形式,1-幻灯片，2-图片, 3-icon, 4-icon+关键词，5-关键词")
    private Integer outputType;
    /**
     * 图片张数
     */
    @ApiModelProperty(value = "图片张数")
    private Integer imageMaxCount;
    /**
     * 红包领取方式 1-领取、2-直充
     */
    @ApiModelProperty(value = "红包领取方式 1-领取、2-直充")
    private Integer pickupMethod;
    /**
     * 关键词长度
     */
    @ApiModelProperty(value = "关键词长度")
    private Integer keywordMaxLength;
    /**
     * 关键词个数
     */
    @ApiModelProperty(value = "关键词个数")
    private Integer keywordMaxCount;
    /**
     * 副标题长度
     */
    @ApiModelProperty(value = "副标题长度")
    private Integer subTitleMaxLength;
    /**
     * 地区覆盖
     */
    @JSONField(serialize = false)
    @ApiModelProperty(value = "地区覆盖", hidden = true)
    private String coverRegionType;
    /**
     * 需要覆盖区域
     */
    @ApiModelProperty(value = "需要覆盖区域")
    private List<String> coverAreas;
    /**
     * 图片宽度
     */
    @ApiModelProperty(value = "图片宽度")
    private Integer imageWidth;
    /**
     * 图片高度
     */
    @ApiModelProperty(value = "图片高度")
    private Integer imageHeight;
    /**
     * 图片大小
     */
    @ApiModelProperty(value = "图片大小")
    private String imageMaxSize;
    /**
     * 未来到期天数
     */
    @ApiModelProperty(value = "未来到期天数")
    private Integer expireDays;
    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String imageUrl;
    /**
     * 是否必填
     */
    @ApiModelProperty(value = "(是否允许为空)是否必填")
    private Integer emptyFlag;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String modifier;
    /**
     * 更新人工号
     */
    @ApiModelProperty(value = "更新人工号")
    private String modifierNo;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String modifiedTime;
    /**
     * 是否需要兜底
     */
    @ApiModelProperty(value = "是否需要兜底")
    private Boolean defaultFlag;

    @ApiModelProperty(value = "需要维护的兜底信息 <平台id-List<推广区域ID>>")
    private Map<Integer,  Map<Integer,List<Integer>>> defaultInfos;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private Integer planCount;

    /**
     * 推广方式
     */
    @ApiModelProperty(value = "推广方式")
    List<SelectItemVo> promoteTypes;
    @ApiModelProperty(value = "推广方式编辑时使用")
    List<SelectItemVo> editPromoteTypes;
    /**
     * 兜底已维护标识
     */
    @ApiModelProperty(value = "必填已维护标识,1-已维护，0-未维护,emptyFlag=0时有效")
    private Boolean vindicateFlag;
    @ApiModelProperty(value = "是否需要维护顺位")
    private Boolean IndexFlag;
    @ApiModelProperty(value = "顺位数量")
    private Integer indexCount;

}
