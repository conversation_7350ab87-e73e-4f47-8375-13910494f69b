package com.ly.localactivity.marketing.service.coupon;

import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;

/**
 * ICouponSyncService
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
public interface ICouponSyncService {

    /**
     * 刷新鲲鹏红包详情到本地
     *
     * @param couponBatchExtDto 请求
     * @return {@link Boolean}
     */
    Boolean refreshKunPengCouponDetailForDb(CouponBatchExtDto couponBatchExtDto);


    /**
     * 判断是否需要查询鲲鹏
     * @param batchNo
     * @return
     */
    boolean checkIsNeedQueryKunPeng(String batchNo);
}
