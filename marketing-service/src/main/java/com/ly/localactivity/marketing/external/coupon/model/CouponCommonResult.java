package com.ly.localactivity.marketing.external.coupon.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CouponCommonResult
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
@ApiModel(value = "CouponCommonResult", description = "代金通用返回结果")
public class CouponCommonResult<T> {
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    /**
     * 返回信息
     */
    @ApiModelProperty(value = "返回信息")
    private String msg;
    /**
     * 返回码
     */
    @ApiModelProperty(value = "返回码")
    private String code;
    /**
     * 返回数据
     */
    private T result;

    public static <T> CouponCommonResult<T> success(T result) {
        CouponCommonResult<T> commonResult = new CouponCommonResult<>();
        commonResult.setSuccess(true);
        commonResult.setResult(result);
        return commonResult;
    }
}
