package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DictTypeEnum
 *
 * <AUTHOR>
 * @date 2024/2/7
 */
@Getter
@AllArgsConstructor
public enum DictTypeEnum {
    /**
     * 推广方式
     */
    PROMOTE_TYPE("promotion_plan_promote_type", "推广方式"),
    PLATFORM("platform", "平台"),
    PROJECT("project", "项目"),
    ;

    private final String type;
    private final String desc;
}
