package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * OperationPromotionListLockedIndexRo
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@ApiModel(value = "OperationPromotionListLockedIndexRo", description = "获取已经锁定的顺位请求参数")
@Data
public class OperationPromotionListLockedIndexRo{
    /**
     * 平台
     */
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String platform;
    /**
     * 运营点位Code
     */
    @ApiModelProperty(value = "运营点位 ID", required = true)
    @NotNull(message = "运营点位 ID不能为空")
    private Long pointId;
    @ApiModelProperty(value = "当前时间", hidden = true)
    private LocalDateTime nowTime = LocalDateTime.now();
}
