package com.ly.localactivity.marketing.common.utils;

import com.ly.localactivity.framework.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.text.MessageFormat;

/**
 * EsSearchUtils
 *
 * <AUTHOR>
 * @date 2024/5/21
 */
@Slf4j
public class EsDeleteUtils {
    private static final String address = "es.dss.17usoft.com";

    /**
     * 保存对象
     *
     * @param index
     * @param id
     * @param env     环境
     * @return
     */
    public static boolean deleteById(EsSearchUtils.EsIndex index, String id, String env) {
        if (StringUtils.isEmpty(id)) {
            return false;
        }

        if (StringUtils.isBlank(env)) {
            env = SpringUtil.getActiveProfile();
        }
        String indexName = index.getName() + "-" + env;

        String url = MessageFormat.format("http://{0}/index/{1}/type/info/{2}", address, indexName, id);
        try {
            doDelete(url, index.getToken());
            return true;
        } catch (Exception e) {
            log.error("es删除失败", e);
            return false;
        }
    }

    private static String doDelete(String url, String token) throws Exception {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpDelete httpDelete = new HttpDelete(url);// 创建httpPost
        httpDelete.setHeader("Accept", "application/json");
        httpDelete.setHeader("Content-Type", "application/json");
        httpDelete.setHeader("Authentication", token);
        CloseableHttpResponse response = null;
        try {
            response = httpclient.execute(httpDelete);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity);
                return jsonString;
            } else {
                //logger.error("请求返回:"+state+"("+url+")");
            }
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
