package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponConsumeRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * <p>
 * 红包使用记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
public interface ICouponConsumeRecordService extends IService<CouponConsumeRecord> {

    /**
     * 消费优惠券
     *
     * @param batchNo     批号
     * @param couponCode  优惠券代码
     * @param orderAmount 订单金额
     * @param amount      金额
     */
    void consumeCoupon(String batchNo, String couponCode, BigDecimal orderAmount, BigDecimal amount);

    /**
     * 返还优惠券
     *
     * @param couponCode 优惠券代码
     */
    void returnCoupon(String couponCode);
}
