package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * CouponSimpleInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponSimpleInfoDto", description = "优惠券简单信息")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CouponSimpleInfoExtDto {
    /**
     * 批号
     */
    @ApiModelProperty(value = "批号")
    private String batchNo;
    /**
     * 费用承担部门
     */
    @ApiModelProperty(value = "费用承担部门")
    private List<CostDeptDto> costDept;
    /**
     * 使用方法code
     */
    @ApiModelProperty(value = "使用方法code")
    private String usedCode;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 批次名称
     */
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    /**
     * 用途
     */
    @ApiModelProperty(value = "用途")
    private String purpose;
    /**
     * 售卖券订单信息
     */
    @ApiModelProperty(value = "售卖券订单信息")
    private OrderInfoDto orderInfo;

    @ApiModelProperty(value = "红包信息")
    private CouponInfoDetailExtDto result;
}
