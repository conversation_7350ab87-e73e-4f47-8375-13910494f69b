package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * OperationPromotionPlanBatchAddRo
 *
 * <AUTHOR>
 * @date 2024/2/27
 */
@ApiModel(value = "OperationPromotionPlanBatchAddRo", description = "运营推广方案批量添加Ro")
@Data
public class OperationPromotionPlanBatchAddRo  {
    /**
     * 计划列表
     */
    @ApiModelProperty(value = "计划列表按照平台分类", required = true)
    @NotEmpty(message = "计划不能为空")
    @Valid
    private List<OperationPromotionPlanBatchAddGroupByPlatformRo> planList;

    @ApiModelProperty(name= "兜底标记", required = true)
    private Boolean defaultFlag;
}
