package com.ly.localactivity.marketing.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 国家地区表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WCCountryArea")
public class WCCountryArea implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家地区Id
     */
    @TableId("CAId")
    private Long CAId;

    /**
     * 名称
     */
    @TableField("CAName")
    private String CAName;

    /**
     * 简称
     */
    @TableField("CAShortName")
    private String CAShortName;

    /**
     * 英文名称
     */
    @TableField("CAEnName")
    private String CAEnName;

    /**
     * 英文简称
     */
    @TableField("CAShortEnName")
    private String CAShortEnName;

    /**
     * 拼音名称
     */
    @TableField("CAPinYinName")
    private String CAPinYinName;

    /**
     * 父级id
     */
    @TableField("CAPid")
    private Long CAPid;

    /**
     * id路径
     */
    @TableField("CAIdPath")
    private String CAIdPath;

    /**
     * 中文路径
     */
    @TableField("CANamePath")
    private String CANamePath;

    /**
     * 行政级别
     */
    @TableField("CAType")
    private Integer CAType;

    /**
     * 洲id
     */
    @TableField("CAContinetId")
    private Long CAContinetId;

    /**
     * 洲
     */
    @TableField("CAContinetName")
    private String CAContinetName;

    /**
     * 国家id
     */
    @TableField("CACountryId")
    private Long CACountryId;

    /**
     * 国家
     */
    @TableField("CACountryName")
    private String CACountryName;

    /**
     * 省id
     */
    @TableField("CAProvinceId")
    private Long CAProvinceId;

    /**
     * 省
     */
    @TableField("CAProvinceName")
    private String CAProvinceName;

    /**
     * 城市id
     */
    @TableField("CACityId")
    private Long CACityId;

    /**
     * 城市
     */
    @TableField("CACityName")
    private String CACityName;

    /**
     * 县Id
     */
    @TableField("CACountyId")
    private Long CACountyId;

    /**
     * 县名称
     */
    @TableField("CACountyName")
    private String CACountyName;

    /**
     * 城市三字码
     */
    @TableField("CAThreeCode")
    private String CAThreeCode;

    /**
     * 有效性
     */
    @TableField("CADataFlag")
    private Long CADataFlag;

    /**
     * 创建人姓名
     */
    @TableField("CACreateName")
    private String CACreateName;

    /**
     * 创建人工号
     */
    @TableField("CACreateJobNum")
    private String CACreateJobNum;

    /**
     * 创建时间
     */
    @TableField("CACreateDate")
    private LocalDateTime CACreateDate;

    /**
     * 更新人姓名
     */
    @TableField("CAUpdateName")
    private String CAUpdateName;

    /**
     * 更新人工号
     */
    @TableField("CAUpdateJobNum")
    private String CAUpdateJobNum;

    /**
     * 更新时间
     */
    @TableField("CAUpdateDate")
    private LocalDateTime CAUpdateDate;

    /**
     * 合并的历史Id集合,多个使用,分隔
     */
    @TableField("CAHistoryIds")
    private String CAHistoryIds;


}
