package com.ly.localactivity.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 红包使用规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@ApiModel(value = "CouponBatchRuleDto", description = "红包使用规则")
public class CouponBatchRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包批次id
     */
    @ApiModelProperty("红包批次id")
    private Long couponBatchId;

    /**
     * 佣金限制，1-不限，2-佣金>0，3-佣金>=0
     */
    @ApiModelProperty("佣金限制，1-不限，2-佣金>0，3-佣金>=0")
    private Integer commissionType;

    /**
     * 商品限制类型，1-全部商品 2-指定出发地，3-指定二级品类，4-指定商品
     */
    @ApiModelProperty(value = "商品限制类型，1-全部商品 2-指定出发地，3-指定二级品类，4-指定商品")
    private Integer limitType;

    /**
     * 数据id，对应地区id、品类id、商品id
     */
    @ApiModelProperty(value = "数据id，对应地区id、品类id、商品id")
    private List<String> dataIdList;
}
