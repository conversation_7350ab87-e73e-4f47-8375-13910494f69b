package com.ly.localactivity.marketing.domain.marketing.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 红包批次有效期
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@ApiModel(value = "CouponBatchValidDateDto", description = "红包批次有效期")
public class CouponBatchValidDateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包批次id
     */
    @ApiModelProperty(value = "红包批次id")
    private Long couponBatchId;

    /**
     * 红包批次明细id
     */
    @ApiModelProperty(value = "红包批次明细id")
    private Long couponBatchDetailId;

    /**
     * 使用有效期类型，1-固定日期段，2-动态有效期，3-倒计时(小时)，4-倒计时(分钟)
     */
    @ApiModelProperty(value = "使用有效期类型，1-固定日期段，2-动态有效期，3-倒计时(小时)，4-倒计时(分钟)")
    private Integer validDateType;

    /**
     * 使用有效期开始
     */
    @ApiModelProperty(value = "使用有效期开始")
    private LocalDateTime validBeginDate;

    /**
     * 使用有效期结束
     */
    @ApiModelProperty(value = "使用有效期结束")
    private LocalDateTime validEndDate;

    private List<String> validDateRange;

    /**
     * 倒计时小时
     */
    @ApiModelProperty(value = "倒计时小时")
    private Integer countdownHour;

    /**
     * 倒计时分钟
     */
    @ApiModelProperty(value = "倒计时分钟")
    private Integer countdownMinute;

    /**
     * 动态生效天数，x天开始生效
     */
    @ApiModelProperty(value = "动态生效天数，x天开始生效")
    private Integer dynamicStartDay;

    /**
     * 动态有效时长
     */
    @ApiModelProperty(value = "动态有效时长")
    private Integer dynamicDurationDay;


}
