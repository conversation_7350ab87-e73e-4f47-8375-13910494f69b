package com.ly.localactivity.marketing.external.coupon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * CouponRechargeResultCodeEnum
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@Getter
@AllArgsConstructor
public enum CouponResultCodeEnum {

    Success("0", "成功"),
    Fail("1000", "失败"),
    Fail_1001("1001", "%s参数错误"),
    Fail_1002("1002", "扩展参数校验不通过"),

    Fail_2001  ("2001", "该红包批次无效或不存在"),
    Fail_2002  ("2002", "该红包批次已过期"),
    Fail_2003  ("2003", "该红包未到可用日期，可用日期为{1}"),
    Fail_2004  ("2004", "该红包已使用，使用时间为{1}"),
    Fail_2005  ("2005", "该红包已被作废，操作时间：{1}"),
    Fail_2006  ("2006", "红包抵扣金额错误，当前尝试扣减的优惠金额为{1}，超出了该红包的面额上限，红包的上限金额为{2}"),

    Fail_2007  ("2007", "红包库存不足，该批次总库存：{1}，当前已发放数量：{2}"),
    Fail_2008  ("2008", "该红包批次已达到当天可发放上限，该批次限制每天最多发放：{1}，目前已发放：{2}"),

    Fail_2009  ("2009", "红包批次不在有效期间内"),
    Fail_2010  ("2010", "红包号不存在或该用户无权限查询"),
    Fail_2011  ("2011", "该红包不可用，当前状态：{1}"),
    Fail_2012  ("2012", "礼包不存在"),
    Fail_2013  ("2013", "礼包发放失败，可发放日期为：{1}"),
    Fail_2014  ("2014", "礼包超过领取次数"),
    ;
    private String code;
    private String name;
}
