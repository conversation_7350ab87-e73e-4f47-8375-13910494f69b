package com.ly.localactivity.marketing.external.coupon.ro;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * CouponOrderListRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponOrderListRo", description = "获取订单列表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponOrderListRequest extends CouponBaseRequest {
    /**
     * 订单金额（此处不传，则消费接口必传，否则不校验，存在风险——国际站统一不做校验，因为项目传的币种金额不一致）
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    /**
     * 状态
     * 1-未使用；2-已占用；3-已使用；4-已作废；5_已过期
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 子渠道
     */
    @ApiModelProperty(value = "子渠道")
    private Integer subChannel;
    /**
     * 特殊规则
     */
    @ApiModelProperty(value = "特殊规则")
    private JSONObject extRuleInfo;
    /**
     * 子状态 1可使用，2未生效
     */
    @ApiModelProperty(value = "1可使用，2未生效")
    private String subStatus;
    /**
     * 券使用人信息
     */
    @ApiModelProperty(value = "券使用人信息")
    private List<PersonInfo> consumeUsers;
    /**
     * 	(yyyy-MM-dd HH:mm:ss)传当地时区的时间，国际券必传
     */
    @ApiModelProperty(value = "时间")
    private String consumeTime;
    /**
     * 是否返回规则限定不通过的券
     * 0:不返回(默认) 1：返回
     */
    @ApiModelProperty(value = "是否返回规则限定不通过的券")
    private Integer ruleStatus;
    /**
     * 0：校验 1：不校验
     * 是否对使用规则进行校验
     */
    @ApiModelProperty(value = "是否对使用规则进行校验")
    private Integer isCheckRule;
    /**
     * 站外平台id（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外平台id")
    private Integer outsidePartyId;
    /**
     * 站外会员id（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外会员id")
    private String outsideUnionId;
}
