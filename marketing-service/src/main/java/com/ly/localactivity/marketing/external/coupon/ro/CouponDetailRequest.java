package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CouponDetailRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponDetailRo", description = "获取优惠券详情")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CouponDetailRequest extends CouponBaseRequest {
    /**
     * 券号
     */
    @ApiModelProperty(value = "券号",required = true)
    @NotNull(message = "券号不能为空")
    @NotBlank(message = "券号不能为空")
    private String couponNo;
    /**
     * 是否返回特殊规则
     */
    @ApiModelProperty(value = "是否返回特殊规则")
    private Boolean ruleStatus;
    /**
     * 酒店红包-已使用红包是否查询订单号。默认false不查，true查询
     */
    @ApiModelProperty(value = "酒店红包-已使用红包是否查询订单号。默认false不查，true查询")
    private Boolean needHotelOrderNo;
}
