package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchValidDate;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchValidDateDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包批次有效期 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchValidDateService extends IService<CouponBatchValidDate> {

    /**
     * 保存或更新
     * @param couponBatchDetail
     * @param couponBatchValidDateDto
     * @return
     */
    Boolean saveOrUpdate(CouponBatchDetail couponBatchDetail, CouponBatchValidDateDto couponBatchValidDateDto);

    /**
     * 根据红包批次id查询
     * @param couponBatchDetailIdList
     * @return
     */
    Map<Long, CouponBatchValidDateDto> selectByCouponBatchDetailIdList(List<Long> couponBatchDetailIdList);
}
