package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.collection.CollectionUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * CollectionUtils
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
public class CollectionUtils extends CollectionUtil {
    /**
     * 按属性去重
     * List<MyObject> distinctList = myList.stream().filter(distinctByProperty(MyObject::getSomeProperty)).collect(Collectors.toList());
     *
     * @param keyExtractor 密钥提取器
     * @return {@link Predicate}<{@link T}>
     */
    public static <T> Predicate<T> distinctByProperty(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
