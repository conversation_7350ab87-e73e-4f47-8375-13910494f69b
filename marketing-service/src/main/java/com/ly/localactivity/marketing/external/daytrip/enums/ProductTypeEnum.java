package com.ly.localactivity.marketing.external.daytrip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型枚举
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Getter
@AllArgsConstructor
public enum ProductTypeEnum {
    /**
     * 一日游
     */
    DayTrip("100", "一日游"),
    /**
     * wifi
     */
    Wifi("300", "WIFI"),
    /**
     * 电话卡
     */
    PhoneCard("200", "电话卡"),
    ;

    private final String code;
    private final String name;

    public static ProductTypeEnum of(String code) {
        for (ProductTypeEnum e : ProductTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (ProductTypeEnum e : ProductTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e.name;
            }
        }
        return "";
    }


    @Override
    public String toString() {
        return this.getCode();
    }
}
