package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponResultInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponResultInfoDto", description = "券结果信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponResultInfoExtDto extends CouponInfoExtDto {
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    /**
     * 失败信息
     */
    @ApiModelProperty(value = "失败信息")
    private String message;
}
