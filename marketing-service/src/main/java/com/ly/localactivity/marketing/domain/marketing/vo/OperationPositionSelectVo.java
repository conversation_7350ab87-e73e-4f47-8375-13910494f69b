package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Builder;
import lombok.Data;

/**
 * OperationPositionSelectVo
 *
 * <AUTHOR>
 * @date 2024/2/5
 */
@Data
@Builder
@ApiModel(value = "OperationPositionSelectVo", description = "运营点位选择信息")
public class OperationPositionSelectVo {
    /**
     * 运营位id
     */
    @ApiModelProperty(value = "运营位code")
    private String value;
    /**
     * 标签
     */
    @ApiModelProperty(value = "运营位别名")
    private String label;

    @ApiModelProperty(value = "IM")
    private Boolean isIM;
}
