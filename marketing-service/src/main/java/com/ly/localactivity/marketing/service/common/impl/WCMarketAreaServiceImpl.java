package com.ly.localactivity.marketing.service.common.impl;

import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.marketing.common.enums.common.MarketAreaType;
import com.ly.localactivity.marketing.domain.common.WCMarketArea;
import com.ly.localactivity.marketing.mapper.common.WCMarketAreaMapper;
import com.ly.localactivity.marketing.service.common.IWCMarketAreaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 营销区域表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class WCMarketAreaServiceImpl extends ServiceImpl<WCMarketAreaMapper, WCMarketArea> implements IWCMarketAreaService {


    /**
     * 按城市id和类型获取区域id
     *
     * @param cityId         城市id
     * @param marketAreaType 市场区域类型
     * @return {@link Long }
     */
    @LACacheable(key = "'getAreaIdByCityIdAndType:' + #cityId + '_' + #marketAreaType.getCode()", expire = 60 * 60 * 24)
    @Override
    public Long getAreaIdByCityIdAndType(Long cityId, MarketAreaType marketAreaType) {
        return baseMapper.getAreaIdByCityIdAndType(cityId, marketAreaType.getCode());
    }
}
