package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OperationPositionPageSelectVo
 *
 * <AUTHOR>
 * @date 2024/2/5
 */
@ApiModel(value = "OperationPositionPageSelectVo", description = "频道页选择信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationPositionPageSelectVo {
    /**
     * 运营位id
     */
    @ApiModelProperty(value = "频道页id")
    private Long value;

    /**
     * 标签
     */
    @ApiModelProperty(value = "运营位名称+版本")
    private String label;
}
