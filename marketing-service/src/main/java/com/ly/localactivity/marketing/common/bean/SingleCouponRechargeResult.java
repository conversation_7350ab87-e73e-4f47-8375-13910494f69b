package com.ly.localactivity.marketing.common.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SingleCouponRechargeResult
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Data
public class SingleCouponRechargeResult implements Serializable {
    private static final long serialVersionUID = 3611189655273336853L;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "错误码")
    private String errorCode;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;



}