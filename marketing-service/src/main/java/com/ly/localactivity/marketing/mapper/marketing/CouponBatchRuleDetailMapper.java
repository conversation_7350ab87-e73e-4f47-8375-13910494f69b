package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.framework.datasource.LaBaseMapper;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 红包使用规则明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchRuleDetailMapper extends MyBaseMapper<CouponBatchRuleDetail> {

    /**
     * 根据批次号查询规则
     *
     * @param couponBatchNo 优惠券批号
     * @return {@link List}<{@link CouponBatchRuleDetail}>
     */
    List<CouponBatchRuleDetail> listByCouponBatchNo(@Param("couponBatchNo") String couponBatchNo);

    List<CouponBatchRuleDetail> listByCouponBatchNos(@Param("batchNos") List<String> batchNos);
}
