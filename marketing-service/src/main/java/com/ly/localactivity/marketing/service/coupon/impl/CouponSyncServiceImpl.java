package com.ly.localactivity.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.CouponDetailStatusEnum;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.service.coupon.ICouponBatchStockControlService;
import com.ly.localactivity.marketing.service.coupon.ICouponSyncService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchDetailService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CouponSyncServiceImpl
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@Service
@Slf4j
public class CouponSyncServiceImpl implements ICouponSyncService {
    @Resource
    private ICouponBatchDetailService couponBatchDetailService;
    @Resource
    private ICouponBatchListService couponBatchListService;
    @Resource
    private ICouponBatchStockControlService couponBatchStockControlService;
    @Resource
    private AdminTokenUtils adminTokenUtils;

    /**
     * 刷新鲲鹏红包信息到本地
     * @param couponBatchExtDto 请求
     * @return
     */
    @Override
    public Boolean refreshKunPengCouponDetailForDb(CouponBatchExtDto couponBatchExtDto) {
        if (couponBatchExtDto == null){
            return true;
        }
        // 查询本地红包批次数据
        List<CouponBatchDetailDto> oldCouponBatchDetailDtoList = couponBatchDetailService.selectByBatchNoList(Collections.singletonList(couponBatchExtDto.getBatchNo()), null);
        if (CollectionUtil.isEmpty(oldCouponBatchDetailDtoList)){
            return true;
        }
        CouponBatchDetailDto oldCouponBatchDetailDto = oldCouponBatchDetailDtoList.get(0);

        // 转换为红包批次详情
        CouponBatchDetailDto newCouponBatchDetail = CouponBatchDetailDto.detailDtoFromExtDto(couponBatchExtDto);

        newCouponBatchDetail.setId(oldCouponBatchDetailDto.getId());
        couponBatchDetailService.updateDetail(newCouponBatchDetail);

        // 如果每日领取次数增加 则清除每日领取限制
        boolean clearDayLimitFlag = newCouponBatchDetail.getReceiveDayLimit() != null && newCouponBatchDetail.getReceiveDayLimit().compareTo(oldCouponBatchDetailDto.getReceiveDayLimit()) > 0;
        //刷新库存限制
        couponBatchStockControlService.refreshLimit(couponBatchExtDto, clearDayLimitFlag);

        // 更新红包状态
        SkyLogUtils.infoByMethod(log, StringUtils.format("开始更新红包状态:{}", JSON.toJSONString(oldCouponBatchDetailDtoList)), "", "");
        couponBatchListService.refreshCouponBatchListStatus(oldCouponBatchDetailDtoList.stream().map(CouponBatchDetailDto::getCouponBatchId).distinct().collect(Collectors.toList()));

        return true;
    }

    /**
     * 判断是否需要查询鲲鹏
     * @param batchNo
     * @return
     */
    @Override
    public boolean checkIsNeedQueryKunPeng(String batchNo) {
        CouponBatchDetail couponBatchDetail = couponBatchDetailService.selectByBatchNo(batchNo);
        if (couponBatchDetail == null){
            return false;
        }

        if (DeleteFlagEnum.NOT_DELETE.getCode().equals(couponBatchDetail.getDeleteFlag())){
            return true;
        }

        CouponBatchDetail newCouponBatchDetail = new CouponBatchDetail();
        newCouponBatchDetail.setId(couponBatchDetail.getId());
        newCouponBatchDetail.setStatus(CouponDetailStatusEnum.CANCEL.getCode());
        newCouponBatchDetail.setModifiedTime(LocalDateTime.now());
        newCouponBatchDetail.setModifier(adminTokenUtils.getModifier());
        couponBatchDetailService.updateById(newCouponBatchDetail);

        // 更新红包状态
        SkyLogUtils.infoByMethod(log, StringUtils.format("红包批次被移除，开始更新红包状态:{}", couponBatchDetail.getBatchNo()), "", "");
        couponBatchListService.refreshCouponBatchListStatus(Collections.singletonList(couponBatchDetail.getCouponBatchId()));
        return false;
    }
}
