package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanRegion;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 运营推广告计划地区 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface IOperationPromotionPlanRegionService extends IService<OperationPromotionPlanRegion> {

    /**
     * 保存区域列表
     *
     * @param planId              计划id
     * @param regionList          ids
     * @param isRegionZone        是否市场区域
     * @param isChangePromoteType 是否改版了推广方式
     * @param modifier            修改人
     */
    void saveRegionList(Long planId, List<Long> regionList, Boolean isRegionZone, Boolean isChangePromoteType,String modifier);

    /**
     * 按计划id获取
     *
     * @param planId 计划id
     * @return {@link List}<{@link OperationPromotionPlanRegion}>
     */
    List<OperationPromotionPlanRegion> getByPlanId(Long planId);
}
