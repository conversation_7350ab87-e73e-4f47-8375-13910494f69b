package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 红包库存限制表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_batch_stock_limit")
public class CouponBatchStockLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 是否当日受限，0-否，1-是
     */
    @TableField("day_limit_flag")
    private Boolean dayLimitFlag;

    /**
     * 当天日期
     */
    @TableField("day_value")
    private LocalDateTime dayValue;

    /**
     * 总库存受限，0-否，1-是
     */
    @TableField("stock_limit_flag")
    private Boolean stockLimitFlag;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
