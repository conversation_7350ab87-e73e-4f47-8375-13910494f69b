package com.ly.localactivity.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.model.dto.CouponBatchQueryDto;
import com.ly.localactivity.marketing.application.model.ro.CouponLimitRo;
import com.ly.localactivity.marketing.common.bean.EsSearchResult;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.enums.CouponRuleLimitTypeEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchApiStatusEnum;
import com.ly.localactivity.marketing.common.utils.CollectionUtils;
import com.ly.localactivity.marketing.common.utils.EsSearchUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsSearchBatchNoDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsSearchDto;
import com.ly.localactivity.marketing.service.coupon.ICouponSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * CouponSearchService
 *
 * <AUTHOR>
 * @date 2024/5/6
 */
@Slf4j
@Service
public class CouponSearchServiceImpl implements ICouponSearchService {

    private static final int BATCH_SIZE = 500;

//    @Override
    public List<CouponEsIndexDto> searchEsBatchList_back(CouponBatchQueryDto queryDto) {
        long start = System.currentTimeMillis();
        List<CouponEsIndexDto> couponBatchList = new ArrayList<>();
        if (queryDto.getLimitData() == null) {
            queryDto.setLimitData(new CouponLimitRo());
        }

        boolean filterDate = true;
        Integer filterStatus = CouponBatchApiStatusEnum.Available.getCode();
        String refid = queryDto.getRefid();
        //默认过滤可用，查询用户已经领取优惠券，不需要过滤红包批次的可用状态、有效期、refid参数
        if (queryDto.getFilterAvaliabled() != null && !queryDto.getFilterAvaliabled()) {
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->filterAvaliabled:req:{}", JSON.toJSONString(queryDto)), queryDto.getRequestId(), "");
            filterStatus = null;
            filterDate = false;
            refid = null;
        }

        boolean finalFilterDate = filterDate;
        Integer finalFilterStatus = filterStatus;
        CouponLimitRo finalLimitData = queryDto.getLimitData();
        String finalRefid = refid;
        //资源id适配
        CompletableFuture<List<CouponEsIndexDto>> resourceIdsFuture = CompletableFuture.supplyAsync(() -> {
            SkyLogUtils.infoByMethod(log, MessageFormat.format("searchEsBatchList->start resourceIdsFuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            if (StringUtils.isBlank(finalLimitData.getResourceIds())) {
                return new ArrayList<>();
            }
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.PRODUCT.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getResourceIds().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);

            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->resourceIdsFuture:cost:{}:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });
        //出发城市适配
        CompletableFuture<List<CouponEsIndexDto>> cityIdsFuture = CompletableFuture.supplyAsync(() -> {
            SkyLogUtils.infoByMethod(log, MessageFormat.format("searchEsBatchList->start cityIdsFuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            if (StringUtils.isBlank(finalLimitData.getCityIds())) {
                return new ArrayList<>();
            }
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.DEPARTURE.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getCityIds().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->cityIdsFuture:cost:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });
        //二级品类适配
        CompletableFuture<List<CouponEsIndexDto>> secondCategorysFuture = CompletableFuture.supplyAsync(() -> {
            SkyLogUtils.infoByMethod(log, MessageFormat.format("searchEsBatchList->start secondCategorysFuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            if (StringUtils.isBlank(finalLimitData.getSecondCategorys())) {
                return new ArrayList<>();
            }

            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.CATEGORY.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getSecondCategorys().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->secondCategorysFuture:cost:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });
        //平台所有适配红包
        CompletableFuture<List<CouponEsIndexDto>> allFeuture = CompletableFuture.supplyAsync(() -> {
            SkyLogUtils.infoByMethod(log, MessageFormat.format("searchEsBatchList->start allFeuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            // 所有适配红包
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setBatchType(CouponBatchListTypeEnum.PLATFORM.getCode());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->allFeuture:cost:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });
        //商家红包
        CompletableFuture<List<CouponEsIndexDto>> supplierFeuture = CompletableFuture.supplyAsync(() -> {
            SkyLogUtils.infoByMethod(log, MessageFormat.format("searchEsBatchList->start supplierFeuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            // 其他条件不为空 商家条件为空 则返回空，全部条件为空 则只查询点位红包
            if (StringUtils.isEmpty(finalLimitData.getSupplierIds()) && finalLimitData.getSupplierId() == null){
                return new ArrayList<>();
            }
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
            if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds())){
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(finalLimitData.getSupplierIds().split(",")));
            }else {
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(String.valueOf(finalLimitData.getSupplierId())));
            }
            searchDto.setBatchType(CouponBatchListTypeEnum.SUPPLIER.getCode());
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->supplierFeuture:cost:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });

        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(resourceIdsFuture, cityIdsFuture, secondCategorysFuture, allFeuture, supplierFeuture)
                .whenComplete((s, th) -> {
                    if (th != null) {
                        log.error("searchByLimit error", th);
                    }
                    if (!resourceIdsFuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(resourceIdsFuture.join());
                    }
                    if (!cityIdsFuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(cityIdsFuture.join());
                    }
                    if (!secondCategorysFuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(secondCategorysFuture.join());
                    }
                    if (!allFeuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(allFeuture.join());
                    }
                    if (!supplierFeuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(supplierFeuture.join());
                    }
                });
        try {
            voidCompletableFuture.join();
        } catch (Exception e) {
            log.error("searchEsBatchList error", e);
        }
        //过滤重复
        List<CouponEsIndexDto> returnData = couponBatchList.stream().filter(CollectionUtils.distinctByProperty(CouponEsIndexDto::getBatchNo)).collect(Collectors.toList());
        SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs->final:cost:thread:{};req:{};res:{}", (System.currentTimeMillis() - start), Thread.currentThread().getId(), queryDto, JSON.toJSONString(returnData)), "", queryDto.getRequestId());
        return returnData;
    }

    private List<CouponEsIndexDto> asyncSearchEsBatchList(CouponBatchQueryDto queryDto) {
        long start = System.currentTimeMillis();
        List<CouponEsIndexDto> couponBatchList = new ArrayList<>();
        if (queryDto.getLimitData() == null) {
            queryDto.setLimitData(new CouponLimitRo());
        }

        boolean filterDate = true;
        Integer filterStatus = CouponBatchApiStatusEnum.Available.getCode();
        String refid = queryDto.getRefid();
        //默认过滤可用，查询用户已经领取优惠券，不需要过滤红包批次的可用状态、有效期、refid参数
        if (queryDto.getFilterAvaliabled() != null && !queryDto.getFilterAvaliabled()) {
//            SkyLogUtils.infoByMethod(log, StringUtils.format("asyncSearchByEs->filterAvaliabled:req:{}", JSON.toJSONString(queryDto)), queryDto.getRequestId(), "");
            filterStatus = null;
            filterDate = false;
            refid = null;
        }

        boolean finalFilterDate = filterDate;
        Integer finalFilterStatus = filterStatus;
        CouponLimitRo finalLimitData = queryDto.getLimitData();
        String finalRefid = refid;
        //资源id适配
        CompletableFuture<List<CouponEsIndexDto>> resourceIdsFuture = CompletableFuture.supplyAsync(() -> {
//            SkyLogUtils.infoByMethod(log, MessageFormat.format("asyncSearchEsBatchList->start resourceIdsFuture :{0}", System.currentTimeMillis()), "", "");

            long futureStart = System.currentTimeMillis();
            if (StringUtils.isBlank(finalLimitData.getResourceIds())) {
                return new ArrayList<>();
            }
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.PRODUCT.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getResourceIds().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);

            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
//            SkyLogUtils.infoByMethod(log, StringUtils.format("asyncSearchByEs->resourceIdsFuture:cost:{}:thread:{};req:{};res:{}", (System.currentTimeMillis() - futureStart), Thread.currentThread().getId(), JSON.toJSONString(searchDto), JSON.toJSONString(couponEsIndexDtos)), "", queryDto.getRequestId());
            return couponEsIndexDtos;
        });
        //出发城市适配
        CompletableFuture<List<CouponEsIndexDto>> cityIdsFuture;
        if (Boolean.TRUE.equals(queryDto.getOnlyResourceId())){
            cityIdsFuture = CompletableFuture.completedFuture(new ArrayList<>());
        }else {
            cityIdsFuture = CompletableFuture.supplyAsync(() -> {
                if (StringUtils.isBlank(finalLimitData.getCityIds())) {
                    return new ArrayList<>();
                }
                CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
                searchDto.setPlatforms(queryDto.getPlatform());
                searchDto.setLimitType(CouponRuleLimitTypeEnum.DEPARTURE.getCode());
                searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getCityIds().split(",")));
                searchDto.setPositionCodes(queryDto.getPositionCode());
                searchDto.setRefids(finalRefid);
                searchDto.setRequestId(queryDto.getRequestId());
                searchDto.setStatus(finalFilterStatus);
                List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
                return couponEsIndexDtos;
            });
        }

        //二级品类适配 暂无
//        CompletableFuture<List<CouponEsIndexDto>> secondCategorysFuture = CompletableFuture.supplyAsync(() -> {
//            if (StringUtils.isBlank(finalLimitData.getSecondCategorys())) {
//                return new ArrayList<>();
//            }
//
//            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
//            searchDto.setPlatforms(queryDto.getPlatform());
//            searchDto.setLimitType(CouponRuleLimitTypeEnum.CATEGORY.getCode());
//            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getSecondCategorys().split(",")));
//            searchDto.setPositionCodes(queryDto.getPositionCode());
//            searchDto.setRefids(finalRefid);
//            searchDto.setRequestId(queryDto.getRequestId());
//            searchDto.setStatus(finalFilterStatus);
//            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
//            return couponEsIndexDtos;
//        });
        //平台所有适配红包
        CompletableFuture<List<CouponEsIndexDto>> allFeuture = CompletableFuture.supplyAsync(() -> {
            // 所有适配红包
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setBatchType(CouponBatchListTypeEnum.PLATFORM.getCode());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            return couponEsIndexDtos;
        });
        //商家红包
        CompletableFuture<List<CouponEsIndexDto>> supplierFeuture = CompletableFuture.supplyAsync(() -> {
            long futureStart = System.currentTimeMillis();
            // 其他条件不为空 商家条件为空 则返回空，全部条件为空 则只查询点位红包
            if (StringUtils.isEmpty(finalLimitData.getSupplierIds()) && finalLimitData.getSupplierId() == null){
                return new ArrayList<>();
            }
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
            if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds())){
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(finalLimitData.getSupplierIds().split(",")));
            }else {
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(String.valueOf(finalLimitData.getSupplierId())));
            }
            searchDto.setBatchType(CouponBatchListTypeEnum.SUPPLIER.getCode());
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
            return couponEsIndexDtos;
        });

        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(resourceIdsFuture, cityIdsFuture/*, secondCategorysFuture*/, allFeuture, supplierFeuture)
                .whenComplete((s, th) -> {
                    if (th != null) {
                        log.error("searchByLimit error", th);
                    }
                    if (!resourceIdsFuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(resourceIdsFuture.join());
                    }
                    if (!cityIdsFuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(cityIdsFuture.join());
                    }
//                    if (!secondCategorysFuture.isCompletedExceptionally()) {
//                        couponBatchList.addAll(secondCategorysFuture.join());
//                    }
                    if (!allFeuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(allFeuture.join());
                    }
                    if (!supplierFeuture.isCompletedExceptionally()) {
                        couponBatchList.addAll(supplierFeuture.join());
                    }
                });
        try {
            voidCompletableFuture.join();
        } catch (Exception e) {
            log.error("searchEsBatchList error", e);
        }
        //过滤重复
        List<CouponEsIndexDto> returnData = couponBatchList.stream().filter(CollectionUtils.distinctByProperty(CouponEsIndexDto::getBatchNo)).collect(Collectors.toList());
        SkyLogUtils.infoByMethod(log, StringUtils.format("asyncSearchByEs->final:cost:{};thread:{};req:{};res:{}", (System.currentTimeMillis() - start), Thread.currentThread().getId(), queryDto, JSON.toJSONString(returnData)), "", queryDto.getRequestId());
        return returnData;
    }


    private List<CouponEsIndexDto> syncSearchEsBatchList(CouponBatchQueryDto queryDto) {
        long start = System.currentTimeMillis();
        List<CouponEsIndexDto> couponBatchList = new ArrayList<>();
        if (queryDto.getLimitData() == null) {
            queryDto.setLimitData(new CouponLimitRo());
        }

        boolean filterDate = true;
        Integer filterStatus = CouponBatchApiStatusEnum.Available.getCode();
        String refid = queryDto.getRefid();
        //默认过滤可用，查询用户已经领取优惠券，不需要过滤红包批次的可用状态、有效期、refid参数
        if (queryDto.getFilterAvaliabled() != null && !queryDto.getFilterAvaliabled()) {
//            SkyLogUtils.infoByMethod(log, StringUtils.format("syncSearchByEs->filterAvaliabled:req:{}", JSON.toJSONString(queryDto)), queryDto.getRequestId(), "");
            filterStatus = null;
            filterDate = false;
            refid = null;
        }

        boolean finalFilterDate = filterDate;
        Integer finalFilterStatus = filterStatus;
        CouponLimitRo finalLimitData = queryDto.getLimitData();
        String finalRefid = refid;

        //资源id适配
        if (StringUtils.isNotBlank(finalLimitData.getResourceIds())) {
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.PRODUCT.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getResourceIds().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto>  resourceIdIndexDtos = searchEsByLimit(searchDto);
            if (CollectionUtil.isNotEmpty(resourceIdIndexDtos)){
                couponBatchList.addAll(resourceIdIndexDtos);
            }
        }

        //出发城市适配 不止查资源id时才查
        if (!Boolean.TRUE.equals(queryDto.getOnlyResourceId()) && StringUtils.isNotBlank(finalLimitData.getCityIds())) {
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.DEPARTURE.getCode());
            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getCityIds().split(",")));
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> cityIdIndexDtos = searchEsByLimit(searchDto);
            if (CollectionUtil.isNotEmpty(cityIdIndexDtos)){
                couponBatchList.addAll(cityIdIndexDtos);
            }
        }

        //二级品类适配 todo 暂无此类型
//        if (StringUtils.isNotBlank(finalLimitData.getSecondCategorys())) {
//            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
//            searchDto.setPlatforms(queryDto.getPlatform());
//            searchDto.setLimitType(CouponRuleLimitTypeEnum.CATEGORY.getCode());
//            searchDto.setLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getSecondCategorys().split(",")));
//            searchDto.setPositionCodes(queryDto.getPositionCode());
//            searchDto.setRefids(finalRefid);
//            searchDto.setRequestId(queryDto.getRequestId());
//            searchDto.setStatus(finalFilterStatus);
//            List<CouponEsIndexDto> secondCategoryIndexDtos = searchEsByLimit(searchDto);
//            if (CollectionUtil.isNotEmpty(secondCategoryIndexDtos)){
//                couponBatchList.addAll(secondCategoryIndexDtos);
//            }
//        }

        //平台所有适配红包
        // 所有适配红包
        CouponEsSearchDto searchAllDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
        searchAllDto.setPlatforms(queryDto.getPlatform());
        searchAllDto.setBatchType(CouponBatchListTypeEnum.PLATFORM.getCode());
        searchAllDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
        searchAllDto.setPositionCodes(queryDto.getPositionCode());
        searchAllDto.setRefids(finalRefid);
        searchAllDto.setRequestId(queryDto.getRequestId());
        searchAllDto.setStatus(finalFilterStatus);
        List<CouponEsIndexDto> allPlatformIndexDtos = searchEsByLimit(searchAllDto);
        if (CollectionUtil.isNotEmpty(allPlatformIndexDtos)){
            couponBatchList.addAll(allPlatformIndexDtos);
        }
        //商家红包
        if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds()) || finalLimitData.getSupplierId() != null){
            CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
            searchDto.setPlatforms(queryDto.getPlatform());
            searchDto.setLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
            if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds())){
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(finalLimitData.getSupplierIds().split(",")));
            }else {
                searchDto.setSupplierIds(CouponEsSearchDto.getListString(String.valueOf(finalLimitData.getSupplierId())));
            }
            searchDto.setBatchType(CouponBatchListTypeEnum.SUPPLIER.getCode());
            searchDto.setPositionCodes(queryDto.getPositionCode());
            searchDto.setRefids(finalRefid);
            searchDto.setRequestId(queryDto.getRequestId());
            searchDto.setStatus(finalFilterStatus);
            List<CouponEsIndexDto> supplierIndexDtos = searchEsByLimit(searchDto);
            if (CollectionUtil.isNotEmpty(supplierIndexDtos)){
                couponBatchList.addAll(supplierIndexDtos);
            }
        }

        //过滤重复
        List<CouponEsIndexDto> returnData = couponBatchList.stream().filter(CollectionUtils.distinctByProperty(CouponEsIndexDto::getBatchNo)).collect(Collectors.toList());
        SkyLogUtils.infoByMethod(log, StringUtils.format("syncSearchByEs->final:cost:{};thread:{};req:{};res:{}", (System.currentTimeMillis() - start), Thread.currentThread().getId(), queryDto, JSON.toJSONString(returnData)), "", queryDto.getRequestId());
        return returnData;
    }

    private List<CouponEsIndexDto> searchSingleEsBatchList(CouponBatchQueryDto queryDto) {
        long start = System.currentTimeMillis();
        if (queryDto.getLimitData() == null) {
            queryDto.setLimitData(new CouponLimitRo());
        }


        boolean filterDate = true;
        Integer filterStatus = CouponBatchApiStatusEnum.Available.getCode();
        String refid = queryDto.getRefid();
        //默认过滤可用，查询用户已经领取优惠券，不需要过滤红包批次的可用状态、有效期、refid参数
        if (queryDto.getFilterAvaliabled() != null && !queryDto.getFilterAvaliabled()) {
//            SkyLogUtils.infoByMethod(log, StringUtils.format("syncSearchByEs->filterAvaliabled:req:{}", JSON.toJSONString(queryDto)), queryDto.getRequestId(), "");
            filterStatus = null;
            filterDate = false;
            refid = null;
        }

        boolean finalFilterDate = filterDate;
        Integer finalFilterStatus = filterStatus;
        CouponLimitRo finalLimitData = queryDto.getLimitData();
        String finalRefid = refid;


        CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, finalFilterDate);
        if (StringUtils.isNotEmpty(queryDto.getPlatform())){
            searchDto.setPlatforms(queryDto.getPlatform());
        }else if(CollectionUtil.isNotEmpty(queryDto.getPlatforms())){
            searchDto.setPlatforms(CouponEsSearchDto.getStringList(queryDto.getPlatforms()));
        }else {
            searchDto.setPlatforms(queryDto.getPlatform());
        }

        searchDto.setQueryLimit(true);
        searchDto.setPositionCodes(queryDto.getPositionCode());
        searchDto.setRefids(finalRefid);
        searchDto.setRequestId(queryDto.getRequestId());
        searchDto.setStatus(finalFilterStatus);

        //资源id适配
        if (StringUtils.isNotBlank(finalLimitData.getResourceIds())) {
            searchDto.setProductLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getResourceIds().split(",")));
        }

        //出发城市适配 不止查资源id时才查
        if (!Boolean.TRUE.equals(queryDto.getOnlyResourceId()) && StringUtils.isNotBlank(finalLimitData.getCityIds())) {
            searchDto.setDepartureLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getCityIds().split(",")));
        }

        // 一级品类
        if (finalLimitData.getFirstCategoryId() != null){
            searchDto.setFirstCategoryId(finalLimitData.getFirstCategoryId());
        }

        //平台所有适配红包
        // 所有适配红包
        searchDto.setPlatformLimitType(CouponRuleLimitTypeEnum.ALL.getCode());

        //商家红包
        if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds()) || finalLimitData.getSupplierId() != null){
            if (StringUtils.isNotEmpty(finalLimitData.getSupplierIds())){
                searchDto.setSupplierLimitDatas(CouponEsSearchDto.getListString(finalLimitData.getSupplierIds().split(",")));
            }else {
                searchDto.setSupplierLimitDatas(CouponEsSearchDto.getListString(String.valueOf(finalLimitData.getSupplierId())));
            }
            searchDto.setSupplierLimitType(CouponRuleLimitTypeEnum.ALL.getCode());
        }

        List<CouponEsIndexDto> couponBatchList = searchEsByLimit(searchDto);
        //过滤重复
        List<CouponEsIndexDto> returnData = couponBatchList.stream().filter(CollectionUtils.distinctByProperty(CouponEsIndexDto::getBatchNo)).collect(Collectors.toList());
        SkyLogUtils.infoByMethod(log, StringUtils.format("searchSingleEsBatchList->final:cost:{};thread:{};req:{};res:{}", (System.currentTimeMillis() - start), Thread.currentThread().getId(), queryDto, JSON.toJSONString(returnData)), "", queryDto.getRequestId());
        return returnData;
    }

    /**
     * 按es搜索
     *
     * @param requestId 请求id
     * @param platform  站台
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> searchEsByPlatform(String requestId, String platform) {
        CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, true);
        searchDto.setPlatforms(platform);
        searchDto.setRequestId(requestId);
        return searchEsBatchNo(searchDto);
    }

    @Override
    public List<CouponEsIndexDto> searchEsBatchListByPlatform(String requestId, String platform) {
        CouponEsSearchDto searchDto = CouponEsSearchDto.generate(BATCH_SIZE, false);
        searchDto.setPlatforms(platform);
        searchDto.setRequestId(requestId);
        return searchEsByLimit(searchDto);
    }

    private List<String> searchEsBatchNo(CouponEsSearchDto searchDto) {
        List<CouponEsIndexDto> couponEsIndexDtos = searchEsByLimit(searchDto);
        if (CollectionUtil.isEmpty(couponEsIndexDtos)) {
            return new ArrayList<>();
        }
        return couponEsIndexDtos.stream().map(CouponEsIndexDto::getBatchNo).collect(Collectors.toList());
    }

    @Override
    public List<CouponEsIndexDto> searchEsByLimit(CouponEsSearchDto searchDto) {
        EsSearchResult<Object> searchResult = EsSearchUtils.searchTemplete(EsSearchUtils.EsIndex.CouponBatchList, EsSearchUtils.EsTemplate.Coupon_Search_General, searchDto);
        SkyLogUtils.infoByMethod(log, StringUtils.format("searchByEs request:{} result:{}", JSON.toJSONString(searchDto), JSON.toJSONString(searchResult)), "", searchDto.getRequestId());
        if (searchResult != null && searchResult.getList() != null) {
            return searchResult.getList().stream().map(o -> {
                return JSON.parseObject(JSONObject.toJSONString(o), CouponEsIndexDto.class);
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 根据批号搜索es红包批次信息
     *
     * @param batchNos 批号
     * @return {@link List}<{@link CouponEsIndexDto}>
     */
    @Override
    public List<CouponEsIndexDto> searchEsByBatchNos(List<String> batchNos) {
        if (CollectionUtil.isEmpty(batchNos)) {
            return null;
        }
        String[] batchNoArray = batchNos.stream().distinct().toArray(String[]::new);
        CouponEsSearchBatchNoDto batchNoDto = CouponEsSearchBatchNoDto.builder()
                .batchNos(CouponEsSearchBatchNoDto.getListString(batchNoArray))
                .from(0)
                .size(batchNoArray.length + 10)
                .build();
        EsSearchResult<Object> searchResult = EsSearchUtils.searchTemplete(EsSearchUtils.EsIndex.CouponBatchList
                , EsSearchUtils.EsTemplate.Coupon_Search_Batchno
                , batchNoDto);
        if (searchResult != null && searchResult.getList() != null) {
            return searchResult.getList().stream().map(o -> {
                return JSON.parseObject(JSONObject.toJSONString(o), CouponEsIndexDto.class);
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<CouponEsIndexDto> searchEsBatchList(CouponBatchQueryDto queryDto) {
        return this.searchSingleEsBatchList(queryDto);
    }
}
