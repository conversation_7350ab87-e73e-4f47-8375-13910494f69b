package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.base.SysAppDto;
import com.ly.localactivity.marketing.domain.marketing.SysAppList;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * app信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
public interface ISysAppListService extends IService<SysAppList> {

    /**
     * 通过appid获取
     *
     * @param appId 应用程序id
     * @return {@link SysAppDto}
     */
    SysAppDto getByAppId(String appId);
}
