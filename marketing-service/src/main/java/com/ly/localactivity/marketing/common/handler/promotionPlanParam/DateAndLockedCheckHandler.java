package com.ly.localactivity.marketing.common.handler.promotionPlanParam;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionIsCanLockedRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionPlanSingleEditRo;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @description DateAndLockedCheckHandler
 * @date 2024-04-08
 */
@Component
@Slf4j
public class DateAndLockedCheckHandler extends AbstractCheckHandler {
    @Resource
    private IOperationPromotionPlanService operationPromotionPlanService;
    @Resource
    private DateAndLockedCheckHandler dateAndLockedCheckHandler;


    @Autowired
    public void init(DefaultPlanCheckHandler defaultPlanCheckHandler) {
        //从容器中获取下一个处理器
        nextHandler = defaultPlanCheckHandler;
    }

    /**
     * 处理器执行方法
     *
     * @param param param
     */
    @Override
    public void handle(OperationPromotionPlanSingleEditRo param, Set<String> accumulationSet) {
        if (!accumulationSet.add("DateAndLockedCheckHandler")) {
            return;
        }
        ;

        SkyLogUtils.infoByMethod(log, StringUtils.format("DateAndLockedCheckHandler[start],param:{}", JSON.toJSONString(param)), "", "");
        //兜底不做校验
        if (param.getDefaultFlag()) {
            dateAndLockedCheckHandler.next("DateAndLockedCheckHandler", param, accumulationSet);
            return;
        }
        //处理时间
        param.setValidBeginDate(LocalDateTimeUtil.beginOfDay(param.getValidBeginDate()));
        param.setValidEndDate(LocalDateTimeUtil.endOfDay(param.getValidEndDate(), Boolean.TRUE));
        boolean isEdit = ObjectUtil.isNotNull(param.getId()) && param.getId() > 0;
        LocalDateTime beginDate = isEdit ? param.getValidBeginDate() : LocalDateTime.now();
        beginDate = beginDate.isAfter(LocalDateTimeUtil.beginOfDay(LocalDateTime.now())) ? LocalDateTimeUtil.beginOfDay(LocalDateTime.now()) : beginDate;
        //开始时间必须大于当前时间/原有时间 精确到天;
        if (LocalDateTimeUtil.beginOfDay(param.getValidBeginDate()).isBefore(LocalDateTimeUtil.beginOfDay(beginDate))) {
            throw new ApiException("开始时间不合法");
        }
        if (LocalDateTimeUtil.endOfDay(param.getValidEndDate(), Boolean.TRUE).isBefore(LocalDateTimeUtil.endOfDay(LocalDateTime.now(), Boolean.TRUE))) {
            throw new ApiException("结束时间必须大于等于当前时间");
        }
        //判断是否存在时间交叉 >0 存在交叉
        if (operationPromotionPlanService.isTimeCross(param)) {
            throw new ApiException("当前时间段不可用");
        }

        //锁定状态校验
        if (param.getTopFlag() && !operationPromotionPlanService.isCanLocked(OperationPromotionIsCanLockedRo.builder()
                .platform(param.getPlatform())
                .planId(param.getId())
                .pointId(param.getPositionId())
                .indexValue(param.getIndexValue())
                .startTime(param.getValidBeginDate())
                .endTime(param.getValidEndDate())
                .build())) {
            throw new ApiException("当前不可被锁定");
        }
        dateAndLockedCheckHandler.next("DateAndLockedCheckHandler", param, accumulationSet);
    }
}
