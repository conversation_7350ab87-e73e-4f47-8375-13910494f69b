package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OperationPromotionPlanCoverConditionVo
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel(value = "OperationPromotionPlanCoverConditionVo", description = "当前顺位已经覆盖时间段返回参数")
public class OperationPromotionPlanCoverConditionVo {
    /**
     * 是否是锁定
     */
    @ApiModelProperty(value = "是否是锁定", required = true)
    private Boolean topFlag;
    /**
     * 已用时间段
     */
    @ApiModelProperty(value = "已用时间段和个数", required = true)
    private List<PeriodVo> periodList;

}
