package com.ly.localactivity.marketing.common.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CommonResultDto
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonResultDto {
    @ApiModelProperty(value = "领券是否成功")
    private Boolean isSuccess;
    @ApiModelProperty(value = "错误信息")
    private String message;

    @ApiModelProperty(value = "领券结果明细")
    private List<SingleCouponRechargeResult> recharegeDetailList;

    @Override
    public String toString(){
        return isSuccess + ";" + message;
    }

    public static CommonResultDto success() {
        return CommonResultDto.builder().isSuccess(true).build();
    }

    public static CommonResultDto error(String message) {
        return CommonResultDto.builder().isSuccess(false).message(message).build();
    }
}
