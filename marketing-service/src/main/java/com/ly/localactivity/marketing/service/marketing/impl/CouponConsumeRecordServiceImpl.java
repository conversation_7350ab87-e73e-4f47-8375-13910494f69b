package com.ly.localactivity.marketing.service.marketing.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.CouponConsumeStatusEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponConsumeRecord;
import com.ly.localactivity.marketing.mapper.marketing.CouponConsumeRecordMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponConsumeRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 红包使用记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Slf4j
@Service
public class CouponConsumeRecordServiceImpl extends ServiceImpl<CouponConsumeRecordMapper, CouponConsumeRecord> implements ICouponConsumeRecordService {

    /**
     * 消费优惠券
     *
     * @param batchNo     批号
     * @param couponCode  优惠券代码
     * @param orderAmount 订单金额
     * @param amount      金额
     */
    @Override
    public void consumeCoupon(String batchNo, String couponCode, BigDecimal orderAmount, BigDecimal amount) {
        CouponConsumeRecord couponConsumeRecord = getByCouponCode(couponCode, CouponConsumeStatusEnum.Consumed);
        if (couponConsumeRecord != null) {
            //已经有数据，并且状态是
            SkyLogUtils.errorByMethod(log, StringUtils.format("已经有数据，并且状态是已核销,batchNo:{},couponCode:{},orderAmount:{},amount:{}", batchNo, couponCode, orderAmount, amount)
                    , batchNo, couponCode, null);
            return;
        }

        couponConsumeRecord = new CouponConsumeRecord();
        couponConsumeRecord.setBatchNo(batchNo);
        couponConsumeRecord.setCouponCode(couponCode);
        couponConsumeRecord.setOrderAmount(orderAmount);
        couponConsumeRecord.setDiscountAmount(amount);
        couponConsumeRecord.setStatus(CouponConsumeStatusEnum.Consumed.getCode());
        couponConsumeRecord.setCreateTime(LocalDateTime.now());
        couponConsumeRecord.setCreator("");
        couponConsumeRecord.setModifiedTime(LocalDateTime.now());
        couponConsumeRecord.setModifier("");
        couponConsumeRecord.setDeleteFlag(false);
        baseMapper.insert(couponConsumeRecord);
    }

    /**
     * 返还优惠券
     *
     * @param couponCode 优惠券代码
     */
    @Override
    public void returnCoupon(String couponCode) {
        CouponConsumeRecord couponConsumeRecord = getByCouponCode(couponCode, CouponConsumeStatusEnum.Consumed);
        if (couponConsumeRecord == null) {
            SkyLogUtils.errorByMethod(log, StringUtils.format("没有数据,batchNo:{},couponCode:{}", couponCode)
                    , null, couponCode, null);
            return;
        }
        couponConsumeRecord.setStatus(CouponConsumeStatusEnum.Unconsumed.getCode());
        couponConsumeRecord.setModifiedTime(LocalDateTime.now());
        couponConsumeRecord.setModifier("");
        baseMapper.updateById(couponConsumeRecord);
    }

    /**
     * 通过优惠券代码获取
     *
     * @param couponCode 优惠券代码
     * @param statusEnum 状态枚举
     * @return {@link CouponConsumeRecord}
     */
    private CouponConsumeRecord getByCouponCode(String couponCode, CouponConsumeStatusEnum statusEnum) {
        LambdaQueryWrapper<CouponConsumeRecord> wrapper = new LambdaQueryWrapper<CouponConsumeRecord>()
                .eq(CouponConsumeRecord::getCouponCode, couponCode);
        if (statusEnum != null) {
            wrapper.eq(CouponConsumeRecord::getStatus, statusEnum.getCode());
        }
        wrapper.orderByDesc(CouponConsumeRecord::getId)
                .last("limit 1");
        return baseMapper.selectOne(wrapper);
    }
}
