package com.ly.localactivity.marketing.domain.marketing.ro;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * OperationPromotionLinkTemplateStatusRo
 *
 * <AUTHOR>
 * @date 2024-2-8
 */
@Data
@ApiModel(value = "OperationPromotionLinkTemplateStatusRo", description = "链接模板设为有效/无效请求参数")
public class OperationPromotionLinkTemplateStatusRo {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;


    /**
     * 链接模板是否有效（1正常 0停用）
     */
    @ApiModelProperty(value = "链接模板状态")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
