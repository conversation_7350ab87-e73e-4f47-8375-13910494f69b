package com.ly.localactivity.marketing.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 营销区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WCMarketArea")
public class WCMarketArea implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "MAId", type = IdType.AUTO)
    private Long MAId;

    /**
     * 名称
     */
    @TableField("MAName")
    private String MAName;

    /**
     * 创建人姓名
     */
    @TableField("MACreateName")
    private String MACreateName;

    /**
     * 创建人工号
     */
    @TableField("MACreateJobNum")
    private String MACreateJobNum;

    /**
     * 创建时间
     */
    @TableField("MACreateDate")
    private LocalDateTime MACreateDate;

    /**
     * 更新人姓名
     */
    @TableField("MAUpdateName")
    private String MAUpdateName;

    /**
     * 更新人工号
     */
    @TableField("MAUpdateJobNum")
    private String MAUpdateJobNum;

    /**
     * 更新时间
     */
    @TableField("MAUpdateDate")
    private LocalDateTime MAUpdateDate;

    /**
     * 有效性
     */
    @TableField("MADataFlag")
    private Integer MADataFlag;


}
