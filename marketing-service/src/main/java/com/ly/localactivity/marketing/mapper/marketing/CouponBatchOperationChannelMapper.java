package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperationChannel;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 红包批次运营渠道配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchOperationChannelMapper extends MyBaseMapper<CouponBatchOperationChannel> {

    /**
     * 按批次号列出refid
     *
     * @param batchNo 批号
     * @return {@link List}<{@link String}>
     */
    List<String> listRefidByBatchNo(@Param("batchNo") String batchNo);
}
