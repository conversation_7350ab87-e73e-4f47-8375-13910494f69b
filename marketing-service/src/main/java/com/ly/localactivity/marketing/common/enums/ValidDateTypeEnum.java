package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ValidDateTypeEnum
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@Getter
@AllArgsConstructor
public enum ValidDateTypeEnum {
    //1-固定日期段
    LONG_TERM(1, "固定日期段"),
    //2-动态有效期
    SPECIFIED_PERIOD(2, "动态有效期"),
    //3-按小时
    SPECIFIED_HOURS(3, "按小时"),
    //4-按分钟
    SPECIFIED_MINUTES(4, "按分钟"),
    ;
    private final Integer code;
    private final String desc;

}
