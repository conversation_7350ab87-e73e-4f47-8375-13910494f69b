package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * QueryCouponBatchListRo
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BaseRo", description = "base")
@Builder
public class BaseRo implements Serializable {
    private static final long serialVersionUID = 8507305919767614182L;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageIndex = 1;
    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小")
    private Integer pageSize = 10;

}
