package com.ly.localactivity.marketing.service.common;

import com.ly.localactivity.marketing.common.enums.AreaCoverageEnum;
import com.ly.localactivity.marketing.domain.common.WCCountryArea;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.common.vo.CountryAreaSimpleVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 国家地区表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IWCCountryAreaService extends IService<WCCountryArea> {

    /**
     * 通过IDs获取国家地区信息
     *
     * @param list
     * @return
     */
    List<CountryAreaSimpleVo> getByIds(List<Long> list);

    /**
     * 获取区域覆盖枚举
     *
     * @param countryArea 国家/地区
     * @return {@link AreaCoverageEnum}
     */
    AreaCoverageEnum getAreaCoverageEnum(WCCountryArea countryArea);

    WCCountryArea getAreaById(Long id);

    /**
     * 转换为map  prop-value
     *
     * @param countryArea 国家/地区
     * @return {@link Map }<{@link String },{@link String }>
     */
    Map<String, String> convertToMap(WCCountryArea countryArea);
}
