package com.ly.localactivity.marketing.domain.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OperationPositionCouponDto
 *
 * <AUTHOR>
 * @date 2024/5/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationPositionCouponDto {
    private String positionCode;
    private String refid;
    private String platform;
    private String openId;
    private String unionId;
    private String deviceId;
    private Long memberId;
}
