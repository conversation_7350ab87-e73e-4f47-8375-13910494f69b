package com.ly.localactivity.marketing.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.marketing.common.enums.AreaCoverageEnum;
import com.ly.localactivity.marketing.domain.common.WCCountryArea;
import com.ly.localactivity.marketing.domain.common.vo.CountryAreaSimpleVo;
import com.ly.localactivity.marketing.mapper.common.WCCountryAreaMapper;
import com.ly.localactivity.marketing.service.common.IWCCountryAreaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 国家地区表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class WCCountryAreaServiceImpl extends ServiceImpl<WCCountryAreaMapper, WCCountryArea> implements IWCCountryAreaService {

    /**
     * 港澳台省份id
     */
    private static final List<Long> GAT_PROVINCE_IDS = CollectionUtil.newArrayList(33L, 34L, 35L);

    @Override
    public List<CountryAreaSimpleVo> getByIds(List<Long> list) {
        List<WCCountryArea> countryAreas = baseMapper.selectBatchIds(list);
        if (CollectionUtil.isEmpty(countryAreas)) {
            return Collections.emptyList();
        }
        return countryAreas.stream().map(ca -> CountryAreaSimpleVo
                        .builder()
                        .id(ca.getCAId())
                        .name(ca.getCAName())
                        .shortName(ca.getCAShortName())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public AreaCoverageEnum getAreaCoverageEnum(WCCountryArea countryArea) {
        if (countryArea == null) {
            return null;
        }

        if (countryArea.getCACountryId() == 1L) {
            if (!GAT_PROVINCE_IDS.contains(countryArea.getCAProvinceId())) {
                //境内
                return AreaCoverageEnum.DOMESTIC;
            } else {
                //港澳
                return AreaCoverageEnum.HK_MACAO;
            }
        }
        //境外
        return AreaCoverageEnum.FOREIGN;
    }

    @Override
    @LACacheable(key = "'getAreaById:' + #id", expire = 60 * 60 * 24)
    public WCCountryArea getAreaById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Map<String, String> convertToMap(WCCountryArea wcCountryArea) {
        return Optional.ofNullable(wcCountryArea).map(countryArea -> {
            Map<String, String> map = new HashMap<>(4);
            map.put("cityId", Optional.ofNullable(countryArea.getCACityId()).map(String::valueOf).orElse(StrUtil.EMPTY));
            map.put("cityName", Optional.ofNullable(countryArea.getCACityName()).orElse(StrUtil.EMPTY));
            map.put("provinceId", Optional.ofNullable(countryArea.getCAProvinceId()).map(String::valueOf).orElse(StrUtil.EMPTY));
            map.put("provinceName", Optional.ofNullable(countryArea.getCAProvinceName()).map(String::trim).orElse(StrUtil.EMPTY));
            map.put("countryId", Optional.ofNullable(countryArea.getCACountryId()).map(String::valueOf).orElse(StrUtil.EMPTY));
            map.put("countryName", Optional.ofNullable(countryArea.getCACountryName()).map(String::trim).orElse(StrUtil.EMPTY));
            return map;
        }).orElse(Collections.emptyMap());
    }
}
