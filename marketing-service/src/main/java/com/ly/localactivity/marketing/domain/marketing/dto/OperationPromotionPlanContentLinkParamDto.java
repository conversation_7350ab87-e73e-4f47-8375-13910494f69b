package com.ly.localactivity.marketing.domain.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OperationPromotionPlanContentLinkParamDto
 *
 * <AUTHOR>
 * @date 2024/2/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationPromotionPlanContentLinkParamDto {
    /**
     * 计划id
     */
    private Long planId;
    /**
     * 内容id
     */
    private Long planContentId;
    /**
     * 链接模版id
     */
    private Long linkTemplateParamId;
    /**
     * 参数code
     */
    private String paramCode;
    /**
     * 参数值
     */
    private String paramValue;
    /**
     * 参数类型
     */
    private Integer valueType;
}
