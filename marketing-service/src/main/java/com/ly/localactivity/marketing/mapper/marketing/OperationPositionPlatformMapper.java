package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPositionPlatform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作位置平台测绘仪
 * <p>
 * 营销运营位平台 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/02/01
 * @since 2024-01-31
 */
public interface OperationPositionPlatformMapper extends BaseMapper<OperationPositionPlatform> {
    /**
     * 批量插入
     */
    void insertBatch(@Param("list") List<OperationPositionPlatform> operationPositionPlatform);

}
