package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CouponReturnRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponReturnRo", description = "退券")
@AllArgsConstructor
@NoArgsConstructor
@Data
@SuperBuilder
public class CouponReturnRequest extends CouponBaseRequest{
    /**
     * 券号
     */
    @ApiModelProperty(value = "券号",required = true)
    @NotNull(message = "券号不能为空")
    @NotBlank(message = "券号不能为空")
    private String couponNo;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号",required = true)
    @NotNull(message = "订单号不能为空")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
}
