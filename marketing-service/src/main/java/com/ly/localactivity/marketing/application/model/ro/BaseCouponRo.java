package com.ly.localactivity.marketing.application.model.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * BaseCouponRo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseCouponRo extends BaseSignRequest {
    @NotNull(message = "项目类型不能为空")
    @ApiModelProperty(value = "项目类型，1-日游")
    private Integer projectType;
    @NotNull(message = "平台不能为空")
    @ApiModelProperty(value = "平台")
    private Integer platform;
    @ApiModelProperty(value = "refid")
    private String refid;
    @ApiModelProperty(value = "memberId")
    private Long memberId;
    @ApiModelProperty(value = "deviceId，app必传")
    private String deviceId;
    @ApiModelProperty(value = "openId，微信平台必传")
    private String openId;
    @ApiModelProperty(value = "unionId，微信平台必传")
    private String unionId;
}
