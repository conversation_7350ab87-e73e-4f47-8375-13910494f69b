package com.ly.localactivity.marketing.application.model.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;

import java.time.LocalDateTime;

/**
 * PromotionPlanVo
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromotionPlanApiVo {

    //    @JSONField(serialize = false)
    @ApiModelProperty(value = "id", hidden = true)
    private Long id;

    @ApiModelProperty(value = "推广计划code")
    private String code;

    @ApiModelProperty(value = "推广计划名称")
    private String name;
    @ApiModelProperty(value = "推广计划自定义名称")
    private String alias;

//    @JSONField(serialize = false)
    @ApiModelProperty(value = "运营点位 code")
    private String positionCode;
    @ApiModelProperty(value = "运营点位名称")
    private String positionName;

    @ApiModelProperty(value = "平台")
    private String platform;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "输出类型，1-幻灯片，2-图片, 3-icon, 4-icon+关键词，5-关键词", hidden = true)
    private Integer outputType;

    /**
     * 推广方式，1-按境内（全国），2-按境外（港澳），3-按大区，4-按省份，4-按城市
     */
    @JSONField(serialize = false)
    @ApiModelProperty(value = "promote_type", hidden = true)
    private Integer promoteType;

    @ApiModelProperty(value = "顺序值")
    private Integer indexValue;

    @ApiModelProperty(value = "标题或关键字，输出类型=4/5时才有值")
    private String keyword;
    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "图片名称", hidden = true)
    private String imageName;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "图片上传信息", hidden = true)
    private String imageInfo;
    @ApiModelProperty(value = "链接地址")
    private String linkUrl;

    /**
     * 有效期类型，1-长期有效，2-固定日期段
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer validDateType;

    /**
     * 有效期开始
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private LocalDateTime validBeginDate;

    /**
     * 有效期结束
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private LocalDateTime validEndDate;

    /**
     * 置顶标记，1-是，0-否
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer topFlag;

    /**
     * 展示方式，1-最后顺位，2-兜底展示
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer defaultShowType;

    /**
     * 兜底标记，1-是，0-否
     */
    @JSONField(serialize = false)
    @ApiModelProperty(value = "兜底标记")
    private Integer defaultFlag;

    /**
     * 推广原因，数据字典维护
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private String reasonType;

    /**
     * 推广原因备注
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private String reasonRemark;

    /**
     * 有效状态，1-有效，0-无效
     */
    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer validFlag;

//    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer indexSortNo;

    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer promotionTypeSortNo;

    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Integer channelSortNo;

    @JSONField(serialize = false)
    @ApiModelProperty(hidden = true)
    private Boolean limitChannel = Boolean.FALSE;

    private String randomKey;
}
