package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponBatchRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 红包使用规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchRuleMapper extends BaseMapper<CouponBatchRule> {

    List<CouponBatchRule> listByBatchNo(@Param("batchNo") String batchNo);

    List<CouponBatchRule> listByBatchNos(@Param("batchNos") List<String> batchNos);
}
