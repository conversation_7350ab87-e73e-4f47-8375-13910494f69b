package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentDto;

/**
 * <p>
 * 运营推广计划内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface IOperationPromotionPlanContentService extends IService<OperationPromotionPlanContent> {

    /**
     * 保存/修改
     *
     * @param contentDto 内容dto
     * @param modifier   修改人
     */
    void saveOrUpdate(OperationPromotionPlanContentDto contentDto, String modifier);

    /**
     * 按计划id获取
     *
     * @param planId 计划id
     * @return {@link OperationPromotionPlanContentDto}
     */
    OperationPromotionPlanContentDto getByPlanId(Long planId);

    /**
     * 同步字段
     *
     * @return {@link String }
     */
    String syncField();

}
