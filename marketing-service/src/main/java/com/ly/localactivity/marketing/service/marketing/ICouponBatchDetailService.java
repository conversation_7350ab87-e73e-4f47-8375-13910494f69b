package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.ro.BatchPushCouponRo;
import com.ly.localactivity.marketing.domain.marketing.ro.QueryCouponBatchDetailListRo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包批次明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchDetailService extends IService<CouponBatchDetail> {

    /**
     * 保存或更新
     *
     * @param couponBatchDetailList
     * @param couponBatchList
     * @return {@link Long}
     */
    Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchDetailDto> couponBatchDetailList);

    /**
     * 根据主键删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<Long> ids);

    /**
     * 根据红包批次id查询红包批次明细
     *
     * @param couponBatchIdList 优惠券批次id列表
     * @return {@link Map}<{@link Long}, {@link List}<{@link CouponBatchDetailDto}>>
     */
    Map<Long, List<CouponBatchDetailDto>> selectByCouponBatchId(List<Long> couponBatchIdList);

    /**
     * 根据批次号查询红包批次明细
     *
     * @param batchNoList 批次号列表
     * @param isDelete
     * @return {@link List}<{@link CouponBatchDetailDto}>
     */
    List<CouponBatchDetailDto> selectByBatchNoList(List<String> batchNoList, Boolean isDelete);

    /**
     * 更新红包批次明细
     * @param couponBatchDetailDto
     * @return
     */
    Boolean updateDetail(CouponBatchDetailDto couponBatchDetailDto);

    /**
     * 分页查询红包批次明细列表
     *
     * @param request
     * @return
     */
    CommonPage<CouponBatchDetailDto> pageQuery(QueryCouponBatchDetailListRo request);

    /**
     * 计算红包营销状态
     * @param couponBatchDetailDto
     */
    void calcDetailMarketingStatus(CouponBatchDetailDto couponBatchDetailDto);

    /**
     * 查询需要刷新的红包批次明细
     * @param request
     * @return
     */
    List<CouponBatchDetail> selectNeedRefreshCouponBatchDetailList(BatchPushCouponRo request);

    /**
     * 查询红包批次明细
     * @param batchNo
     * @return
     */
    CouponBatchDetail selectByBatchNo(String batchNo);
}
