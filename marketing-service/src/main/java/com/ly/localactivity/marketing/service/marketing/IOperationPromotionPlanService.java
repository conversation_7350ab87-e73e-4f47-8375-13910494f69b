package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanPageDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.*;

import java.util.List;

/**
 * <p>
 * 运营推广计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface IOperationPromotionPlanService extends IService<OperationPromotionPlan> {

    /**
     * 批量保存
     *
     * @param addRoList 列表
     * @return {@link Boolean}
     */
    Boolean saveBatch(OperationPromotionPlanBatchAddRo addRoList);


    /**
     * 保存计划
     *
     * @param request  请求
     * @param modifier 修改人
     * @param single   单条
     * @return Long
     */
    Long saveSinglePlan(OperationPromotionPlanSingleEditRo request, String modifier,Boolean single);

    /**
     * 是否可以锁定
     *
     * @param request 请求
     * @return boolean
     */
    boolean isCanLocked(OperationPromotionIsCanLockedRo request);

    /**
     * 获取详细信息
     *
     * @param planId 计划id
     * @return {@link OperationPromotionPlanDto}
     */
    OperationPromotionPlanSingleEditVo getDetail(Long planId);

    /**
     * 获取推广计划列表
     *
     * @param request 请求
     * @return {@link CommonPage}<{@link OperationPromotionPlanPageVo}>
     */
    CommonPage<OperationPromotionPlanPageVo> pageList(OperationPromotionPlanPageDto request);

    /**
     * 获取不可用日期
     *
     * @param request 请求
     * @return {@link List}<{@link PeriodVo}>
     */
    List<PeriodVo> getUnavailableDate(OperationPromotionPlanUnavailableDateRo request);

    /**
     * 获取锁定的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    List<String> listLockedIndex(OperationPromotionListLockedIndexRo request);

    /**
     * 获取存在占位的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    List<String> listPlaceholderIndex(OperationPromotionListPlaceholderIndexRo request);

    /**
     * 获取已经维护的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    List<String> listMaintainIndex(OperationPromotionListMaintainIndexRo request);

    /**
     * 获取当前顺位,所选推广时间推广计划覆盖情况
     *
     * @param request 请求
     * @return {@link OperationPromotionPlanCoverConditionVo}
     */
    List<OperationPromotionPlanCoverConditionVo> getCoverCondition(OperationPromotionPlanCoverConditionRo request);

    /**
     * 获取计划img列表
     *
     * @param positionId 位置id
     * @param planId     计划id
     * @return {@link List}<{@link ImageVo}>
     */
    List<ImageVo> getPlanImgList(Long positionId, Long planId);


    /**
     * 修改推广计划有效状态
     *
     * @param request  请求
     * @return boolean
     */
    boolean changeValid(OperationPromotionPlanValidRo request);

    /**
     * 获取推广计划计划统计信息
     *
     * @param request 请求
     * @return {@link PromotionPlanStatisticsVo}
     */
    PromotionPlanStatisticsVo getPromotionPlanStatistics(PromotionPlanStatisticsRo request);


    /**
     * 获取最晚过期的天数
     *
     * @param positionId 位置id
     * @return {@link Integer}
     */
    Integer getLatestExpireDay(Long positionId);

    /**
     * 自动无效
     *
     * @param planId 计划id
     * @param reason 原因
     * @return boolean
     */
    boolean invalidByAuto(Long planId,String reason);

    /**
     * 获取数量
     *
     * @param positionId 位置id
     * @return int
     */
    int getCount(Long positionId);

    /**
     * 判断是否存在时间交叉
     *
     * @param editRo 编辑ro
     * @return boolean
     */
    boolean isTimeCross(OperationPromotionPlanSingleEditRo editRo);


}
