package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponPackageInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponPackageInfoDto", description = "券包信息")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CouponPackageInfoExtDto {
    /**
     * 包名
     */
    @ApiModelProperty(value = "礼包名称")
    private String packageName;
    /**
     * 包装编号
     */
    @ApiModelProperty(value = "礼包号")
    private String packageNo;
    /**
     * 批处理列表
     */
    private List<CouponInfoDetailExtDto> batchList;
}
