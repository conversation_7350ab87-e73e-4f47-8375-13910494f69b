package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * OperationStatusEnum
 *
 * <AUTHOR>
 * @date 2024-2-1
 */
@Getter
@AllArgsConstructor
public enum PlanDefaultShowTypeEnum {
    INDEX_SHOW(1, "最后顺位"),
    DEFAULT_SHOW(2, "兜底展示"),
            ;
    private Integer code;
    private String name;
    public static String codeToName(Integer code) {
        PlanDefaultShowTypeEnum anEnum = codeOf(code);
        return Objects.isNull(anEnum) ? null : anEnum.getName();
    }
    public static PlanDefaultShowTypeEnum codeOf(Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
