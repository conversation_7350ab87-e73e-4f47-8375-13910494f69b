package com.ly.localactivity.marketing.service.marketing.impl;

import com.ly.localactivity.marketing.domain.marketing.OperationPositionPlatform;
import com.ly.localactivity.marketing.mapper.marketing.OperationPositionPlatformMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionPlatformService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 营销运营位平台 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
public class OperationPositionPlatformServiceImpl extends ServiceImpl<OperationPositionPlatformMapper, OperationPositionPlatform> implements IOperationPositionPlatformService {
    @Resource
    private OperationPositionPlatformMapper operationPositionPlatformMapper;

    @Override
    public OperationPositionPlatformMapper getMapper() {
        return this.operationPositionPlatformMapper;
    }

    @Override
    public void insertBatch(List<OperationPositionPlatform> operationPositionPlatform) {
        operationPositionPlatformMapper.insertBatch(operationPositionPlatform);
    }


}
