package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRule;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRuleDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包使用规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchRuleService extends IService<CouponBatchRule> {


    /**
     * 保存或更新
     * @param couponBatchList
     * @param couponBatchRuleDto
     * @return
     */
    Boolean saveOrUpdate(CouponBatchList couponBatchList, CouponBatchRuleDto couponBatchRuleDto);

    /**
     * 根据红包id查询
     * @param couponBatchIdList
     * @return
     */
    Map<Long, CouponBatchRuleDto> selectByCouponBatchId(List<Long> couponBatchIdList);
}
