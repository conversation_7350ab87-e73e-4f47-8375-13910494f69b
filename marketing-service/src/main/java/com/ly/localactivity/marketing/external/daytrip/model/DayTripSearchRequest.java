package com.ly.localactivity.marketing.external.daytrip.model;

import com.ly.localactivity.marketing.external.daytrip.enums.ProductTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DayTripSearchRequest
 *
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DayTripSearchRequest {
    private String keyword;
    private List<String> poiIds;
    private List<Long> destinationIds;
    private LocalDateTime dateTime;
    private ProductTypeEnum productType;
    private int limit;
    private String sortType;
}
