package com.ly.localactivity.marketing.common.constants;

/**
 * <AUTHOR>
 * @description RedisConstants
 * @date 2024-07-26
 */
public interface RedisConstants {
    String GET_OPERATION_POSITION_API_VO_CACHE_DATA_KEY_PREFIX = "get:operation:position:api:vo:cache:data:";
    String GET_OPERATION_POSITION_API_VO_CACHE_VERSION_KEY_PREFIX = "get:operation:position:api:vo:cache:version:";
    String GET_PROMOTION_PLAN_API_VO_LIST_CACHE_DATA_KEY_PREFIX = "get:promotion:plan:api:vo:list:cache:data:";
    String GET_PROMOTION_POSITION_API_VO_LIST_CACHE_DATA_KEY_PREFIX = "get:promotion:position:api:vo:list:cache:data:";
    String OPERATION_CACHE_REFRESH_LIMIT_KEY = "operation:cache:refresh:limit";

    /**
     * 红包会员接口缓存
     */
    String COUPON_ALL_MEMBER_INFO_PREFIX = "coupon:all:member:info:";
}
