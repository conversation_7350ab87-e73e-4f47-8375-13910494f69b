package com.ly.localactivity.marketing.service.coupon.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.annotation.LARedisLock;
import com.ly.localactivity.framework.utils.*;
import com.ly.localactivity.marketing.common.bean.CommonResultDto;
import com.ly.localactivity.marketing.common.bean.SingleCouponRechargeResult;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.enums.CouponRechargeSyncStatusEnum;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.WhetherEnum;
import com.ly.localactivity.marketing.domain.marketing.CouponRechargeRecord;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoExtDto;
import com.ly.localactivity.marketing.external.coupon.enums.CouponResultCodeEnum;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.CouponRechargeRequest;
import com.ly.localactivity.marketing.external.coupon.service.CouponCoreService;
import com.ly.localactivity.marketing.service.coupon.ICouponBatchStockControlService;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponRechargeService;
import com.ly.localactivity.marketing.service.marketing.ICouponRechargeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 红包发送服务
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@Service
@Slf4j
public class CouponRechargeServiceImpl implements ICouponRechargeService {
    @Resource
    private CouponCoreService couponCoreService;
    @Resource
    private ICouponRechargeRecordService couponRechargeRecordService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private ICouponBatchStockControlService couponBatchStockControlService;

    private final static String[] NEED_REFRESH_CODE = new String[]{
            CouponResultCodeEnum.Fail_2001.getCode()
            ,CouponResultCodeEnum.Fail_2007.getCode()
    };

    /**
     * 签名KEY
     */
    private final static String SIGN_KEY = "FC1mwZaiBZ1GtA4m58vt";


    /**
     * 发送红包
     *
     * @param couponRechargeDto
     * @param repeatCheck
     */
    @LARedisLock(keyPrex = "'LK_coupon_recharge_' + #couponRechargeDto.cacheKey")
    @Override
    public CommonResultDto recharge(CouponRechargeDto couponRechargeDto, Boolean repeatCheck) {
        SkyLogUtils.infoByMethod(log, MessageFormat.format("发送红包，请求内容:{0}。", JSON.toJSONString(couponRechargeDto)), couponRechargeDto.getTradeNo(), "");

        //控制相同请求不重复发送，默认30分钟
        String key = "coupon_recharge2_" + couponRechargeDto.getCacheKey();
        if (redisUtils.exists(key) && repeatCheck) {
            SkyLogUtils.warnByMethod(log, MessageFormat.format("发送红包，重复请求，请求内容:{0}。", JSON.toJSONString(couponRechargeDto)), couponRechargeDto.getTradeNo(), "");
            return CommonResultDto.error("重复请求");
        }
        if (repeatCheck) {
            String interval = ConfigUtils.get(ConfigConst.COUPON_RECHARGE_REPEAT_INTERVAL, "");
            int time = 60 * 30;
            if (StringUtils.hasText(interval)) {
                time = Convert.toInt(interval, 60 * 30);
            }
            redisUtils.setnx(key, JSON.toJSONString(couponRechargeDto), time);
        }

        // 参数校验
        if (!checkRechargeParams(couponRechargeDto)) {
            redisUtils.del(key);
            return CommonResultDto.error("参数校验失败");
        }
        Boolean success = true;
        StringBuilder errorMsg = new StringBuilder();
        // 优先使用入参tradeNo，如果没有则生成
        String tradeNo = StringUtils.hasText(couponRechargeDto.getTradeNo()) ? couponRechargeDto.getTradeNo() : IdUtil.simpleUUID();

        List<SingleCouponRechargeResult> recharegeDetailList = new ArrayList<>();
        for (String batchNo : couponRechargeDto.getBatchNo()) {
            // 红包接口数据实体
            CouponRechargeRequest couponRechargeRo = new CouponRechargeRequest();
            if (couponRechargeDto.getPlatform().equals(PlatformEnum.Wx_App.getCode())) {
                couponRechargeRo.setSource(1);
                couponRechargeRo.setUserkey(couponRechargeDto.getUnionId());
            } else if (couponRechargeDto.getPlatform().equals(PlatformEnum.App_Android.getCode())
                    || couponRechargeDto.getPlatform().equals(PlatformEnum.App_IOS.getCode())) {
                couponRechargeRo.setSource(2);
                couponRechargeRo.setUserkey(couponRechargeDto.getMemberId().toString());
            }
            couponRechargeRo.setOpenId(couponRechargeDto.getOpenId());
            couponRechargeRo.setCurrentTime(LocalDateTimeUtils.getString(LocalDateTime.now()));
            couponRechargeRo.setBatchNo(batchNo);
            if (ObjectUtil.isNotNull(couponRechargeDto.getIsPackage())) {
                couponRechargeRo.setIsPackage(couponRechargeDto.getIsPackage() ? WhetherEnum.YES.getCode() : WhetherEnum.NO.getCode());
            }
            // tradeNo + 批次号索引
            couponRechargeRo.setTradeNo(tradeNo + "-" + (couponRechargeDto.getBatchNo().indexOf(batchNo) + 1));
            couponRechargeRo.setOrderNo(couponRechargeDto.getOrderNo());
            couponRechargeRo.setDeviceId(couponRechargeDto.getDeviceId());
            couponRechargeRo.setRefid(couponRechargeDto.getRefId());
            CouponCommonResult<List<CouponInfoExtDto>> couponCommonResult = couponCoreService.recharge(couponRechargeRo);
            ThreadUtil.execAsync(() -> {
                try {
                    //判断是否需要刷新红包
                    refreshBatch(couponCommonResult, batchNo);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
            // 领券明细结果
            SingleCouponRechargeResult singleCouponRechargeResult = new SingleCouponRechargeResult();
            singleCouponRechargeResult.setBatchNo(batchNo);
            if (couponCommonResult != null){
                singleCouponRechargeResult.setSuccess(Boolean.TRUE.equals(couponCommonResult.getSuccess()));
                singleCouponRechargeResult.setErrorCode(couponCommonResult.getCode());
                singleCouponRechargeResult.setErrorMessage(couponCommonResult.getMsg());
            }
            recharegeDetailList.add(singleCouponRechargeResult);
            // 数据库实体
            CouponRechargeRecord couponRechargeRecord = new CouponRechargeRecord();
            if (couponCommonResult == null || !Boolean.TRUE.equals(couponCommonResult.getSuccess())) {
                SkyLogUtils.warnByMethod(log, MessageFormat.format("发送红包，调用接口失败，请求内容:{0}，批次号:{1}，tradeNo:{2}，result:{3}"
                                , JSON.toJSONString(couponRechargeRo)
                                , batchNo
                                , couponRechargeRo.getTradeNo()
                                , JSON.toJSONString(couponCommonResult))
                        , couponRechargeDto.getTradeNo()
                        , couponRechargeRo.getUserkey());
                couponRechargeRecord.setSyncStatus(CouponRechargeSyncStatusEnum.FAIL.getCode());
                couponRechargeRecord.setSyncResult(couponCommonResult.getMsg());
                if (errorMsg.length() > 0) {
                    errorMsg.append(";");
                }
                errorMsg.append(StrUtil.format("[{}]{}", batchNo, couponCommonResult.getMsg()));
                success = false;
            } else {
                SkyLogUtils.infoByMethod(log, MessageFormat.format("发送红包，调用接口成功，请求内容:{0}，批次号:{1}，tradeNo:{2}，result:{3}"
                                , JSON.toJSONString(couponRechargeRo)
                                , batchNo
                                , couponRechargeRo.getTradeNo()
                                , JSON.toJSONString(couponCommonResult))
                        , couponRechargeDto.getTradeNo()
                        , couponRechargeRo.getUserkey());
                couponRechargeRecord.setSyncStatus(CouponRechargeSyncStatusEnum.SUCCESS.getCode());
                couponRechargeRecord.setSyncResult("发送成功");
            }
            couponRechargeRecord.setBatchNo(batchNo);
            couponRechargeRecord.setPlatform(couponRechargeDto.getPlatform());
            couponRechargeRecord.setRefid(couponRechargeDto.getRefId());
            couponRechargeRecord.setTradeNo(couponRechargeRo.getTradeNo());
            couponRechargeRecord.setOrderNo(couponRechargeDto.getOrderNo());
            couponRechargeRecord.setSource(couponRechargeRo.getSource());
            couponRechargeRecord.setUserKey(couponRechargeRo.getUserkey());
            couponRechargeRecord.setOpenId(couponRechargeDto.getOpenId());
            couponRechargeRecord.setDeviceId(couponRechargeDto.getDeviceId());
            if (ObjectUtil.isNotNull(couponRechargeDto.getIsPackage())) {
                couponRechargeRecord.setPackageFlag(couponRechargeDto.getIsPackage() ? WhetherEnum.YES.getCode() : WhetherEnum.NO.getCode());
            }
            couponRechargeRecord.setSendFrom(couponRechargeDto.getFrom());
            couponRechargeRecord.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            couponRechargeRecord.setCreator("");
            couponRechargeRecord.setCreateTime(LocalDateTime.now());
            couponRechargeRecord.setModifier("");
            couponRechargeRecord.setModifiedTime(LocalDateTime.now());
            couponRechargeRecordService.insertCouponRechargeRecord(couponRechargeRecord);
            SkyLogUtils.infoByMethod(log, MessageFormat.format("发送红包完成，请求内容:{0}，批次号:{1}，tradeNo:{2}"
                            , JSON.toJSONString(couponRechargeRo)
                            , batchNo
                            , couponRechargeRo.getTradeNo())
                    , couponRechargeDto.getTradeNo()
                    , couponRechargeRo.getUserkey());
        }

        CommonResultDto commonResultDto = success ? CommonResultDto.success() : CommonResultDto.error(errorMsg.toString());
        commonResultDto.setRecharegeDetailList(recharegeDetailList);
        return commonResultDto;
    }

    /**
     * 检查发送红包参数
     *
     * @param couponRechargeDto 优惠券充值dto
     * @return {@link Boolean}
     */
    public Boolean checkRechargeParams(CouponRechargeDto couponRechargeDto) {
        String errMsg = "";
        if (CollectionUtils.isEmpty(couponRechargeDto.getBatchNo())) {
            errMsg = "批次号为空";
        } else if (!StringUtils.hasText(couponRechargeDto.getFrom())) {
            errMsg = "来源为空";
        } else if (!StringUtils.hasText(couponRechargeDto.getPlatform())) {
            errMsg = "平台为空";
        } else if (!couponRechargeDto.getPlatform().equals(PlatformEnum.Wx_App.getCode())
                && !couponRechargeDto.getPlatform().equals(PlatformEnum.App_Android.getCode())
                && !couponRechargeDto.getPlatform().equals(PlatformEnum.App_IOS.getCode())) {
            errMsg = "平台不存在";
        } else if (couponRechargeDto.getPlatform().equals(PlatformEnum.Wx_App.getCode()) && !StringUtils.hasText(couponRechargeDto.getUnionId())) {
            errMsg = "微信小程序用户，unionId不能为空";
        }
//        else if (couponRechargeDto.getPlatform().equals(PlatformEnum.Wx_App.getCode()) && !StringUtils.hasText(couponRechargeDto.getOpenId())) {
//            errMsg = "微信小程序用户，openId不能为空";
//        }
        else if ((couponRechargeDto.getPlatform().equals(PlatformEnum.App_Android.getCode()) || couponRechargeDto.getPlatform().equals(PlatformEnum.App_IOS.getCode()))
                && (couponRechargeDto.getMemberId() == null || couponRechargeDto.getMemberId().equals(0))) {
            errMsg = "APP用户，会员Id不能为空";
        } else if (!StringUtils.hasText(couponRechargeDto.getSign())) {
            errMsg = "签名为空";
        }
        if (errMsg != "") {
            SkyLogUtils.warnByMethod(log, MessageFormat.format("发送红包，参数检验错误，错误原因：{0}，请求内容:{1}。", errMsg, JSON.toJSONString(couponRechargeDto)), "", "");
            return false;
        }
        // 签名校验
        String sign = SignUtils.getSign(SIGN_KEY, (JSONObject) JSON.toJSON(couponRechargeDto));
        if (!sign.equals(couponRechargeDto.getSign())) {
            SkyLogUtils.warnByMethod(log, MessageFormat.format("发送红包，签名校验错误，请求内容:{1}。", errMsg, JSON.toJSONString(couponRechargeDto)), "", "");
            return false;
        }
        return true;
    }

    private void refreshBatch(CouponCommonResult<List<CouponInfoExtDto>> couponCommonResult, String batchNo) {
        if (couponCommonResult == null || StrUtil.isBlank(couponCommonResult.getCode())) {
            return;
        }
        SkyLogUtils.infoByMethod(log, StrUtil.format("发送红包失败，刷新红包批次缓存，批次号:{}，result:{}", batchNo, JSON.toJSONString(couponCommonResult)), batchNo, "");
        //库存限制问题就刷新库存
        if (couponCommonResult.getCode().equals(CouponResultCodeEnum.Fail_2007.getCode())) {
            //总库存受限
            couponBatchStockControlService.setCouponStockLimit(batchNo);
        } else if (couponCommonResult.getCode().equals(CouponResultCodeEnum.Fail_2008.getCode())) {
            //当日受限
            couponBatchStockControlService.setCouponDayLimit(batchNo);
        }

        if (!couponCommonResult.getCode().equals(CouponResultCodeEnum.Success.getCode())) {
            //只要红包发送失败，就刷新缓存
            couponQueryService.refreshCache(batchNo);
        }
    }
}
