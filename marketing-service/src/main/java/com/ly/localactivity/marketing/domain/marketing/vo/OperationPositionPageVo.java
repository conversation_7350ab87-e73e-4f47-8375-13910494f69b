package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OperationPositionPageVo
 *
 * <AUTHOR>
 * @date 2024-1-31
 */

@Data
@ApiModel(value = "OperationPositionPageVo",description = "频道页编辑/新增返回参数")
public class OperationPositionPageVo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "频道页id")
    private Long id;

    /**
     * 频道页面名称
     */
    @ApiModelProperty(value = "频道页面名称")
    private String name;


    /**
     * code
     */
    @ApiModelProperty(value = "频道页code")
    private String code;


    /**
     * 频道页版本
     */
    @ApiModelProperty(value = "频道页版本")
    private String version;

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    private Integer project;
    /**
     * 所属项目名称
     */
    @ApiModelProperty(value = "所属项目名称")
    private String projectName;

    /**
     * 频道页排序值
     */
    @ApiModelProperty(value = "频道页排序值")
    private Integer sortNo;

    /**
     * 频道页是否有效（1正常 0停用）
     */
    @ApiModelProperty(value = "频道页状态")
    private Integer status;
}
