package com.ly.localactivity.marketing.service.mq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.mq.LAMqListener;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchSyncDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * AdminMqListener
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@ConditionalOnProperty(prefix = "local-activity", name = "appName", havingValue = "tcscenery.java.localactivity.marketing.admin")
@Service
@Slf4j
public class AdminMqListener {

    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private ICouponSyncService couponSyncService;

    @LAMqListener(topic = MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC, consumerGroup = MQConst.GROUP_LA_MARKETING_ADMIN_DEFAULT)
    public void listenCouponBatchInfo(MessageExt message) {
        //刷新红包信息
        CouponBatchSyncDto syncDto = formatMsg(message, CouponBatchSyncDto.class);
        if (syncDto == null || StringUtils.isBlank(syncDto.getBatchNo())) {
            return;
        }
        SkyLogUtils.infoByMethod(log, "开始刷新红包批次信息", syncDto.getBatchNo(), "");
        // 先查询是否删除
        boolean needQueryKunPeng = couponSyncService.checkIsNeedQueryKunPeng(syncDto.getBatchNo());
        if (needQueryKunPeng){
            //再从鲲鹏获取红包信息
            CouponBatchExtDto couponBatchExtDto = couponQueryService.getExternalCoupon(syncDto.getBatchNo());
            if (couponBatchExtDto == null){
                return;
            }
            // 刷新本地红包信息信息
            couponSyncService.refreshKunPengCouponDetailForDb(couponBatchExtDto);
        }

        //最后刷新es
        couponQueryService.refreshCacheByBatchNo(syncDto.getBatchNo(), "");
    }

    public static <T> T formatMsg(MessageExt message, Class<T> clazz) {
        if (message == null) {
            throw new ApiException("MQ消息为空");
        }
        try {
            return JSON.parseObject(new String(message.getBody(), StandardCharsets.UTF_8), clazz);
        } catch (Exception ex) {
            throw new ApiException("MQ消息格式化错误");
        }
    }
}
