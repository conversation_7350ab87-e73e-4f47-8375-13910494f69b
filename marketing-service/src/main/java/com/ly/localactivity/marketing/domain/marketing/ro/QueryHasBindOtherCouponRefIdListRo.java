package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * QueryHasBindOtherCouponRefIdListRo
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
@Data
@ApiModel("查询已绑定其他优惠券的refId列表请求参数")
public class QueryHasBindOtherCouponRefIdListRo implements Serializable {
    private static final long serialVersionUID = -1169803307745271203L;

    @ApiModelProperty("红包id")
    private Long couponBatchId;

    @ApiModelProperty("refId列表")
    private List<String> refIdList;
}