package com.ly.localactivity.marketing.application.model.ro;


import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 运营推广计划查询参数
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@Data
@EqualsAndHashCode(callSuper = false, of = {"cacheVersion", "positionCodes", "cityId", "areaId", "areaType", "platform", "queryParams", "queryParamStr"})
public class PromotionPlanApiRo extends BaseSignRequest {

    public String cacheVersion = "";
    /**
     * 运营点位code
     */
    @NotNull(message = "运营点位code不能为空")
    @ApiModelProperty(value = "运营点位code，一次最多10个", required = true)
    private List<String> positionCodes;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @NotBlank(message = "平台不能为空")
    @ApiModelProperty(value = "平台code，433-app-ios,434-app-android,852-微信小程序", required = true)
    private String platform;

    /**
     * 页面参数
     */
    @ApiModelProperty(value = "页面url参数，与queryParamStr二选一，优先使用queryParams")
    private Map<String, String> queryParams;

    @ApiModelProperty(value = "页面url参数")
    private String queryParamStr;

    @ApiModelProperty(value = "memberId")
    private Long memberId;
    @ApiModelProperty(value = "deviceId，app必传")
    private String deviceId;
    @ApiModelProperty(value = "openId，微信平台必传")
    private String openId;
    @ApiModelProperty(value = "unionId，微信平台必传")
    private String unionId;
    /**
     * 区域ID
     */
    @ApiModelProperty(value = "区域ID")
    private Long areaId;
    /**
     * 区域类型（行政级别） 0：州 1：国 2：省 3：市 4：县 5：镇
     */
    @ApiModelProperty(value = "区域类型（行政级别） 0：州 1：国 2：省 3：市 4：县 5：镇")
    private Integer areaType;
}
