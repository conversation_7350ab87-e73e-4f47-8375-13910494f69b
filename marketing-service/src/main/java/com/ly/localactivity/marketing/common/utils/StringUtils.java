package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import org.apache.commons.text.similarity.CosineSimilarity;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * StringUtils
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
public class StringUtils extends StrUtil {

    /**
     * 计算两文本相似度，使用余弦相似度算法
     *
     * @param text1 文本1
     * @param text2 文本2
     * @return double
     */
    public static double getSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0;
        }
        Map<CharSequence, Integer> map1 = Arrays.stream(text1.trim().split(""))
                .collect(Collectors.toMap(c -> c, c -> 1, Integer::sum));
        Map<CharSequence, Integer> map2 = Arrays.stream(text2.trim().split(""))
                .collect(Collectors.toMap(c -> c, c -> 1, Integer::sum));
        CosineSimilarity cosineSimilarity = new CosineSimilarity();

        return cosineSimilarity.cosineSimilarity(map1, map2);
    }

    /**
     * 获取url参数
     *
     * @param url url
     * @return {@link Map}<{@link String},{@link String}>
     */
    public static Map<String, String> getUrlParams(String url) {
        String[] params = URLUtil.decode(url).split("&");
        Map<String, String> mapStr = new ConcurrentHashMap<>();
        Arrays.stream(params).parallel().forEach(s -> {
            String[] split = s.split("=");
            if (split.length > 1) {
                mapStr.put(split[0], split[1]);
            } else if (split.length == 1) {
                mapStr.put(split[0], "");
            };

        });
        return mapStr;
    }
}
