package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 商品限制类型，1-全部(不限)，2-指定出发地，3-指定二级品类，3-指定商品
 * @date 2024-04-22
 */
@AllArgsConstructor
@Getter
public enum CouponRuleLimitTypeEnum {
    /**
     * 1-全部(不限)
     */
    ALL(1, "全部(不限)"),
    /**
     * 2-指定出发地
     */
    DEPARTURE(2, "出发地"),
    /**
     * 3-指定二级品类
     */
    CATEGORY(3, "二级品类"),
    /**
     * 4-指定商品
     */
    PRODUCT(4, "指定商品")

    ;
    private final Integer code;
    private final String desc;

}
