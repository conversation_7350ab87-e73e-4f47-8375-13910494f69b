package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContentLinkParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentLinkParamDto;

import java.util.List;

/**
 * <p>
 * 运营推广链接参数 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
public interface IOperationPromotionPlanContentLinkParamService extends IService<OperationPromotionPlanContentLinkParam> {
    /**
     * 保存或更新批处理
     * 保存或更新
     *
     * @param modifier      修饰符
     * @param linkParamDtos 链接参数dtos
     * @param planId        计划id
     */
    void saveOrUpdateBatch(List<OperationPromotionPlanContentLinkParamDto> linkParamDtos, Long planId, String modifier);
}
