package com.ly.localactivity.marketing.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TemplateUtils
 * <p>
 * Created by cdl3112 on 2021-02-02.
 */
public class TemplateUtils {

    private static final Pattern REPLACE_PATTERN = Pattern.compile("(\\{([a-zA-Z\u4E00-\u9FA5]+)\\})");
    private static final String PLAN_LETTER_REGEX = "<if type=\"{0}\".+\\/if>";

    /**
     * 占位符替换
     * demo:{name}你好
     *
     * @param template   内容
     * @param parameters 参数
     * @return result
     */
    public static String getContent(String template, Map<String, String> parameters) {
        return generateContent(template, parameters, false);
    }




    public static String getStrictContent(String template, Map<String, String> parameters) {
        return generateContent(template, parameters, true);
    }

    private static String generateContent(String template, Map<String, String> parameters, boolean isStrict) {
        if(StringUtils.isBlank(template)){
            return template;
        }
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        Matcher m = REPLACE_PATTERN.matcher(template);
        StringBuffer stringBuffer = new StringBuffer();
        while (m.find()) {
            String key = m.group(2);
            String value = null;
            if (parameters.containsKey(key)) {
                value = parameters.get(key);

                if(isStrict){
                    value = (value == null) ? "" : value;
                    m.appendReplacement(stringBuffer, value);
                }
            }
            if(!isStrict){
                value = (value == null) ? "" : value;
                m.appendReplacement(stringBuffer, value);
            }
        }
        m.appendTail(stringBuffer);
        return stringBuffer.toString();
    }

    public static void main(String[] args){
        Map<String, String> map = new HashMap<>();
        map.put("任务名称", "面访");
        map.put("abc", "123");
//        map.put("测试", "哈哈哈");
        String text = TemplateUtils.getContent("{任务名称}tttt{abc}ttttttt{测试}11111", map);
        System.out.println(text);
    }
}
