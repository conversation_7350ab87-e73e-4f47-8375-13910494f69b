package com.ly.localactivity.marketing.service.marketing.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchStockLimit;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchStockLimitMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchStockLimitService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 红包库存限制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Service
public class CouponBatchStockLimitServiceImpl extends ServiceImpl<CouponBatchStockLimitMapper, CouponBatchStockLimit> implements ICouponBatchStockLimitService {

}
