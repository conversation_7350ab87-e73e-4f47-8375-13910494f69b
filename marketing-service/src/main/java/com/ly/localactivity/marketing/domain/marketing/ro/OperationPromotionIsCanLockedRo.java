package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.common.validation.annotation.FutureOrPresentDay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * OperationPromotionIsCanLockedRo
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@ApiModel(value = "OperationPromotionIsCanLockedRo", description = "推广计划是否可以锁定请求参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationPromotionIsCanLockedRo{
    @ApiModelProperty(value = "推广计划id")
    private Long planId;
    @ApiModelProperty(value = "平台", required = true)
    @NotNull(message = "平台不能为空")
    @NotBlank(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "运营点位 ID", required = true)
    @NotNull(message = "运营点位 ID不能为空")
    private Long pointId;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    /**
     * 顺位
     */
    @ApiModelProperty(value = "顺位", required = true)
    @NotNull(message = "顺位不能为空")
    private Integer indexValue;


}
