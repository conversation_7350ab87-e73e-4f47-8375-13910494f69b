package com.ly.localactivity.marketing.external.daytrip.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * SaleProductDto
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DayTripProductDto {
    @ApiModelProperty(value = "最低价格")
    private BigDecimal minPrice;
    @ApiModelProperty(value = "产品code")
    private String code;
    @ApiModelProperty(value = "产品标题")
    private String title;
    @ApiModelProperty(value = "产品副标题")
    private String subTitle;
    /**
     * 点评得分
     */
    @ApiModelProperty(value = "点评得分")
    private Double score;
    /**
     * 头图
     */
    @ApiModelProperty(value = "头图")
    private String image;

    private List<String> poiIds;
    private List<String> labels;
    private List<String> themes;
}
