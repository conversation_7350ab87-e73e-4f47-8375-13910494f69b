package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * RedisSetRo
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
@Data
public class RedisSetRo {
    @NotBlank(message = "key不能为空")
    @ApiModelProperty("key")
    private String key;
    @NotBlank(message = "data不能为空")
    @ApiModelProperty("数据")
    private String data;
    @ApiModelProperty("过期时间，单位秒")
    private Integer expireTime;
}
