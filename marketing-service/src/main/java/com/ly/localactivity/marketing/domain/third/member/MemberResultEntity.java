package com.ly.localactivity.marketing.domain.third.member;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemberResultEntity<T> {

    /**
     * 0 错误码 ，0代表成功
     */
    private String code;
    private String msg;
    private T data;

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
