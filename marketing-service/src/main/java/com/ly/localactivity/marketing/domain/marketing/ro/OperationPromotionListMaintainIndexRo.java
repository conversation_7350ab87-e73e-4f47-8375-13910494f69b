package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPromotionListMaintainIndexRo
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@Data
@ApiModel(value = "OperationPromotionListMaintainIndexRo", description = "获取已经维护的顺位请求参数")
public class OperationPromotionListMaintainIndexRo {
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "运营点位 ID", required = true)
    @NotNull(message = "运营点位 ID不能为空")
    private Long pointId;
    @ApiModelProperty(value = "推广方式", required = true)
    @NotNull(message = "推广方式不能为空")
    private Integer promotionType;
    @ApiModelProperty(value = "推广地区ids", required = true)
    @NotEmpty(message = "推广地区ids不能为空")
    private List<Long> regionIds;
}
