package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.ro.BatchPushCouponRo;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 红包批次明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchDetailMapper extends MyBaseMapper<CouponBatchDetail> {


    /**
     * 查询可用红包批次
     *
     * @param platform        平台
     * @param dateNow         日期
     * @param cityIds         出发城市id
     * @param secondCategorys 二级品类
     * @param resourceIds     资源id
     * @return {@link List}<{@link String}>
     */
    List<String> listAvailable(@Param("platform") String platform
            , @Param("dateNow") LocalDateTime dateNow
            , @Param("cityIds") List<String> cityIds
            , @Param("secondCategorys") List<String> secondCategorys
            , @Param("resourceIds") List<String> resourceIds);

    /**
     * 查询可用红包批次
     *
     * @param platform        平台
     * @param dateNow         日期
     * @param cityIds         出发城市id
     * @param secondCategorys 二级品类
     * @param resourceIds     资源id
     * @param supplierIds     供应商id
     * @param positionCode    点位code
     *
     * @return {@link List}<{@link String}>
     */
    List<String> listAllAvailable(@Param("platform") String platform
            , @Param("dateNow") LocalDateTime dateNow
            , @Param("cityIds") List<String> cityIds
            , @Param("secondCategorys") List<String> secondCategorys
            , @Param("resourceIds") List<String> resourceIds
            , @Param("supplierIds") List<Long> supplierIds
            , @Param("positionCode") String positionCode);

    /**
     * 查询需要刷新的红包批次明细
     *
     * @param request 请求
     * @return {@link List}<{@link CouponBatchDetail}>
     */
    List<CouponBatchDetail> selectNeedRefreshCouponBatchDetailList(BatchPushCouponRo request);
}
