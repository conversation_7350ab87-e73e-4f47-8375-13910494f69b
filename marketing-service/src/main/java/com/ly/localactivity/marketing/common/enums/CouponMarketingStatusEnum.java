package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponMarketingStatusEnum {

    INVALID(1, "未生效"),

    ACTIVE(2, "活动中"),

    NOT_START(3, "未开始"),

    EXPIRED(4, "已过期"),

    DE_LISTED(5, "已下架"),

    ;
    private final Integer code;
    private final String desc;


    public static CouponMarketingStatusEnum getByCode(Integer code){
        if (code == null){
            return null;
        }
        for (CouponMarketingStatusEnum value : CouponMarketingStatusEnum.values()) {
            if (Objects.equals(value.getCode(), code)){
                return value;
            }
        }
        return null;
    }
}
