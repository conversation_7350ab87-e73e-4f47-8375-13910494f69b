package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OperationPositionOutputTypeEnum
 *
 * <AUTHOR>
 * @date 2024/2/1
 */
@Getter
@AllArgsConstructor
public enum OperationPositionOutputTypeEnum {
    /**
     * 1- 幻灯片
     */
     SLIDE(1, "幻灯片"),
    /**
     * 2-单图
     */
    SINGLE_IMAGE(2, "单图"),
    /**
     * 3-ICON
     */
    ICON(3, "ICON"),
    /**
     * 4-ICON-关键词
     */
    ICON_AND_TITLE(4, "ICON+标题"),
    /**
     * 5关键词
     */
    KEYWORD(5, "关键词"),
    /**
     * 6-红包容器
     */
    RED_PACKET_CONTAINER(6, "红包容器"),

    /**
     * 7-ICON+标题+副标题
     */
    ICON_TITLE_SUBTITLE(7, "ICON+标题+副标题"),

    /**
     * 8-标题+副标题
     */
    TITLE_SUBTITLE(8, "标题+副标题"),
    ;
    private final Integer code;
    private final String name;
    public static String getName(Integer code) {
        for (OperationPositionOutputTypeEnum c : OperationPositionOutputTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return null;
    }
}
