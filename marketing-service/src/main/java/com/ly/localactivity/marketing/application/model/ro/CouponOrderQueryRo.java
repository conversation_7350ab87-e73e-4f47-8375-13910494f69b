package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Map;

/**
 * CouponOrderQueryRo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponOrderQueryRo extends BaseCouponRo {
    @NotNull(message = "订单金额不能为空")
    @Min(value = 0, message = "订单金额不能小于0")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "限制条件，resourceIds:资源id,cityIds:出发城市id,secondCategorys:二级品类，多个逗号分隔")
    private CouponLimitRo limitData;

    @ApiModelProperty(value = "优惠券码")
    private String couponCode;
}
