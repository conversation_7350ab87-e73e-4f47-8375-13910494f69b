package com.ly.localactivity.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 红包批次运营渠道配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@ApiModel(value = "CouponBatchOperationChannelDto", description = "红包批次运营渠道配置")
public class CouponBatchOperationChannelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包批次id
     */
    @ApiModelProperty(value = "红包批次id")
    private Long couponBatchId;

    /**
     * refid
     */
    @ApiModelProperty(value = "refid")
    private String refid;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String name;

}
