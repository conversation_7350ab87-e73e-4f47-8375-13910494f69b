package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CouponDetailRo
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponDetailRo extends BaseCouponRo{
    @NotNull(message = "优惠券码不能为空")
    @ApiModelProperty(value = "优惠券列表")
    private List<String> couponCodes;
}
