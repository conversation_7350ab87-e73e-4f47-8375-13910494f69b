package com.ly.localactivity.marketing.domain.marketing.ro;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanUnavailableDateRo
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperationPromotionPlanUnavailableDateRo", description = "推广计划不可用时间段请求参数")
public class OperationPromotionPlanUnavailableDateRo {
    @ApiModelProperty(value = "推广方式", required = true)
    @NotNull(message = "推广方式不能为空")
    private Integer promotionType;
    @ApiModelProperty(value = "推广地区id", required = true)
    @NotEmpty(message = "推广地区id不能为空")
    private List<Long> regionIds;
    @ApiModelProperty(value = "平台", required = true)
    @NotNull(message = "平台不能为空")
    @NotBlank(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "运营点位 ID", required = true)
    @NotNull(message = "运营点位 ID不能为空")
    private Long pointId;
    @ApiModelProperty(value = "顺位", required = true)
    @NotNull(message = "顺位不能为空")
    private Integer indexValue;
    @ApiModelProperty(value = "推广计划id", required = false)
    private Integer planId;
    @ApiModelProperty(value = "现在时间", required = false, hidden = true)
    private LocalDateTime nowTime = LocalDateTime.now();
}
