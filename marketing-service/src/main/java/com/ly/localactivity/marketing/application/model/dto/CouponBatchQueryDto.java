package com.ly.localactivity.marketing.application.model.dto;

import com.ly.localactivity.marketing.application.model.ro.CouponLimitRo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponBatchQueryDto
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CouponBatchQueryDto {

    private String requestId;
    private String platform;
    private List<String> platforms;
    private CouponLimitRo limitData;

    /**
     * 运营点位code
     */
    @ApiModelProperty(value = "运营点位code",hidden = true)
    private String positionCode;

    @ApiModelProperty(value = "refid",hidden = true)
    private String refid;

    private Boolean filterAvaliabled;
    private Boolean filterPosition;

    /**
     * 是否只查询资源id 排除城市、品类过滤
     */
    private Boolean onlyResourceId;
}
