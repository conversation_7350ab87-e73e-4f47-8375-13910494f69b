package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * LinkParamVo
 *
 * <AUTHOR>
 * @date 2024-2-7
 */
@Data
@ApiModel(value = "LinkParamVo", description = "链接参数返回对象")
public class LinkParamVo {
    /**
     * 链接参数 id 主键
     */
    @ApiModelProperty(value = "链接参数 id")
    private Integer id;
    /**
     * 链接参数
     */
    @ApiModelProperty(value = "链接参数Code")
    private String paramCode;
    /**
     * 链接参数值
     */
    @ApiModelProperty(value = "链接参数值")
    private String paramValue;
    /**
     * 链接参数类型
     */
    @ApiModelProperty(value = "参数类型，1-继承点位页面参数，2-固定参数值，3-人工页面配置")
    private Integer valueType;
}
