package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 优惠券列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_batch_list")
public class CouponBatchList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包类型，1-平台红包，2-商家红包
     */
    @TableField("type")
    private Integer type;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 日游产品品类
     */
    @TableField("product_category_id")
    private Integer productCategoryId;

    /**
     * 红包名称
     */
    @TableField("name")
    private String name;

    /**
     * 红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核状态，1-待审核，2-审核通过，3-审核驳回
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    @TableField("audit_comment")
    private String auditComment;

    /**
     * 审核人工号
     */
    @TableField("audit_user_no")
    private String auditUserNo;

    /**
     * 审核人姓名
     */
    @TableField("audit_user")
    private String auditUser;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 供应商id，对应common库的supplier表.supplierId
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 无效/下架原因
     */
    @TableField("invalid_reason")
    private String invalidReason;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人工号
     */
    @TableField("creator_no")
    private String creatorNo;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人工号
     */
    @TableField("modifier_no")
    private String modifierNo;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
