package com.ly.localactivity.marketing.domain.base;

import com.ly.localactivity.marketing.common.enums.OperationLogActionEnum;
import com.ly.localactivity.marketing.common.enums.OperationLogTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OperationLogDto
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationLogDto {
    private OperationLogTypeEnum type;
    private OperationLogActionEnum action;
    private Long relationId;
    private String content;
    private String operatorNo;
    private String operatorName;
}
