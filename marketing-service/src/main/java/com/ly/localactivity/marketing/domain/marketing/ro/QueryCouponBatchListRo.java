package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * QueryCouponBatchListRo
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@Data
@ApiModel(value = "QueryCouponBatchListRo", description = "查询红包列表RO")
public class QueryCouponBatchListRo extends BaseRo {
    private static final long serialVersionUID = -3535217686074303314L;

    @ApiModelProperty(value = "红包类型 1-平台 2-商家")
    @NotNull(message = "红包类型不能为空")
    private Integer type;

    private List<Integer> typeList;

    @ApiModelProperty(value = "产品品类")
    private List<Integer> productCategoryIdList;

    @ApiModelProperty(value = "自定义名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "营销状态")
    private Integer marketingStatus;

    @ApiModelProperty(value = "营销状态集合")
    private List<Integer> marketingStatusList;

    @ApiModelProperty(value = "活动有效期开始时间")
    private LocalDateTime eventValidDateStart;

    @ApiModelProperty(value = "活动有效期结束时间")
    private LocalDateTime eventValidDateEnd;

    @ApiModelProperty(value = "红包类型")
    private List<Integer> couponTypeList;

    @ApiModelProperty(value = "红包批次号")
    private String batchNo;

    @ApiModelProperty(value = "红包批次名称")
    private String batchName;

    @ApiModelProperty(value = "红包内部名称")
    private String batchCname;

    @ApiModelProperty(value = "运营场景")
    private Integer sceneType;

    @ApiModelProperty(value = "站内投放点位code")
    private String operationPositionCode;

    @ApiModelProperty(value = "商家ID/名称")
    private String supplierQuery;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;


    /**
     * 供应商ID 和supplierQuery查询 出来的聚合
     */
    private List<Long> supplierIdList;

    private LocalDateTime currentDate = LocalDateTime.now();

}