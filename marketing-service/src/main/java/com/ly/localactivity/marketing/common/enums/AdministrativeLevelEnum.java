package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 行政级别
 * @date 2024-03-06
 */
@AllArgsConstructor
@Getter
public enum AdministrativeLevelEnum {
    /**
     * 1-国家
     */
    COUNTRY(1, "国家"),
    /**
     * 2-省
     */
    PROVINCE(2, "省"),
    /**
     * 3-市
     */
    CITY(3, "市"),
    /**
     * 4-区(县)
     */
    DISTRICT(4, "区(县)"),
    ;
    private final Integer code;
    private final String name;

}
