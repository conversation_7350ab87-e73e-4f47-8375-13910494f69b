package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.common.validation.group.PlatformValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @description CouponAuditStatusChangeRo
 * @date 2024-04-23
 */
@ApiModel("优惠券审核状态变更")
@Data
public class ChangeCouponAuditStatusRo {
    @ApiModelProperty("batch_list#id")
    @Range(min = 1, message = "红包id必须大于0", groups = {BusinessValidation.class, PlatformValidation.class})
    private Long couponBatchId;
    @ApiModelProperty("优惠券审核状态")
    @Range(min = 1, max = 2, message = "权限不足", groups = {BusinessValidation.class})
    @Range(min = 1, max = 5, message = "错误的审核状态", groups = {PlatformValidation.class})
    private Integer auditStatus;
    @ApiModelProperty("审核意见")
    @Null(message = "审核意见不可填写", groups = {BusinessValidation.class})
    private String auditComment;
}
