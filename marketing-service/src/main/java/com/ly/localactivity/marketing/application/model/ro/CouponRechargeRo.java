package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * CouponRechargeRo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRechargeRo extends BaseCouponRo {
    @ApiModelProperty(value = "红包批次号")
    @NotNull(message = "红包批次号不能为空")
    @Size(min = 1, message = "红包批次号不能为空")
    private List<String> batchNos;
    @ApiModelProperty(value = "交易号，不传则自动生成")
    private String tradeNo;
    @ApiModelProperty(value = "是否异步")
    private Boolean async;
}
