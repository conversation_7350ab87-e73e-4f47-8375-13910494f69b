package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;

/**
 * ImageVo
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@ApiModel("图片对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(of = "imageUrl")
public class ImageVo {
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 图片名称
     */
    private String imageName;
    /**
     * 图片上传信息
     */
    private String imageInfo;
}
