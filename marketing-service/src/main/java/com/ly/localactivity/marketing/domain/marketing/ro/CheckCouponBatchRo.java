package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * CheckCouponBatchRo
 *
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@ApiModel(value = "CheckCouponBatchRo", description = "红包批次校验DTO")
public class CheckCouponBatchRo implements Serializable {

    @ApiModelProperty("批次号列表")
    @NotNull(message = "批次号列表不能为空")
    @Size(min = 1, message = "批次号列表不能为空")
    private List<String> batchNoList;

    private Long couponBatchId;
}