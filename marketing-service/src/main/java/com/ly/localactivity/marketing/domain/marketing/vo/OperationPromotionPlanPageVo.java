package com.ly.localactivity.marketing.domain.marketing.vo;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * OperationPromotionPlanPageVo
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@Data
public class OperationPromotionPlanPageVo extends OperationPromotionPlan {
    /**
     * 关键词
     */
    private String keyword;
    private String subTitle;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 推广链接
     */
    private String linkUrl;
    /**
     * 地区ID（省、地级市）
     */
    private List<Long> regionIds;
    /**
     * 地区（省、地级市 OR 华东、华南...）
     */
    private List<String> regionNames;

    /**
     * 占位有效期（天）
     */
    private Integer placeExpireDays;
    /**
     * 有效期（天）
     */
    private Integer expireDays;
    /**
     * 推广方式
     */
    private String promoteTypeName;
    /**
     * 推广原因
     */
    private String reasonTypeName;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 状态
     */
    private Integer status;
    private Integer operationStatus;
    /**
     * 状态
     */
    private String operationStatusDesc;
    /**
     * 展示方式
     */
    private String defaultShowTypeName;
    /**
     * 有效期开始
     */
    private String beginDate;

    /**
     * 有效期结束
     */
    private String endDate;
}
