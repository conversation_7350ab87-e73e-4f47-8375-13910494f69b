package com.ly.localactivity.marketing.application.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * CouponQueryResponse
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponQueryResponse {
    @ApiModelProperty(value = "用户已领取红包列表")
    public List<CouponVo> couponList = new ArrayList<>();
    @ApiModelProperty(value = "所有可用的红包列表")
    public List<CouponBatchVo> couponBatchList = new ArrayList<>();
}
