package com.ly.localactivity.marketing.external.coupon.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * CouponInfoDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
@ApiModel(value = "CouponInfoDto", description = "发券返回结果")
public class CouponInfoExtDto {
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    /**
     * 信息
     */
    @ApiModelProperty(value = "失败信息")
    private String message;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    /**
     * 批次名称
     */
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    /**
     * 券码
     */
    @ApiModelProperty(value = "券码")
    private String couponCode;
    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private List<ProjectDto> projectId;
    /**
     * 优惠券类型
     */
    @ApiModelProperty(value = "优惠券类型")
    private Integer couponType;
    /**
     * 券名称
     */
    @ApiModelProperty(value = "券名称")
    private String couponName;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 门槛
     */
    @ApiModelProperty(value = "门槛")
    private BigDecimal sill;
    /**
     * 券简述
     */
    @ApiModelProperty(value = "券简述")
    private String brief;
    /**
     * 券详情
     */
    @ApiModelProperty(value = "券详情")
    private String detail;

    /**
     * 券开始时间
     */
    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginDate;
    /**
     * 券结束时间
     */
    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;
    /**
     * 券领取时间
     */
    @ApiModelProperty(value = "券领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * app使用地址
     */
    @ApiModelProperty(value = "app使用地址")
    private String appHref;
    /**
     * h5使用地址
     */
    @ApiModelProperty(value = "h5使用地址")
    private String h5Href;
    /**
     * 小程序使用地址
     */
    @ApiModelProperty(value = "小程序使用地址")
    private String mappHref;
    /**
     * 折扣值70代表7折
     */
    @ApiModelProperty(value = "折扣值70代表7折")
    private Integer discount;
    /**
     * 用户一共可以领几次券,不是当前还可以领几次
     */
    @ApiModelProperty(value = "用户一共可以领几次券,不是当前还可以领几次")
    private Integer times;
    /**
     * 商品id(里程商城专用)
     */
    @ApiModelProperty(value = "商品id(里程商城专用)")
    private String productId;

    /**
     * 商品券信息(里程商城专用)
     * 0-普通券;1-单品券;2-跨品券
     */
    @ApiModelProperty(value = "商品券信息(里程商城专用) 0-普通券;1-单品券;2-跨品券")
    private Integer productType;

    /**
     * 扩展属性列表
     */
    @ApiModelProperty(value = "扩展属性列表")
    private List<ExtendInfoDto> extendList;

}
