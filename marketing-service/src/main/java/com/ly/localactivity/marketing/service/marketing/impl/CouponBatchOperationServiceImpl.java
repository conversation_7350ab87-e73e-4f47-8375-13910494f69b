package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.common.enums.CouponSceneTypeEnum;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperation;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchOperationMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchOperationService;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包批次运营配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class CouponBatchOperationServiceImpl extends ServiceImpl<CouponBatchOperationMapper, CouponBatchOperation> implements ICouponBatchOperationService {
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private CouponBatchOperationMapper couponBatchOperationMapper;
    @Resource
    private ICouponBatchOperationService couponBatchOperationService;
    @Resource
    private IOperationPositionService operationPositionService;


    /**
     * 保存或更新
     * @param couponBatchList
     * @param couponBatchOperationDtoList
     * @return
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchOperationDto> couponBatchOperationDtoList) {
        if (CollectionUtil.isEmpty(couponBatchOperationDtoList)) {
            return true;
        }
        for (CouponBatchOperationDto couponBatchOperationDto : couponBatchOperationDtoList) {
            // 数据控制 防止脏数据
            if (CouponSceneTypeEnum.CROSS_CONNECTION.getCode().equals(couponBatchOperationDto.getSceneType()) || CouponSceneTypeEnum.ACTIVITY_THEME.getCode().equals(couponBatchOperationDto.getSceneType())) {
                couponBatchOperationDto.setOperationPositionCode(StringUtils.EMPTY);
            }
        }

        LambdaQueryWrapper<CouponBatchOperation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchOperation::getCouponBatchId, couponBatchList.getId());
        List<CouponBatchOperation> oldCouponBatchOperationList = couponBatchOperationMapper.selectList(lambdaQueryWrapper);

        Map<String, CouponBatchOperation> positionCodeMap = oldCouponBatchOperationList.stream().collect(Collectors.toMap(CouponBatchOperation::getOperationPositionCode, a -> a, (a, b) -> a));

        List<Long> validIdList = new ArrayList<>();
        for (CouponBatchOperationDto couponBatchOperationDto : couponBatchOperationDtoList) {
            // 场景类型非必填
            if (couponBatchOperationDto.getSceneType() == null) {
                continue;
            }
            CouponBatchOperation couponBatchOperation = BeanUtil.copyProperties(couponBatchOperationDto, CouponBatchOperation.class);
            couponBatchOperation.setModifiedTime(LocalDateTime.now());
            couponBatchOperation.setModifier(adminTokenUtils.getModifier());
            couponBatchOperation.setCouponBatchId(couponBatchList.getId());
            if (couponBatchOperation.getOperationPositionCode() == null){
                couponBatchOperation.setOperationPositionCode("");
            }

            CouponBatchOperation oldCouponBatchOperation = positionCodeMap.get(couponBatchOperation.getOperationPositionCode());
            if (oldCouponBatchOperation == null) {
                couponBatchOperation.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                couponBatchOperation.setCreator(adminTokenUtils.getModifier());
                couponBatchOperation.setCreateTime(LocalDateTime.now());
                couponBatchOperationMapper.insert(couponBatchOperation);
            } else {
                validIdList.add(oldCouponBatchOperation.getId());
                couponBatchOperation.setId(oldCouponBatchOperation.getId());
                couponBatchOperationMapper.updateById(couponBatchOperation);
            }
        }

        // 无效数据
        List<Long> invalidIdList = oldCouponBatchOperationList.stream().map(CouponBatchOperation::getId).filter(id -> !validIdList.contains(id)).collect(Collectors.toList());
        couponBatchOperationService.deleteByIds(invalidIdList);

        return true;
    }

    /**
     * 根据红包id查询
     * @param couponBatchIdList
     * @return
     */
    @Override
    public Map<Long, List<CouponBatchOperationDto>> selectByCouponBatchId(List<Long> couponBatchIdList) {
        if (CollectionUtil.isEmpty(couponBatchIdList)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<CouponBatchOperation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchOperation::getCouponBatchId, couponBatchIdList);
        List<CouponBatchOperation> couponBatchOperationList = couponBatchOperationMapper.selectList(lambdaQueryWrapper);
        List<CouponBatchOperationDto> couponBatchOperationDtoList = BeanUtil.copyToList(couponBatchOperationList, CouponBatchOperationDto.class);

        // 点位别名
        List<String> operationPositionCodeList = couponBatchOperationDtoList.stream().map(CouponBatchOperationDto::getOperationPositionCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(operationPositionCodeList)) {
            return couponBatchOperationDtoList.stream().collect(Collectors.groupingBy(CouponBatchOperationDto::getCouponBatchId));
        }
        LambdaQueryWrapper<OperationPosition> operationPositionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        operationPositionLambdaQueryWrapper.in(OperationPosition::getCode, operationPositionCodeList);
        List<OperationPosition> operationPositionList = operationPositionService.list(operationPositionLambdaQueryWrapper);
        Map<String, OperationPosition> operationPositionCodeMap = operationPositionList.stream().collect(Collectors.toMap(OperationPosition::getCode, Function.identity(), (a, b) -> a));

        for (CouponBatchOperationDto couponBatchOperationDto : couponBatchOperationDtoList) {
            OperationPosition operationPosition = operationPositionCodeMap.get(couponBatchOperationDto.getOperationPositionCode());
            if (operationPosition != null) {
                couponBatchOperationDto.setOperationPositionName(operationPosition.getName());
                couponBatchOperationDto.setOperationPositionAlias(operationPosition.getAlias());
            }
        }

        return couponBatchOperationDtoList.stream().collect(Collectors.groupingBy(CouponBatchOperationDto::getCouponBatchId));
    }

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    @Override
    public Boolean deleteByIds(List<Long> invalidIdList) {
        if (CollectionUtil.isEmpty(invalidIdList)){
            return true;
        }
        return couponBatchOperationMapper.deleteByIds(invalidIdList, adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo());
    }

    /**
     * 检查点位code是否可用
     * @param couponBatchId
     * @param validPositionCodeList
     * @return
     */
    @Override
    public Boolean checkPositionCode(Long couponBatchId, List<String> validPositionCodeList) {
        if (CollectionUtil.isEmpty(validPositionCodeList)){
            return true;
        }
        LambdaQueryWrapper<CouponBatchOperation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchOperation::getCouponBatchId, couponBatchId);
        List<CouponBatchOperation> oldCouponBatchOperationList = couponBatchOperationMapper.selectList(lambdaQueryWrapper);

        for (CouponBatchOperation couponBatchOperation : oldCouponBatchOperationList) {
            if (!validPositionCodeList.contains(couponBatchOperation.getOperationPositionCode())){
                couponBatchOperation.setOperationPositionCode(StringUtils.EMPTY);
                couponBatchOperation.setModifier(adminTokenUtils.getModifier());
                couponBatchOperation.setModifiedTime(LocalDateTime.now());
                couponBatchOperationMapper.updateById(couponBatchOperation);
            }
        }
        return true;
    }
}
