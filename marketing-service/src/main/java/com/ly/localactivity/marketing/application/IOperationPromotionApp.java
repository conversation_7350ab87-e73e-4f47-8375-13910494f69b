package com.ly.localactivity.marketing.application;

import com.ly.localactivity.marketing.application.model.ro.PromotionPlanApiRo;
import com.ly.localactivity.marketing.application.model.vo.OperationPositionApiVo;
import com.ly.localactivity.marketing.domain.common.WCCountryArea;

import java.util.List;
import java.util.Map;

/**
 * 运营点位、推广位业务
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
public interface IOperationPromotionApp {

    /**
     * 获取运营点位api vo列表
     *
     * @param ro ro
     * @return {@link List }<{@link OperationPositionApiVo }>
     */
    List<OperationPositionApiVo> getOperationPositionApiVoList(PromotionPlanApiRo ro);

}
