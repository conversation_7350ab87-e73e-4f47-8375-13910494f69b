package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PlatformVo
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PlatformVo", description = "所属平台信息")
@Builder
public class PlatformVo {

    private Long couponBatchDetailId;
    /**
     * 平台编码
     */
    @ApiModelProperty(value = "平台编码")
    private Integer code;
    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称")
    private String name;
}
