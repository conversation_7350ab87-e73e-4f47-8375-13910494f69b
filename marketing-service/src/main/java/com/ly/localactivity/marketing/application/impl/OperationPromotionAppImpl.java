package com.ly.localactivity.marketing.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.framework.utils.ListSortUtils;
import com.ly.localactivity.framework.utils.LocalDateTimeUtils;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.IOperationPromotionApp;
import com.ly.localactivity.marketing.application.model.dto.PromotionPlanApiCacheDto;
import com.ly.localactivity.marketing.application.model.ro.PromotionPlanApiRo;
import com.ly.localactivity.marketing.application.model.vo.OperationPositionApiVo;
import com.ly.localactivity.marketing.application.model.vo.PromotionPlanApiVo;
import com.ly.localactivity.marketing.common.constants.RedisConstants;
import com.ly.localactivity.marketing.common.enums.*;
import com.ly.localactivity.marketing.common.enums.common.MarketAreaType;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.common.utils.TemplateUtils;
import com.ly.localactivity.marketing.common.utils.UrlUtils;
import com.ly.localactivity.marketing.domain.common.WCCountryArea;
import com.ly.localactivity.marketing.domain.marketing.*;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanListForApiDto;
import com.ly.localactivity.marketing.mapper.marketing.*;
import com.ly.localactivity.marketing.service.common.IWCCountryAreaService;
import com.ly.localactivity.marketing.service.common.IWCMarketAreaService;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运营点位、推广位业务实现
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@Slf4j
@Service(value = "operationPromotionApp")
public class OperationPromotionAppImpl implements IOperationPromotionApp {

    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private IWCCountryAreaService countryAreaService;

    @Resource
    private OperationPromotionPlanMapper operationPromotionPlanMapper;
    @Resource
    private OperationPromotionPlanContentMapper operationPromotionPlanContentMapper;
    @Resource
    private OperationPromotionPlanContentLinkParamMapper operationPromotionPlanContentLinkParamMapper;
    @Resource
    private OperationPromotionLinkTemplateMapper operationPromotionLinkTemplateMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OperationPromotionPlanChannelMapper operationPromotionPlanChannelMapper;
    @Resource
    private IWCMarketAreaService marketAreaService;


    private static final String REFID = "refId";


    @Override
    @LACacheable(key = "'getOperationPositionApiVoList' + #ro.hashCode()", expire = 60 * 5)
    public List<OperationPositionApiVo> getOperationPositionApiVoList(PromotionPlanApiRo ro) {
        List<OperationPositionApiVo> positionVos = new CopyOnWriteArrayList<>();
        PlatformEnum platformEnum = EnumUtil.getBy(PlatformEnum::getCode, ro.getPlatform());
        if (ObjectUtil.isNull(platformEnum)) {
            SkyLogUtils.warnByMethod(log, "未查询到平台信息", "", "");
            return positionVos;
        }
        if (CollectionUtil.isEmpty(ro.getPositionCodes())) {
            SkyLogUtils.warnByMethod(log, "运营点位code为空", "", "");
            return positionVos;
        }
        WCCountryArea countryArea = Optional.ofNullable(ro.getCityId()).map(countryAreaService::getAreaById)
                //如果没有传入区域id，就根据城市id获取
                .orElseGet(() -> Optional.ofNullable(ro.getAreaId()).map(countryAreaService::getAreaById).orElse(null));
        if (countryArea == null) {
            SkyLogUtils.warnByMethod(log, "未查询到城市信息", "", "");
            return positionVos;
        }
        //转换平台id，特殊处理逻辑
        String platform = convertPlatform(ro.getPlatform());
        //1.----------------获取所有运营点-------------------
        List<OperationPosition> operationPositionList = operationPositionService.list(new LambdaQueryWrapper<OperationPosition>().eq(OperationPosition::getStatus, StatusFlagEnum.NORMAL.getCode()).in(OperationPosition::getCode, ro.getPositionCodes()));
        if (CollectionUtil.isEmpty(operationPositionList)) {
            SkyLogUtils.warnByMethod(log, "未查询到运营点位信息", "", "");
            return positionVos;
        }

        //判断传入城市区域类型
        AreaCoverageEnum areaCoverageEnum = countryAreaService.getAreaCoverageEnum(countryArea);
        //过滤掉覆盖区域不满足的
        operationPositionList = operationPositionList.stream().filter(operationPosition -> {
            String coverRegionType = operationPosition.getCoverRegionType();
            if (StringUtils.isNotBlank(coverRegionType)) {
                return areaCoverageEnum != null && coverRegionType.contains(areaCoverageEnum.getCode());
            }
            return false;
        }).collect(Collectors.toList());

        //处理参数
        Map<String, String> pageParams = UrlUtils.getUrlMap(ro.getQueryParams(), ro.getQueryParamStr());
        Map<String, String> countryAreaMap = countryAreaService.convertToMap(countryArea);
        //添加区域信息
        pageParams.putAll(countryAreaMap);

        //1.----------------运营点位对应的缓存-------------------
        //需要进一步查询的运营点位
        List<OperationPosition> needQueriedOperationPositionList = operationPositionList.stream()
                //过滤有缓存的
                .filter(position -> {
                    OperationPositionApiVo plansCacheData = getOperationPositionApiVoCacheData(countryArea, platform, position, pageParams);
                    if (ObjectUtil.isNotNull(plansCacheData)) {
                        positionVos.add(plansCacheData);
                        return false;
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
        //2.----------------获取所有运营点位对应的推广计划-------------------
        SkyLogUtils.infoByMethod(log, StrUtil.format("getOperationPositionApiVoList[needQueriedOperationPositionList]:list:{}", JSON.toJSONString(needQueriedOperationPositionList)), "getOperationPositionApiVoList", "");
        List<OperationPositionApiVo> positionApiVoList = getAllOperationPositionApiVoList(needQueriedOperationPositionList, countryArea, platform, pageParams);
        positionVos.addAll(positionApiVoList);
        SkyLogUtils.infoByMethod(log, StrUtil.format("getOperationPositionApiVoList[success],ro:{},result:{}", ro.hashCode(), JSON.toJSONString(positionVos)), "getOperationPositionApiVoList", "");
        return positionVos.stream().filter(positionVo -> ObjectUtil.isNotNull(positionVo) && CollectionUtil.isNotEmpty(positionVo.getPlans())).collect(Collectors.toList());
    }


    /**
     * 获取运营点位缓存数据
     *
     * @param countryArea 国家/地区
     * @param platform    站台
     * @param position    位置
     * @param pageParams  页面参数
     * @return {@link OperationPositionApiVo }
     */
    private OperationPositionApiVo getOperationPositionApiVoCacheData(WCCountryArea countryArea, String platform, OperationPosition position, Map<String, String> pageParams) {
        if (ObjectUtil.isNull(countryArea) || StrUtil.isBlank(platform) || ObjectUtil.isNull(position)) {
            return null;
        }
        String positionCode = position.getCode();
        String redisKey = RedisConstants.GET_OPERATION_POSITION_API_VO_CACHE_DATA_KEY_PREFIX + countryArea.getCACityId() + StrUtil.COLON + platform + StrUtil.COLON + positionCode
                + StrUtil.COLON + (pageParams != null ? SecureUtil.md5(JSON.toJSONString(pageParams)) : StrUtil.EMPTY);
        String cacheBody = redisUtils.get(redisKey);
        if (StringUtils.isNotBlank(cacheBody)) {
            OperationPositionApiVo positionApiVo = JSON.parseObject(cacheBody, OperationPositionApiVo.class);
            if (position.getRandomKey().equals(positionApiVo.getRandomKey())) {
                SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlansCacheData[hit cache]:key:{},result{}", redisKey, JSON.toJSONString(positionApiVo)), "getOperationPositionApiVoList", "getPromotionPlansCacheData");
                return positionApiVo;
            } else {
                SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlansCacheData[cache invalidation]:key:{},oldResult{}", redisKey, JSON.toJSONString(positionApiVo)), "getOperationPositionApiVoList", "getPromotionPlansCacheData");
                return null;
            }
        } else {
            SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlansCacheData[cache miss]:key{}", redisKey), "getOperationPositionApiVoList", "getPromotionPlansCacheData");
            return null;
        }
    }

    /**
     * 获取运营点位Vo列表
     *
     * @param operationPositionList 操作位置列表
     * @param countryArea           国家/地区
     * @param platform              站台
     * @param pageParams            页面参数
     * @return {@link List }<{@link OperationPositionApiVo }>
     */
    List<OperationPositionApiVo> getAllOperationPositionApiVoList(List<OperationPosition> operationPositionList, WCCountryArea countryArea, String platform, Map<String, String> pageParams) {
        if (CollectionUtil.isEmpty(operationPositionList) || ObjectUtil.isNull(countryArea) || StrUtil.isBlank(platform)) {
            return Collections.emptyList();
        }
        Map<String, List<PromotionPlanApiVo>> positionCodePromotionPlanListMap = new HashMap<>(operationPositionList.size());
        String refId;
        if (MapUtil.isNotEmpty(pageParams) && pageParams.containsKey(REFID)) {
            refId = pageParams.get(REFID);
        } else if (MapUtil.isNotEmpty(pageParams) && pageParams.containsKey(REFID.toLowerCase())) {
            refId = pageParams.get(REFID.toLowerCase());
        } else {
            refId = StrUtil.EMPTY;
        }
        //----------------获取运营点位对应的推广计划基础信息的缓存-------------------
        List<OperationPosition> needQueriedPromotionPlanOperationPositionList = operationPositionList.stream()
                //过滤存在缓存的
                .filter(operationPosition -> {
                    //获取运营点位对应的推广计划
                    List<PromotionPlanApiVo> promotionPlanApiVoListCacheData = getPromotionPlanApiVoBaseInfoListCacheData(operationPosition, countryArea, platform, refId);
                    if (ObjectUtil.isNotNull(promotionPlanApiVoListCacheData)) {
                        positionCodePromotionPlanListMap.put(operationPosition.getCode(), promotionPlanApiVoListCacheData);
                        return false;
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
        //----------------获取运营点位对应的推广计划基础信息-------------------
        SkyLogUtils.infoByMethod(log, StrUtil.format("getAllOperationPositionApiVoList[needQueriedPromotionPlanOperationPositionList]:list:{}", JSON.toJSONString(needQueriedPromotionPlanOperationPositionList)), "getOperationPositionApiVoList", "");
        List<PromotionPlanApiVo> operationPositionApiVoBaseInfoList = getAllOperationPositionApiVoBaseInfoList(needQueriedPromotionPlanOperationPositionList, countryArea, platform, refId);

        positionCodePromotionPlanListMap.putAll(operationPositionApiVoBaseInfoList.stream().collect(Collectors.groupingBy(PromotionPlanApiVo::getPositionCode)));
        List<PromotionPlanApiVo> promotionPlanApiVos = positionCodePromotionPlanListMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        //获取并填充内容
        fillPlanContents(promotionPlanApiVos, pageParams, platform);

        List<OperationPositionApiVo> operationPositionApiVos = new CopyOnWriteArrayList<>();
        operationPositionList.forEach(position -> {
            List<PromotionPlanApiVo> planApiVos = positionCodePromotionPlanListMap.getOrDefault(position.getCode(), Collections.emptyList());
            OperationPositionApiVo operationPositionApiVo = BeanUtil.copyProperties(position, OperationPositionApiVo.class);
            //获取模块
            operationPositionApiVo.setModule(getModel(position.getLocation()));
            operationPositionApiVo.setPlans(planApiVos);
            String redisKey = RedisConstants.GET_OPERATION_POSITION_API_VO_CACHE_DATA_KEY_PREFIX + countryArea.getCACityId() + StrUtil.COLON + platform + StrUtil.COLON + position.getCode()
                    + StrUtil.COLON + (pageParams != null ? SecureUtil.md5(JSON.toJSONString(pageParams)) : StrUtil.EMPTY);
            redisUtils.del(redisKey);
            SkyLogUtils.infoByMethod(log, StrUtil.format("getAllOperationPositionApiVoList[cache data]:key:{},positionCode:{},result:{}", redisKey, position.getCode(), JSON.toJSONString(operationPositionApiVo)), "getOperationPositionApiVoList", "cacheData");
            redisUtils.setnx(redisKey, JSON.toJSONString(operationPositionApiVo), 60 * 30);
            operationPositionApiVos.add(operationPositionApiVo);
        });
        return operationPositionApiVos;
    }


    /**
     * 获取所有操作位置api vo基本信息列表
     *
     * @param operationPositionList 操作位置列表
     * @param countryArea           国家/地区
     * @param platform              平台
     * @param refId                 参考编号
     * @return {@link List }<{@link PromotionPlanApiVo }>
     */
    private List<PromotionPlanApiVo> getAllOperationPositionApiVoBaseInfoList(List<OperationPosition> operationPositionList, WCCountryArea countryArea, String platform, String refId) {
        if (CollectionUtil.isEmpty(operationPositionList) || ObjectUtil.isNull(countryArea) || StrUtil.isBlank(platform)) {
            return Collections.emptyList();
        }
        Long provinceId = countryArea.getCAProvinceId();
        Long cityId = countryArea.getCACityId();
        //判断传入城市区域类型
        AreaCoverageEnum areaCoverageEnum = countryAreaService.getAreaCoverageEnum(countryArea);
        //查询city归属区域id
        Long areaId = !AreaCoverageEnum.FOREIGN.equals(areaCoverageEnum) ? marketAreaService.getAreaIdByCityIdAndType(cityId, MarketAreaType.GEOGRAPHICAL_AREA) : null;

        OperationPromotionPlanListForApiDto modelDto = OperationPromotionPlanListForApiDto.builder()
                .platform(platform)
                .positionIds(operationPositionList.stream().map(OperationPosition::getId).collect(Collectors.toList()))
                .cityId(cityId)
                .provinceId(provinceId)
                .areaIds(Collections.singletonList(areaId))
                .date(LocalDateTimeUtils.getString(LocalDateTime.now()))
                .areaCoverage(areaCoverageEnum == null ? StrUtil.EMPTY : areaCoverageEnum.getCode())
                .build();
        List<OperationPromotionPlan> promotionPlans = operationPromotionPlanMapper.listForApi(modelDto);
        if (CollectionUtil.isEmpty(promotionPlans)) {
            SkyLogUtils.warnByMethod(log, StrUtil.format("operationPromotionPlanMapper[listForApi] result is empty,params:{}", JSON.toJSONString(modelDto)), "getOperationPositionApiVoList", "listForApi");
            return Collections.emptyList();
        }

        Map<Long, List<OperationPromotionPlan>> positionIdPromotionPlanMap = promotionPlans.stream().collect(Collectors.groupingBy(OperationPromotionPlan::getPositionId));
        //获取所有的推广计划渠道信息
        List<OperationPromotionPlanChannel> planChannels = operationPromotionPlanChannelMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanChannel>()
                .in(OperationPromotionPlanChannel::getPlanId, promotionPlans.stream().map(OperationPromotionPlan::getId).collect(Collectors.toList()))
                .eq(OperationPromotionPlanChannel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        Map<Long, Map<Long, List<OperationPromotionPlanChannel>>> positionIdChannelMap = planChannels.stream().collect(Collectors.groupingBy(OperationPromotionPlanChannel::getPositionId, Collectors.groupingBy(OperationPromotionPlanChannel::getPlanId)));

        List<PromotionPlanApiVo> promotionPlanApiVos = new ArrayList<>();
        operationPositionList.forEach(operationPosition -> {
            List<PromotionPlanApiVo> promotionPlanApiVoList = convertPromotionPlanWithFilterAndSort(operationPosition, positionIdPromotionPlanMap.getOrDefault(operationPosition.getId(), Collections.emptyList()), positionIdChannelMap.getOrDefault(operationPosition.getId(), Collections.emptyMap()), refId);
            if (CollectionUtil.isNotEmpty(promotionPlanApiVoList)) {
                promotionPlanApiVos.addAll(promotionPlanApiVoList);
            }
            //缓存数据  境外的不区分行政区域
            String redisKey = RedisConstants.GET_PROMOTION_PLAN_API_VO_LIST_CACHE_DATA_KEY_PREFIX + countryArea.getCACityId() + StrUtil.COLON + platform + StrUtil.COLON + operationPosition.getCode() + StrUtil.COLON + refId;
            PromotionPlanApiCacheDto cacheDto = PromotionPlanApiCacheDto.builder()
                    .positionCode(operationPosition.getCode())
                    .randomKey(operationPosition.getRandomKey())
                    .plans(promotionPlanApiVoList)
                    .build();
            redisUtils.del(redisKey);
            SkyLogUtils.infoByMethod(log, StrUtil.format("getAllOperationPositionApiVoBaseInfoList[cache data]:key:{},positionCode:{},result:{}", redisKey, operationPosition.getCode(), JSON.toJSONString(cacheDto)), "getAllOperationPositionApiVoBaseInfoList", "cacheData");
            redisUtils.setnx(redisKey, JSON.toJSONString(cacheDto), 60 * 30);
        });
        return promotionPlanApiVos;
    }


    /**
     * 转换vo，并且排序
     *
     * @param position 位置
     * @param planList 计划列表
     * @return {@link List}<{@link PromotionPlanApiVo}>
     */
    private List<PromotionPlanApiVo> convertPromotionPlanWithFilterAndSort(OperationPosition position, List<OperationPromotionPlan> planList, Map<Long, List<OperationPromotionPlanChannel>> planIdChannelMap, String refId) {
        try {
            if (CollectionUtil.isEmpty(planList) || position == null) {
                return Collections.emptyList();
            }
            List<PromotionPlanApiVo> planVos = BeanUtil.copyToList(planList.stream().filter(s -> s.getPlaceFlag() == 0).collect(Collectors.toList()),
                    PromotionPlanApiVo.class);
            AtomicBoolean exitNotDefaultPlan = new AtomicBoolean(false);
            //过滤不满足refId限制的计划
            SkyLogUtils.infoByMethod(log, StrUtil.format("positionCode:{} ,before filtering by refId:{}", position.getCode(), JSON.toJSONString(planVos)), "getOperationPositionApiVoList", "filterByChannels");
            planVos = planVos.stream().filter(plan -> {
                plan.setPositionName(position.getName());
                plan.setRandomKey(position.getRandomKey());
                if (PlanDefaultFlagEnum.NOT_DEFAULT.getCode().equals(plan.getDefaultFlag())) {
                    exitNotDefaultPlan.set(true);
                }
                List<OperationPromotionPlanChannel> channels = planIdChannelMap.get(plan.getId());
                //如果有渠道信息，但是refId为空，直接返回false
                if (CollectionUtil.isNotEmpty(channels)) {
                    if (StrUtil.isBlank(refId)) {
                        return false;
                    } else {
                        //如果有渠道信息，但是refId不在渠道信息中，返回false
                        if (channels.stream().anyMatch(channel -> refId.equals(channel.getRefid()))) {
                            plan.setLimitChannel(true);
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
                return true;
            }).collect(Collectors.toList());
            SkyLogUtils.infoByMethod(log, StrUtil.format("positionCode:{} ,after filtering by refId:{}", position.getCode(), JSON.toJSONString(planVos)), "getOperationPositionApiVoList", "filterByChannels");

            if (CollectionUtil.isEmpty(planVos)) {
                return Collections.emptyList();
            }
            //如果有内容，去掉兜底内容（展示方式=只做兜底展示）
            if (exitNotDefaultPlan.get()) {
                planVos.removeIf(plan -> PlanDefaultFlagEnum.DEFAULT.getCode().equals(plan.getDefaultFlag()) && plan.getDefaultShowType().equals(PlanDefaultShowTypeEnum.DEFAULT_SHOW.getCode())
                );
            }
            sortVo(position, planVos);
            if (CollectionUtil.isEmpty(planVos)) {
                return Collections.emptyList();
            }
            //去重复，并填充空缺顺位
            planVos = removeRepetition(position, planVos);
            return planVos;
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "vo转换异常", "", "", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取推广计划基本信息列表缓存数据
     *
     * @param operationPosition 操作位置
     * @param countryArea       国家/地区
     * @param platform          站台
     * @param refId             参考编号
     * @return {@link List }<{@link PromotionPlanApiVo }>
     */
    List<PromotionPlanApiVo> getPromotionPlanApiVoBaseInfoListCacheData(OperationPosition operationPosition, WCCountryArea countryArea, String platform, String refId) {
        String positionCode = operationPosition.getCode();
        String redisKey = RedisConstants.GET_PROMOTION_PLAN_API_VO_LIST_CACHE_DATA_KEY_PREFIX + countryArea.getCACityId() + StrUtil.COLON + platform + StrUtil.COLON + positionCode + StrUtil.COLON + refId;
        String cacheBody = redisUtils.get(redisKey);
        if (StringUtils.isNotBlank(cacheBody)) {
            PromotionPlanApiCacheDto cacheDto = JSON.parseObject(cacheBody, PromotionPlanApiCacheDto.class);
            if (ObjectUtil.equal(cacheDto.getPositionCode(), operationPosition.getCode())) {
                if (operationPosition.getRandomKey().equals(cacheDto.getRandomKey())) {
                    SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlanApiVoBaseInfoListCacheData[hit cache]:key:{},result:{}", redisKey, JSON.toJSONString(cacheDto)), "getOperationPositionApiVoList", "getPromotionPlanApiVoBaseInfoListCacheData");
                    return cacheDto.getPlans();
                } else {
                    SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlanApiVoBaseInfoListCacheData[cache invalidation]:key:{},oldResult:{}", redisKey, JSON.toJSONString(cacheDto)), "getOperationPositionApiVoList", "getPromotionPlanApiVoBaseInfoListCacheData");
                    return null;
                }
            } else {
                SkyLogUtils.warnByMethod(log, StrUtil.format("getPromotionPlanApiVoBaseInfoListCacheData[cache error]:key:{},oldResult:{}", redisKey, JSON.toJSONString(cacheDto)), "getOperationPositionApiVoList", "getPromotionPlanApiVoBaseInfoListCacheData");
                return null;
            }

        } else {
            SkyLogUtils.infoByMethod(log, StrUtil.format("getPromotionPlanApiVoBaseInfoListCacheData[cache miss]:key{}", redisKey), "getOperationPositionApiVoList", "getPromotionPlanApiVoBaseInfoListCacheData");
            return null;
        }
    }


    /**
     * 填充推广计划内容
     *
     * @param promotionPlanApiVos 推广计划vos
     * @param pageParams          页面参数
     * @param platform            站台
     */
    private void fillPlanContents(List<PromotionPlanApiVo> promotionPlanApiVos, Map<String, String> pageParams, String platform) {
        if (CollectionUtil.isEmpty(promotionPlanApiVos)) {
            return;
        }
        List<Long> ids = promotionPlanApiVos.stream().map(PromotionPlanApiVo::getId).collect(Collectors.toList());
        //获取推广计划内容
        List<OperationPromotionPlanContent> contents = operationPromotionPlanContentMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanContent>()
                .in(OperationPromotionPlanContent::getPlanId, ids)
                .eq(OperationPromotionPlanContent::getPlatform, platform)
                .eq(OperationPromotionPlanContent::getDeleteFlag, false));
        //获取推广计划内容链接参数
        List<OperationPromotionPlanContentLinkParam> linkParams = operationPromotionPlanContentLinkParamMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanContentLinkParam>()
                .in(OperationPromotionPlanContentLinkParam::getPlanId, ids)
                .eq(OperationPromotionPlanContentLinkParam::getDeleteFlag, false));
        Map<Long, OperationPromotionPlanContent> planIdPlanContentMap = contents.stream().collect(Collectors.toMap(OperationPromotionPlanContent::getPlanId, Function.identity(), (a, b) -> a));
        Map<Long, List<OperationPromotionPlanContentLinkParam>> planIdContentParamsMap = linkParams.stream().collect(Collectors.groupingBy(OperationPromotionPlanContentLinkParam::getPlanId));

        promotionPlanApiVos.forEach(planApiVo -> {
            //填充内容
            doFillPlanContent(planApiVo, pageParams, planIdPlanContentMap.get(planApiVo.getId()), planIdContentParamsMap.getOrDefault(planApiVo.getId(), Collections.emptyList()));
            //过滤前后空格
            if (StringUtils.isNotBlank(planApiVo.getLinkUrl())) {
                planApiVo.setLinkUrl(planApiVo.getLinkUrl().replace(StrUtil.SPACE, StrUtil.EMPTY));
            }
        });
    }

    /**
     * 填充推广计划内容
     *
     * @param planVo      计划vo
     * @param pageParams  page params
     * @param planContent 计划内容
     * @param linkParams  链接params
     */
    private void doFillPlanContent(PromotionPlanApiVo planVo
            , Map<String, String> pageParams
            , OperationPromotionPlanContent planContent
            , List<OperationPromotionPlanContentLinkParam> linkParams) {
        if (ObjectUtil.isNull(planContent) || ObjectUtil.isNull(planVo)) {
            return;
        }

        planVo.setImageUrl(planContent.getImageUrl());
        planVo.setImageName(planContent.getImageName());
        planVo.setImageInfo(planContent.getImageInfo());
        planVo.setKeyword(planContent.getKeyword());
        planVo.setSubTitle(planContent.getSubTitle());

        //链接为空
        if (planContent.getLinkType().equals(PromotionLinkTypeEnum.EMPTY.getCode())) {
            return;
        }

        if (planContent.getLinkType().equals(PromotionLinkTypeEnum.FIXED_LINK.getCode())) {
            //固定链接替换城市id后直接返回链接
            String url = planContent.getLinkUrl();
            if (StringUtils.isNotBlank(url)) {
                url = TemplateUtils.getContent(url, pageParams);
            }
            planVo.setLinkUrl(url);
            return;
        }

        //url前缀
        String linkUrlPrefix = planContent.getLinkUrlPrefix();
        String baseUrl = planContent.getLinkUrl();
        if (StringUtils.isBlank(baseUrl)
                && planContent.getLinkTemplateId() != null
                && planContent.getLinkTemplateId() > 0) {
            //链接为空就去取模板
            OperationPromotionLinkTemplate linkTemplate = operationPromotionLinkTemplateMapper.selectById(planContent.getLinkTemplateId());
            if (linkTemplate != null) {
                baseUrl = linkTemplate.getLinkUrl();
                linkUrlPrefix = linkTemplate.getLinkUrlPrefix();
            }
        }
        //防止前缀为 null
        linkUrlPrefix = StringUtils.isBlank(linkUrlPrefix) ? StringUtils.EMPTY : linkUrlPrefix;

        if (StringUtils.isBlank(baseUrl)) {
            return;
        }

        //获取参数
        List<OperationPromotionPlanContentLinkParam> params = linkParams.stream()
                .filter(s -> s.getPlanContentId().equals(planContent.getId()))
                .collect(Collectors.toList());

        //拼接参数
        Map<String, String> queryParams = new HashMap<>();
        params.forEach(s -> {
            if (s.getValueType().equals(PromotionLinkParamTypeEnum.Extend_Page.getCode())) {
                //继承页面参数
                queryParams.put(s.getParamCode(), pageParams.getOrDefault(s.getParamCode(), StrUtil.EMPTY));
            } else {
                //固定值、人工配置都使用固定值
                queryParams.put(s.getParamCode(), s.getParamValue());
            }
        });
        //拼接链接
        String url = baseUrl.split("\\?")[0];
        String urlQuery = URLUtil.buildQuery(queryParams, StandardCharsets.UTF_8);
        Integer linkUrlType = planContent.getLinkUrlType();
        if (LinkUrlTypeEnum.WEBVIEW.getCode().equals(linkUrlType)
                && StringUtils.isNotBlank(linkUrlPrefix)) {
            //webview链接
            planVo.setLinkUrl(linkUrlPrefix + URLUtil.encodeAll(url + "?" + urlQuery));
        } else {
            planVo.setLinkUrl(url + "?" + URLUtil.encode(urlQuery));
        }
    }


    private Integer getModel(String location) {
        if (StringUtils.isBlank(location)) {
            return null;
        }
        return Integer.valueOf(location.split(StrUtil.UNDERLINE)[0]);
    }


    /**
     * 排序vo
     *
     * @param position 位置
     * @param planVos  计划vos
     */
    private void sortVo(OperationPosition position, List<PromotionPlanApiVo> planVos) {
        if (CollectionUtil.isEmpty(planVos)) {
            return;
        }
        int maxIndex = Stream.of(position.getImageMaxCount(), position.getKeywordMaxCount(), planVos.stream().mapToInt(PromotionPlanApiVo::getIndexValue).max().orElse(NumberUtils.INTEGER_ZERO)).filter(ObjectUtil::isNotNull).max(Integer::compareTo).orElse(NumberUtils.INTEGER_ONE);
        //设置排序值
        //规则：置顶优先，顺位从大到小，兜底最后
        //推广计划顺序：锁定->城市->省份->大区->（境内+境外）-->兜底
        //最后，渠道限制优先
        planVos.forEach(plan -> {
            plan.setOutputType(position.getOutputType());
            if (plan.getTopFlag() != null && plan.getTopFlag() == 1) {
                //置顶优先
                plan.setPromotionTypeSortNo(9999);
            } else {
                if (NumberUtils.INTEGER_ONE.equals(plan.getDefaultFlag())) {
                    plan.setPromotionTypeSortNo(NumberUtils.INTEGER_ZERO);
                    //兜底最后
                    plan.setIndexValue(maxIndex + 1);
                } else {
                    //其它情况按照推广类型排序，推广方式，0-兜底，1-按境内（全国），2-按境外（港澳），3-按大区，4-按省份，4-按城市, 20 - 境外
                    plan.setPromotionTypeSortNo(plan.getPromoteType());
                    if (plan.getPromoteType().equals(PromoteTypeEnum.OVERSEAS.getCode()) || plan.getPromoteType().equals(PromoteTypeEnum.FOREIGN.getCode())) {
                        //因为1-境内，2-是境外，如果是境外的排序值设置成和境内一样
                        plan.setPromotionTypeSortNo(PromoteTypeEnum.DOMESTIC.getCode());
                    }
                    //限制了渠道需要前置
                    plan.setChannelSortNo(plan.getLimitChannel() ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
                }
                //因为是，顺位要按顺序排序，所以取最大值减去顺位值
                plan.setIndexSortNo(maxIndex - plan.getIndexValue());
            }
        });
        //排序，按顺位排序号、推广类型排序号倒序
        ListSortUtils.sort(planVos, false, "indexSortNo", "promotionTypeSortNo", "channelSortNo");
    }

    /**
     * 去重复，并填充空缺顺位
     *
     * @param position 位置
     * @param planVos  计划vos
     */
    private List<PromotionPlanApiVo> removeRepetition(OperationPosition position, List<PromotionPlanApiVo> planVos) {
        //总顺位数
        int maxCount = Stream.of(position.getImageMaxCount(), position.getKeywordMaxCount(), NumberUtils.INTEGER_ONE).filter(ObjectUtil::isNotNull).max(Integer::compareTo).orElse(NumberUtils.INTEGER_ONE);
        //最大顺位值
        int maxIndex = planVos.stream().mapToInt(PromotionPlanApiVo::getIndexValue).max().orElse(0);
        //去重相同顺位值内多个计划
        Map<Integer, List<PromotionPlanApiVo>> map =
                planVos.stream().collect(Collectors.groupingBy(PromotionPlanApiVo::getIndexValue));

        List<PromotionPlanApiVo> newPlanVos = map.values().stream().map(s -> s.get(0)).collect(Collectors.toList());
        newPlanVos.forEach(s -> {
            s.setIndexSortNo(s.getIndexValue());
        });
        //填充空缺顺位
        for (int i = 0; i < maxIndex; i++) {
            if (map.containsKey(i + 1)) {
                continue;
            }
            //把上一顺位多余的数据填充到这个顺位
            PromotionPlanApiVo prevValue = getPrevIndexValue(i
                    , map
                    , newPlanVos.stream().map(PromotionPlanApiVo::getId).collect(Collectors.toList()));
            if (prevValue != null) {
                //顺位过来的，值要改成当前顺位
                prevValue.setIndexSortNo(i + 1);
                //找到上一个顺位的值
                newPlanVos.add(prevValue);
            }
        }
        //按顺位值升序再排序
        ListSortUtils.sort(planVos, true, "indexSortNo");
        return newPlanVos.stream().limit(maxCount).collect(Collectors.toList());
    }

    /**
     * 递归获取上一个顺位的值
     * 没有值或只有一个值的情况下继续往上找，直到index<=0
     *
     * @param index 索引
     * @param map   地图
     * @return {@link PromotionPlanApiVo}
     */
    private PromotionPlanApiVo getPrevIndexValue(int index, Map<Integer, List<PromotionPlanApiVo>> map, List<Long> planIds) {
        if (index <= 0) {
            return null;
        }
        if (!map.containsKey(index)) {
            //没有这个顺位，继续往上找
            return getPrevIndexValue(index - 1, map, planIds);
        }
        if (map.get(index).size() > 1) {
            //找到那个顺位并且有两个值的情况，而且值不在已有的计划里面，返回第一个值
            Optional<PromotionPlanApiVo> first = map.get(index).stream().filter(s -> !planIds.contains(s.getId())).findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        //只有一个值继续再往上找
        return getPrevIndexValue(index - 1, map, planIds);
    }

    /**
     * 转换平台，特殊处理逻辑
     *
     * @param platform 站台
     * @return {@link String}
     */
    private String convertPlatform(String platform) {
        if (StringUtils.isBlank(platform)) {
            return platform;
        }
        //特殊处理，如果是app-ios或者app-android，返回app-ios-android
        if (platform.equals(PlatformEnum.App_IOS.getCode())
                || platform.equals(PlatformEnum.App_Android.getCode())) {
            return PlatformEnum.App_IOS_Android.getCode();
        }
        return platform;
    }
}
