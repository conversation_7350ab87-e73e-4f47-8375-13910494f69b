package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionLinkTemplateParam;
import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import com.ly.localactivity.marketing.domain.marketing.vo.LinkParamVo;

import java.util.List;

/**
 * <p>
 * 运营推广链接模板参数 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
public interface IOperationPromotionLinkTemplateParamService extends IService<OperationPromotionLinkTemplateParam> {
    /**
     * 新增链接参数
     * @param linkParamRos
     * @param linkTemplateId
     * @return boolean
     */
    Boolean saveOperationPromotionLinkTemplateParams(List<LinkParamRo> linkParamRos, Long linkTemplateId);

    /**
     * 根据 linkTemplateId 获取链接参数
     * @param linkTemplateId
     * @return {@link List}<{@link LinkParamVo}>
     */
    List<LinkParamVo> getLinkTemplateParamsByLinkTemplateId(Long linkTemplateId);


    /**
     * 编辑链接参数 [新增|修改|删除]
     * @param linkParamRos
     * @param linkTemplateId
     * @return boolean
     */
    Boolean updateLinkTemplateParams(List<LinkParamRo> linkParamRos,Long linkTemplateId);

}
