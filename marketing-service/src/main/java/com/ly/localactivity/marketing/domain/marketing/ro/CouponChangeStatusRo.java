package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @description CouponChangeStatusRo
 * @date 2024-04-24
 */
@ApiModel("红包状态变更")
@Data
public class CouponChangeStatusRo {
    @ApiModelProperty("batch_list#id")
    private Long couponBatchId;
    @ApiModelProperty("红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架")
    @Range(min = 1, max = 5, message = "红包状态值错误")
    private Integer status;
    @ApiModelProperty("无效/下架原因")
    private String invalidReason;
}
