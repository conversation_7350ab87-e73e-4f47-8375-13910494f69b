package com.ly.localactivity.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 红包批次运营配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@ApiModel(value = "CouponBatchOperationDto", description = "红包批次运营配置")
public class CouponBatchOperationDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包批次id
     */
    @ApiModelProperty(value = "红包批次id")
    private Long couponBatchId;

    /**
     * 运营场景，1-交叉发放，2-活动主题，3-站内营销，4-投放&导流
     */
    @ApiModelProperty(value = "运营场景，1-交叉发放，2-活动主题，3-站内营销，4-投放&导流")
    private Integer sceneType;

    /**
     * 运营位code
     */
    @ApiModelProperty(value = "运营位code")
    private String operationPositionCode;

    /**
     * 运营位name
     */
    @ApiModelProperty(value = "运营位name")
    private String operationPositionName;

    /**
     * 运营位别名
     */
    @ApiModelProperty(value = "运营位别名")
    private String operationPositionAlias;

    @ApiModelProperty(value = "详情点位code")
    private List<String> detailPositionCodeList;


}
