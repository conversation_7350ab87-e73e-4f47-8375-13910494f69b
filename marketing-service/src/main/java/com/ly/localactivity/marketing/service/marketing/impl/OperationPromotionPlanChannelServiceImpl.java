package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanChannel;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanChannelVo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanChannelMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanChannelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.joda.time.DateTimeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营位平台 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class OperationPromotionPlanChannelServiceImpl extends ServiceImpl<OperationPromotionPlanChannelMapper, OperationPromotionPlanChannel> implements IOperationPromotionPlanChannelService {
    @Resource
    private OperationPromotionPlanChannelMapper operationPromotionPlanChannelMapper;

    /**
     * 保存渠道
     *
     * @param promotionPlan 推广计划
     * @param channels      渠道
     * @param modifier      修改人
     */
    @Override
    public void saveOrUpdateChannelList(OperationPromotionPlan promotionPlan, List<OperationPromotionPlanChannelVo> channels, String modifier) {
        if (CollectionUtil.isEmpty(channels)) {
            return;
        }
        List<OperationPromotionPlanChannel> operationPromotionPlanChannels = operationPromotionPlanChannelMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanChannel>()
                .eq(OperationPromotionPlanChannel::getPlanId, promotionPlan.getId())
                .eq(OperationPromotionPlanChannel::getPositionId, promotionPlan.getPositionId())
                .eq(OperationPromotionPlanChannel::getDeleteFlag, Boolean.FALSE)
        );
        Map<String, OperationPromotionPlanChannel> refIdChannelMap = operationPromotionPlanChannels.stream().collect(Collectors.toMap(OperationPromotionPlanChannel::getRefid, Function.identity()));

        //有效的
        List<Long> validChannelIds = new ArrayList<>();

        List<OperationPromotionPlanChannel> needInsertChannels = new ArrayList<>();
        List<OperationPromotionPlanChannel> needUpdateChannels = new ArrayList<>();
        LocalDateTime currTime = LocalDateTime.now();

        for (OperationPromotionPlanChannelVo channel : channels) {
            OperationPromotionPlanChannel operationPromotionPlanChannel = refIdChannelMap.get(channel.getRefid());
            if (ObjectUtil.isNull(operationPromotionPlanChannel)) {
                operationPromotionPlanChannel = new OperationPromotionPlanChannel();
                operationPromotionPlanChannel.setPlanId(promotionPlan.getId());
                operationPromotionPlanChannel.setPositionId(promotionPlan.getPositionId());
                operationPromotionPlanChannel.setRefid(channel.getRefid());
                operationPromotionPlanChannel.setChannelName(channel.getChannelName());
                operationPromotionPlanChannel.setModifier(modifier);
                operationPromotionPlanChannel.setModifiedTime(currTime);
                operationPromotionPlanChannel.setCreator(modifier);
                operationPromotionPlanChannel.setCreateTime(currTime);
                operationPromotionPlanChannel.setDeleteFlag(Boolean.FALSE);
                needInsertChannels.add(operationPromotionPlanChannel);
            } else {
                operationPromotionPlanChannel.setChannelName(channel.getChannelName());
                operationPromotionPlanChannel.setModifier(modifier);
                operationPromotionPlanChannel.setModifiedTime(currTime);
                validChannelIds.add(operationPromotionPlanChannel.getId());
                needUpdateChannels.add(operationPromotionPlanChannel);
            }
        }


        if (CollectionUtil.isNotEmpty(needUpdateChannels)) {
            needUpdateChannels.forEach(operationPromotionPlanChannelMapper::updateById);
        }
        if (CollectionUtil.isNotEmpty(needInsertChannels)) {
            operationPromotionPlanChannelMapper.insertBatchSomeColumn(needInsertChannels);
        }


        //需要删除的
        List<Long> deleteChannelIds = operationPromotionPlanChannels.stream().map(OperationPromotionPlanChannel::getId).filter(id -> !validChannelIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteChannelIds)) {
            operationPromotionPlanChannelMapper.deleteByIds(deleteChannelIds, modifier);
        }

    }

    /**
     * 按计划id全部删除
     *
     * @param planId       id
     * @param modifier 修改人
     */
    @Override
    public void deleteAllByPlanId(Long planId, String modifier) {
        if (ObjectUtil.isNull(planId)) {
            return;
        }
        List<OperationPromotionPlanChannel> operationPromotionPlanChannels = operationPromotionPlanChannelMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanChannel>()
                .eq(OperationPromotionPlanChannel::getPlanId, planId)
                .eq(OperationPromotionPlanChannel::getDeleteFlag, Boolean.FALSE)
        );
        if (CollectionUtil.isNotEmpty(operationPromotionPlanChannels)) {
            operationPromotionPlanChannelMapper.deleteByIds(operationPromotionPlanChannels.stream().map(OperationPromotionPlanChannel::getId).collect(Collectors.toList()), modifier);
        }
    }
}
