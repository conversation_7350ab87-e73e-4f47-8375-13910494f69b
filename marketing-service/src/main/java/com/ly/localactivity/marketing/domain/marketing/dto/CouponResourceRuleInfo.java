package com.ly.localactivity.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponResourceRuleInfo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponResourceRuleInfo {
    @ApiModelProperty(value = "佣金限制，1-不限，2-佣金>0，3-佣金>=0")
    private Integer commissionType;
    @ApiModelProperty(value = "商品限制类型，1-全部(不限)，2-指定出发地，2-指定二级品类，3-指定商品")
    private Integer limitType;
    @ApiModelProperty(value = "限制数据，根据limitType不同，数据不同")
    private List<String> limitData;
}
