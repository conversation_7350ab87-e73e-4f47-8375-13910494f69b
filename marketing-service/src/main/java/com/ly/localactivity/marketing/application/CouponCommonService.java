package com.ly.localactivity.marketing.application;

import cn.hutool.core.util.EnumUtil;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.marketing.application.model.enums.CouponSourceEnum;
import com.ly.localactivity.marketing.application.model.ro.BaseCouponRo;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponProjectTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.external.coupon.ro.CouponBaseRequest;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * CouponCommonService
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Service
public class CouponCommonService {

    public void getExternalRequest(BaseCouponRo couponRo, CouponBaseRequest baseRequest) {
        if (couponRo.getPlatform() == null || baseRequest == null) {
            return;
        }
        if (couponRo.getPlatform().toString().equals(PlatformEnum.Wx_App.getCode())) {
            baseRequest.setSource(CouponSourceEnum.UnionId.getCode());
            baseRequest.setUserkey(couponRo.getUnionId());
            baseRequest.setOpenId(couponRo.getOpenId());
        } else if (couponRo.getPlatform().toString().equals(PlatformEnum.App_IOS.getCode())
                || couponRo.getPlatform().toString().equals(PlatformEnum.App_Android.getCode())) {
            baseRequest.setSource(CouponSourceEnum.MemberId.getCode());
            baseRequest.setUserkey(couponRo.getMemberId() != null ? couponRo.getMemberId().toString() : "");
        }

        CouponProjectTypeEnum projectType = EnumUtil.getBy(CouponProjectTypeEnum::getCode, couponRo.getProjectType());
        if (projectType == null) {
            throw new ApiException("项目类型错误");
        }
        baseRequest.setProjectId(projectType.getProjectId());
        baseRequest.setProjectIds(Collections.singletonList(projectType.getProjectId()));
        baseRequest.setChannel(couponRo.getPlatform());

        baseRequest.setRequestId(couponRo.getRequestId());
    }

    public String checkReuqest(BaseCouponRo couponRo) {
        PlatformEnum platform = PlatformEnum.codeOf(couponRo.getPlatform().toString());
        if (platform == null) {
            return "平台类型错误";
        }
        if (platform.equals(PlatformEnum.Wx_App)) {
            if (StringUtils.isBlank(couponRo.getUnionId())) {
                //throw new ApiException("unionId不能为空");
                return "unionId不能为空";
            }
//            if (StringUtils.isBlank(couponRo.getOpenId())) {
//            TODO 鲲鹏非必填
////                throw new ApiException("openId不能为空");
//                return "openId不能为空";
//            }
        }else if (platform.equals(PlatformEnum.App_IOS) || platform.equals(PlatformEnum.App_Android)) {
            if (couponRo.getMemberId() == null) {
                throw new ApiException("memberId不能为空");
//                return "memberId不能为空";
            }
        }
        else {
            return "平台类型错误";
        }
        return "";
    }
}
