package com.ly.localactivity.marketing.domain.marketing.ro;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * OperationPositionPageStatusRo
 *
 * <AUTHOR>
 * @date 2024-2-6
 */
@Data
@ApiModel(value = "OperationPositionPageStatusRo",description = "频道页设为有效/无效请求参数")
public class OperationPositionPageStatusRo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;


    /**
     * 频道页是否有效（1正常 0停用）
     */
    @ApiModelProperty(value = "频道页状态")
    @NotNull(message = "频道页状态不能为空")
    private Integer status;
}