package com.ly.localactivity.marketing.common.utils;

import com.ly.localactivity.framework.utils.SpringUtils;
import org.apache.commons.lang3.ArrayUtils;

/**
 * SpringUtil
 *
 * <AUTHOR>
 * @date 2024/8/9
 */
public class SpringUtil {

    /**
     * 获取当前的环境配置，当有多个环境配置时，只获取第一个
     *
     * @return 当前的环境配置
     */
    public static String getActiveProfile() {
        final String[] activeProfiles = SpringUtils.getActiveProfiles();

        if (ArrayUtils.isEmpty(activeProfiles)){
            return null;
        }
        for (String activeProfile : activeProfiles) {
            if(!activeProfile.contains("-")){
                return activeProfile;
            }
        }
        return activeProfiles[0];
    }
}