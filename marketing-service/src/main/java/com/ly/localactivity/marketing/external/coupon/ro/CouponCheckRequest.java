package com.ly.localactivity.marketing.external.coupon.ro;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * CouponCheckRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponCheckRo", description = "券校验")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CouponCheckRequest extends CouponBaseRequest {

    /**
     * 券信息
     */
    @ApiModelProperty(value = "券信息", required = true)
    @NotNull(message = "券信息不能为空")
    @Valid
    private List<CouponInfo> couponInfos;
    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额", required = true)
    @NotNull(message = "订单金额不能为空")
    private BigDecimal orderAmount;
    /**
     * 特殊规则
     */
    @ApiModelProperty(value = "特殊规则")
    private JSONObject extRuleInfo;

    /**
     * 乘机人信息
     */
    @ApiModelProperty(value = "乘机人信息")
    private List<PersonInfo> consumeUsers;
    /**
     * 当地时区时间
     */
    @ApiModelProperty(value = "当地时区时间")
    private String consumeTime;
    /**
     * 消费货币
     */
    @ApiModelProperty(value = "消费货币")
    private String consumeCurrency;
    /**
     * 项目特殊规则是否校验：默认 0 校验；1 不校验
     * 券基础信息依然校验不受影响，包括：有效期，状态，门槛，抵扣金额，二要素，使用渠道，使用项目，
     */
    private Integer isCheckRule;

    @Data
    public static class CouponInfo {
        @NotNull(message = "券号不能为空")
        @NotBlank(message = "券号不能为空")
        private String couponNo;
    }
}

