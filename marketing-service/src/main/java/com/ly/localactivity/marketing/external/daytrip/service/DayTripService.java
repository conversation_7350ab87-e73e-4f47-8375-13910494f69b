package com.ly.localactivity.marketing.external.daytrip.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.annotation.ElapsedTime;
import com.ly.localactivity.framework.annotation.LACacheable;
import com.ly.localactivity.framework.utils.LocalDateTimeUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.config.ConfigConst;
import com.ly.localactivity.marketing.external.daytrip.enums.ProductTypeEnum;
import com.ly.localactivity.marketing.external.daytrip.model.DayTripProductDto;
import com.ly.localactivity.marketing.external.daytrip.model.DayTripSearchRequest;
import com.ly.localactivity.marketing.external.daytrip.model.SearchListRequest;
import com.ly.localactivity.marketing.external.daytrip.model.SearchListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DayTripService
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Slf4j
@Service
public class DayTripService {
    private static final String SEARCH_LIST_URL = "https://search.17usoft.com/localactivity/search";

    @Value("${local-activity.appName}")
    private String appName;

    /**
     * 搜索接口
     *
     * @param request 请求
     * @return {@link SearchListResponse}
     */
    public SearchListResponse search(SearchListRequest request) {
        try {

//            String url = ConfigUtils.get(ConfigConst.DayTrip_SearchUrl);
//            if (StringUtils.isBlank(url)) {
//                return null;
//            }

            request.setCaller(appName);
            if (request.getPageIndex() == null || request.getPageIndex() <= 0) {
                request.setPageIndex(1);
            }
            if (request.getPageSize() == null || request.getPageSize() <= 0) {
                request.setPageSize(10);
            }
            request.setSection(MessageFormat.format("{0}-{1}", (request.getPageIndex() - 1) * request.getPageSize() + 1,
                    request.getPageIndex() * request.getPageSize()));
            String key = IdUtil.simpleUUID();
            SkyLogUtils.infoByMethod(log, "搜索接口请求开始：" + JSON.toJSONString(request), key, "");
            String result = HttpUtil.get(SEARCH_LIST_URL, BeanUtil.beanToMap(request));
            SkyLogUtils.infoByMethod(log, "搜索接口请求结束：" + result, key, "");
            if (StringUtils.isBlank(result)) {
                return null;
            }
            return JSON.parseObject(result, SearchListResponse.class);
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "搜索接口异常", "", "", e);
            return null;
        }
    }

    /**
     * 根据serialId查询产品是否存在
     *
     * @param serialIds serialIds
     * @return boolean
     */
    public boolean existBySerialId(List<String> serialIds) {
        if (CollectionUtil.isEmpty(serialIds)){
            return false;
        }
        SearchListRequest request = SearchListRequest.builder()
                .caller(appName)
                .seriaIds(StringUtils.join(serialIds, ","))
                .sortType("hasPreferred:desc,hotSale:desc")
                .isTest(0)
                .pageIndex(1)
                .pageSize(1)
                .build();

        SearchListResponse searchResult = this.search(request);
        if (searchResult == null || CollectionUtil.isEmpty(searchResult.getItems())) {
            return false;
        }
        return true;
    }

    public List<DayTripProductDto> getProducts(String keyword, List<String> poiIds, List<Long> destinationIds, LocalDateTime dateTime, ProductTypeEnum productType, int limit) {
        SearchListRequest request = SearchListRequest.builder()
                .caller(appName)
                .categoryIds(productType.getCode())
                .sortType("hasPreferred:desc,hotSale:desc")
                .isTest(0)
                .pageIndex(1)
                .pageSize(limit)
                .build();
        // 有关键词
        if (StringUtils.isNotBlank(keyword)) {
            request.setKeyword(keyword);
        }
        // 有目的地
        if (CollectionUtil.isNotEmpty(destinationIds)) {
            request.setDestCityIds(StringUtils.join(destinationIds, ","));
        }
        if (dateTime != null) {
            String sDate = LocalDateTimeUtils.getString(dateTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
            request.setSSaleDay(Convert.toInt(sDate));
        }
        if (CollectionUtil.isNotEmpty(poiIds)) {
            request.setPoiIds(StringUtils.join(poiIds, ","));
        }
        SearchListResponse searchResult = this.search(request);
        if (searchResult == null || CollectionUtil.isEmpty(searchResult.getItems())) {
            return null;
        }
//        SearchListResponse.SearchItems productItem = searchResult.getItems().get(0);

        List<DayTripProductDto> productDtos = new ArrayList<>();
        searchResult.getItems().forEach(s -> {
            DayTripProductDto productDto = new DayTripProductDto();
            productDto.setMinPrice(BigDecimal.valueOf(s.getSellPrice() / 100));
            productDto.setCode(s.getSeriaId());
            productDto.setTitle(s.getTitle());
            productDto.setSubTitle(s.getSubTitle());
            productDto.setScore(s.getScore());
            productDto.setImage(s.getImage());
            if (CollectionUtil.isNotEmpty(s.getPoiItems())) {
                productDto.setPoiIds(s.getPoiItems().stream().map(m -> m.getId().toString()).collect(Collectors.toList()));
            }

            productDtos.add(productDto);
        });

        return productDtos;
    }

//    @LACacheable(key = "'RSE:dayTripList:' + #request.hashCode", expire = 60 * 60 * 2)
    public SearchListResponse getProductResponse(DayTripSearchRequest request) {
        SearchListRequest listRequest = SearchListRequest.builder()
                .caller(appName)
                .categoryIds(request.getProductType().getCode())
                .sortType(StringUtils.isBlank(request.getSortType()) ? "hasPreferred:desc,hotSale:desc" : request.getSortType())
                .isTest(0)
                .pageIndex(1)
                .pageSize(request.getLimit())
                .build();
        // 有关键词
        if (StringUtils.isNotBlank(request.getKeyword())) {
            listRequest.setKeyword(request.getKeyword());
        }
        // 有目的地
        if (CollectionUtil.isNotEmpty(request.getDestinationIds())) {
            listRequest.setDestCityIds(StringUtils.join(request.getDestinationIds(), ","));
        }
        if (request.getDateTime() != null) {
            String sDate = LocalDateTimeUtils.getString(request.getDateTime(), DateTimeFormatter.ofPattern("yyyyMMdd"));
            listRequest.setSSaleDay(Convert.toInt(sDate));
        }
        if (CollectionUtil.isNotEmpty(request.getPoiIds())) {
            listRequest.setPoiIds(StringUtils.join(request.getPoiIds(), ","));
        }
        SearchListResponse searchResult = this.search(listRequest);
        if (searchResult == null || CollectionUtil.isEmpty(searchResult.getItems())) {
            return null;
        }

        return searchResult;
    }
}
