package com.ly.localactivity.marketing.service.marketing.impl;

import com.ly.localactivity.marketing.domain.marketing.CouponRechargeRecord;
import com.ly.localactivity.marketing.mapper.marketing.CouponRechargeRecordMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponRechargeRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 红包发放记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Service
public class CouponRechargeRecordServiceImpl implements ICouponRechargeRecordService {
    @Resource
    private CouponRechargeRecordMapper couponRechargeRecordMapper;

    /**
     * 插入红包发送记录
     *
     * @param couponRechargeRecord 红包发送记录
     * @return {@link Boolean}
     */
    @Override
    public Boolean insertCouponRechargeRecord(CouponRechargeRecord couponRechargeRecord) {
        couponRechargeRecordMapper.insert(couponRechargeRecord);
        return true;
    }
}
