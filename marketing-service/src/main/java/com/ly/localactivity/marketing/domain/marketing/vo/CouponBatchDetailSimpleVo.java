package com.ly.localactivity.marketing.domain.marketing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description CouponBatchDetailSimpleVo
 * @date 2024-04-24
 */
@ApiModel("红包详情")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CouponBatchDetailSimpleVo {
    @ApiModelProperty("红包类型 0-满减、1-折扣")
    private Integer couponType;
    @ApiModelProperty("detailId")
    private Long batchDetailId;
    @ApiModelProperty("红包批次号")
    private String batchNo;
    @ApiModelProperty("红包批次名称")
    private String batchName;
    @ApiModelProperty("红包名称对商家")
    private String batchCname;
    @ApiModelProperty("鲲鹏审核状态")
    private Integer status;
    @ApiModelProperty("库存")
    private Integer stock;
    @ApiModelProperty("剩余库存")
    private Integer remainStock;
    @ApiModelProperty("已领取")
    private Integer receiveCount;
    @ApiModelProperty("已使用")
    private Integer usedCount;
    @ApiModelProperty("活动有效时间类型")
    private Integer eventValidDateType;
    @ApiModelProperty("活动效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime eventValidBeginDate;
    @ApiModelProperty("活动有效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime eventValidEndDate;
}
