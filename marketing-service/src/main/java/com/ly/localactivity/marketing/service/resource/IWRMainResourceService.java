package com.ly.localactivity.marketing.service.resource;

import com.ly.localactivity.marketing.domain.resource.WRMainResource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 玩乐主资源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface IWRMainResourceService extends IService<WRMainResource> {

    List<WRMainResource> getCanSaleProductByMainResourceSerialIds(List<String> mainResourceSerialIdList);

    /**
     * 通过供应商id获取可以销售产品
     *
     * @param supplierId        供应商id
     * @param firstCategoryId
     * @return {@link List}<{@link WRMainResource}>
     */
    List<WRMainResource> getCanSaleProductBySupplierId(Long supplierId, Integer firstCategoryId);

    /**
     * 通过主资源id获取供应商id 缓存
     * @param mainResourceSerialIdList
     * @return
     */
    Map<String, Long> getMainResourceSupplierId(List<String> mainResourceSerialIdList);

}
