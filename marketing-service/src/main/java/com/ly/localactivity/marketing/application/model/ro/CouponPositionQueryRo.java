package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * CouponUserQueryRo
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponPositionQueryRo extends BaseCouponRo{
    @ApiModelProperty(value = "城市id")
    private String cityId;
    @ApiModelProperty(value = "点位code")
    @NotNull(message = "点位code不能为空")
    private String positionCode;
    @ApiModelProperty(value = "资源id")
    private String resourceId;

}
