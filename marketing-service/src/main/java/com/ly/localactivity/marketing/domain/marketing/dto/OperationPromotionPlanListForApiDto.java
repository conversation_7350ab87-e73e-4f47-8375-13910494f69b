package com.ly.localactivity.marketing.domain.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanListForApiDto
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationPromotionPlanListForApiDto {
    private Long positionId;
    private List<Long> positionIds;
    private String platform;
    private String date;
    private Long cityId;
    private Long provinceId;
    private List<Long> areaIds;
    private String areaCoverage;
}
