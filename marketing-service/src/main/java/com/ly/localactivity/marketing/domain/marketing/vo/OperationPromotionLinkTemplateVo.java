package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OperationPromotionLinkTemplateVo
 *
 * <AUTHOR>
 * @date 2024-2-6
 */
@Data
@ApiModel(value = "OperationPromotionLinkTemplateVo",description = "链接模板返回参数")
public class OperationPromotionLinkTemplateVo {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "链接模板id")
    private Long id;

    /**
     * 链接模板名称
     */
    @ApiModelProperty(value = "链接模板名称")
    private String name;

    /**
     * 自定义名称
     */
    @ApiModelProperty(value = "自定义名称")
    private String customerName;

    /**
     * 页面，数据字典 dict_value
     */
    @ApiModelProperty(value = "页面, 数据字典")
    private String pageCode;

    /**
     * 平台code，数据字典
     */
    @ApiModelProperty(value = "平台code, 数据字典")
    private String platform;

    /**
     * 推广链接
     */
    @ApiModelProperty(value = "推广链接")
    private String linkUrl;
    /**
     * 是否自动验证下架，1-是，0-否
     */
    @ApiModelProperty(value = "是否自动验证下架")
    private Integer autoInvalidFlag;

    /**
     * 自动验证下架的参数code
     */
    @ApiModelProperty(value = "自动验证下架的参数code")
    private String[] autoInvalidParamCodes;

    /**
     * 状态，1-有效，0-无效
     */
    @ApiModelProperty(value = "是否有效")
    private Integer status;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifiedTime;

    /**
     * 引用次数
     */
    @ApiModelProperty(value = "引用次数")
    private Integer citationCount;

    /**
     * 链接类型，1-原生链接，2-webview链接
     */
    @ApiModelProperty(value = "链接类型")
    private Integer linkUrlType;

    /**
     * 链接前缀
     */
    @ApiModelProperty(value = "链接前缀")
    private String linkUrlPrefix;
}
