package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 红包发放记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_recharge_record")
public class CouponRechargeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包批次
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 平台
     */
    @TableField("platform")
    private String platform;

    /**
     * refid
     */
    @TableField("refid")
    private String refid;

    /**
     * 发放交易号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 赠送/预发放记录订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 红包来源，同红包接口
     */
    @TableField("source")
    private Integer source;

    /**
     * 用户信息，同红包接口
     */
    @TableField("user_key")
    private String userKey;

    /**
     * 微信openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * deviceId
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 是否礼包，0-否，1-是
     */
    @TableField("package_flag")
    private Integer packageFlag;

    /**
     * 发送来源
     */
    @TableField("send_from")
    private String sendFrom;

    /**
     * 同步状态，1-成功，2-失败
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 同步结果
     */
    @TableField("sync_result")
    private String syncResult;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
