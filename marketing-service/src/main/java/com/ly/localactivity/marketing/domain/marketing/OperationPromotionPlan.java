package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 运营推广计划
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("operation_promotion_plan")
public class OperationPromotionPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * code
     */
    @TableField("code")
    private String code;

    /**
     * 名称，平台_推广方式_顺位_id
     */
    @TableField("name")
    private String name;

    /**
     * 自定义名称
     */
    @TableField("alias")
    private String alias;

    /**
     * 位置id
     */
    @TableField("position_id")
    private Long positionId;

    /**
     * 位置code
     */
    @TableField("position_code")
    private String positionCode;

    /**
     * 平台code，数据字典
     */
    @TableField("platform")
    private String platform;

    /**
     * 推广方式，1-按境内（全国），2-按境外（港澳），3-按大区，4-按省份，4-按城市
     */
    @TableField("promote_type")
    private Integer promoteType;

    /**
     * 顺位，位置序号，从小到大
     */
    @TableField("index_value")
    private Integer indexValue;

    /**
     * 有效期类型，1-长期有效，2-固定日期段
     */
    @TableField("valid_date_type")
    private Integer validDateType;

    /**
     * 有效期开始
     */
    @TableField("valid_begin_date")
    private LocalDateTime validBeginDate;

    /**
     * 有效期结束
     */
    @TableField("valid_end_date")
    private LocalDateTime validEndDate;

    /**
     * 置顶标记，1-是，0-否
     */
    @TableField("top_flag")
    private Integer topFlag;

    /**
     * 展示方式，1-最后顺位，2-兜底展示
     */
    @TableField("default_show_type")
    private Integer defaultShowType;

    /**
     * 兜底标记，1-是，0-否
     */
    @TableField("default_flag")
    private Integer defaultFlag;

    /**
     * 推广原因，数据字典维护
     */
    @TableField("reason_type")
    private String reasonType;

    /**
     * 推广原因备注
     */
    @TableField("reason_remark")
    private String reasonRemark;

    /**
     * 过期失效时间
     */
    @TableField("invalid_time")
    private LocalDateTime invalidTime;

    /**
     * 有效状态，1-有效，0-无效
     */
    @TableField("valid_flag")
    private Integer validFlag;

    /**
     * 无效原因枚举，1-手动设置，2-过期失效，3-产品下架失效，4-占位失效
     */
    @TableField("valid_reason_type")
    private Integer validReasonType;

    /**
     * 无效原因备注
     */
    @TableField("valid_reason_remark")
    private String validReasonRemark;

    /**
     * 是否占位
     */
    @TableField("place_flag")
    private Integer placeFlag;

    /**
     * 占位原因备注
     */
    @TableField("place_reason_remark")
    private String placeReasonRemark;

    @TableField(value = "custom_sort")
    private Integer customSort;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人工号
     */
    @TableField("creator_no")
    private String creatorNo;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人工号
     */
    @TableField("modifier_no")
    private String modifierNo;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
