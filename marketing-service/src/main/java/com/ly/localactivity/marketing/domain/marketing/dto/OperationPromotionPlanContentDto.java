package com.ly.localactivity.marketing.domain.marketing.dto;

import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * PromotionPlanContentDto
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationPromotionPlanContentDto {
    private String platform;
    private Long planId;
    private String imageUrl;
    private String keyword;
    private String linkUrl;
    private Integer linkType;
    private String linkUrlPrefix;
    private Integer linkUrlType;
    private Long linkTemplateId;
    private String imageName;
    private String imageInfo;
    private Boolean autoInvalidFlag;
    private String autoInvalidParamCodes;
    private String subTitle;
    private List<LinkParamRo> linkParams;

    private List<OperationPromotionPlanLinkDto> links;
}
