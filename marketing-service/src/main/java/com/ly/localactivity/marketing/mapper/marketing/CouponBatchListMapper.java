package com.ly.localactivity.marketing.mapper.marketing;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.ro.QueryCouponBatchListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.CouponBatchListVo;
import com.ly.localactivity.marketing.domain.marketing.vo.CouponStatisticsVo;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠券列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchListMapper extends MyBaseMapper<CouponBatchList> {

    /**
     * 分页查询优惠券列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<CouponBatchListVo> pageSelectCouponList(@Param("page") Page<CouponBatchListVo> page, @Param("request") QueryCouponBatchListRo request);

    /**
     * 查询优惠券列表
     *
     * @param request
     * @return
     */
    List<CouponBatchListVo> selectCouponList(@Param("request") QueryCouponBatchListRo request);

    /**
     * 查询红包统计数据
     * @param queryCouponBatchListRo
     * @return
     */
    List<CouponStatisticsVo> queryCouponStatistics(QueryCouponBatchListRo queryCouponBatchListRo);
}
