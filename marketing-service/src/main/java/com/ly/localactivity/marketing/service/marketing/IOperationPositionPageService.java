package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPositionPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageSelectVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageVo;

import java.util.List;

/**
 * <p>
 * 营销运营位页面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface IOperationPositionPageService extends IService<OperationPositionPage> {


    /** 查询频道列表
     * @return {@link List}<{@link OperationPositionPageVo}>
     */
    List<OperationPositionPageVo> listOperationPositionPage();


    /**
     * 新增频道页
     * @param request
     * @return {@link Boolean}
     */
    Boolean insertOperationPositionPage(OperationPositionPageRo request);


    /**
     * 修改频道页
     * @param request
     * @return {@link Boolean}
     */
    Boolean updateOperationPositionPage(OperationPositionPageRo request);


    /**
     * 频道页设为有效/无效
     * @param request
     * @return {@link Boolean}
     */
    Boolean updateOperationPositionPageStatus(OperationPositionPageStatusRo request);

    /**
     * 根据 id 获取频道页详情
     *
     * @param id id
     * @return {@link OperationPositionPageVo}
     */
    OperationPositionPageVo detailOperationPositionPage(Long id);

    /**
     * 搜索频道页页面
     *
     * @param keyword 关键词
     * @return {@link List}<{@link OperationPositionPageSelectVo}>
     */
    List<OperationPositionPageSelectVo> searchOperationPositionPage(String keyword);
}
