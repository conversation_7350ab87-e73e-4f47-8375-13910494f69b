package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * PeriodVo
 *
 * <AUTHOR>
 * @date 2024/2/8
 */
@ApiModel(value = "PeriodVo", description = "时间段返回参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PeriodVo {
    @ApiModelProperty(value = "覆盖数量", required = true)
    private Integer count;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;


}
