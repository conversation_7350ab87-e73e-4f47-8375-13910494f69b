package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * PromoteTypeEnum
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@Getter
@AllArgsConstructor
public enum PromoteTypeEnum {
    DEFAULT(0, "兜底"),
    DOMESTIC(1, "境内(全国)"),
    OVERSEAS(2, "境外(港澳)"),
    ZONE(3, "大区"),
    PROVINCE(4, "省份"),
    CITY(5, "城市"),
    /**
     * 20 境外
     */
    FOREIGN(20, "境外"),
    ;
    private final Integer code;
    private final String name;

    public static String codeToName(Integer code) {
        PromoteTypeEnum reasonTypeEnum = codeOf(code);
        return Objects.isNull(reasonTypeEnum) ? null : reasonTypeEnum.getName();
    }

    public static PromoteTypeEnum codeOf(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
