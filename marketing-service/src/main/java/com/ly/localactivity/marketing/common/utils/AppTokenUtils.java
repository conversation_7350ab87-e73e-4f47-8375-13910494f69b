package com.ly.localactivity.marketing.common.utils;

import com.ly.localactivity.framework.common.CommonConstant;
import com.ly.localactivity.marketing.domain.base.SysAppDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * AppTokenUtils
 *
 * @auth aaron
 * @date 2023/12/22
 */
@Component
public class AppTokenUtils {

    @Autowired
    private HttpServletRequest request;

//    @Resource
//    private ISysAppListService sysAppListService;

    /**
     * 获取当前应用程序id
     *
     * @return {@link String}
     */
    public String getCurrentAppId() {
        Object appKey = request.getAttribute(CommonConstant.REQUEST_APP_ID);
        return appKey != null ? appKey.toString() : "";
    }

    /**
     * 获取当前请求id
     *
     * @return {@link String}
     */
    public String getCurrentRequestId() {
        Object requestId = request.getAttribute(CommonConstant.REQUEST_ID);
        return requestId != null ? requestId.toString() : "";
    }

    /**
     * 获取当前渠道
     *
     * @return {@link SysAppDto}
     */
    public SysAppDto getCurrentApp(){
//        String appKey = getCurrentAppId();
//        return sysAppListService.getByAppId(appKey);
        return null;
    }
}
