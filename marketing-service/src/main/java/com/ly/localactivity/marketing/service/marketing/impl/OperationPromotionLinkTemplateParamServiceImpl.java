package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.LinkParamValueTypeEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionLinkTemplateParam;
import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import com.ly.localactivity.marketing.domain.marketing.vo.LinkParamVo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionLinkTemplateParamMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionLinkTemplateParamService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营推广链接模板参数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@Service
public class OperationPromotionLinkTemplateParamServiceImpl extends ServiceImpl<OperationPromotionLinkTemplateParamMapper, OperationPromotionLinkTemplateParam> implements IOperationPromotionLinkTemplateParamService {
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private OperationPromotionLinkTemplateParamServiceImpl operationPromotionLinkTemplateParamService;
    /**
     * 新增链接参数(批量新增)
     *
     * @param linkParamRos 链接参数列表
     * @param linkTemplateId 链接模板id
     * @return boolean
     */
    @Override
    public Boolean saveOperationPromotionLinkTemplateParams(List<LinkParamRo> linkParamRos, Long linkTemplateId) {
        for (LinkParamRo linkParamRo : linkParamRos) {
            // 参数类型和参数值校验
            operationPromotionLinkTemplateParamService.verifyLinkParamValue(linkParamRo);
            OperationPromotionLinkTemplateParam operationPromotionLinkTemplateParam = new OperationPromotionLinkTemplateParam();
            operationPromotionLinkTemplateParam.setParamCode(linkParamRo.getParamCode());
            operationPromotionLinkTemplateParam.setParamValue(linkParamRo.getParamValue());
            operationPromotionLinkTemplateParam.setValueType(linkParamRo.getValueType());
            operationPromotionLinkTemplateParam.setLinkTemplateId(linkTemplateId);
            operationPromotionLinkTemplateParam.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            operationPromotionLinkTemplateParam.setCreator(adminTokenUtils.getModifier());
            operationPromotionLinkTemplateParam.setCreateTime(LocalDateTime.now());
            operationPromotionLinkTemplateParam.setModifier(adminTokenUtils.getModifier());
            operationPromotionLinkTemplateParam.setModifiedTime(LocalDateTime.now());
            save(operationPromotionLinkTemplateParam);
        }
        return true;
    }

    /**
     * 根据 linkTemplateId 获取链接参数列表
     *
     * @param linkTemplateId 链接模板id
     * @return {@link List}<{@link LinkParamVo}>
     */
    @Override
    public List<LinkParamVo> getLinkTemplateParamsByLinkTemplateId(Long linkTemplateId) {
        List<OperationPromotionLinkTemplateParam> operationPromotionLinkTemplateParams =
                list(new LambdaQueryWrapper<OperationPromotionLinkTemplateParam>()
                        .eq(OperationPromotionLinkTemplateParam::getLinkTemplateId, linkTemplateId)
                        .eq(OperationPromotionLinkTemplateParam::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
       return BeanUtil.copyToList(operationPromotionLinkTemplateParams, LinkParamVo.class);
    }


    /**
     * 编辑链接参数 [新增|修改|删除]
     *
     * @param linkParamRos 链接参数列表
     * @param linkTemplateId 链接模板id
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateLinkTemplateParams(List<LinkParamRo> linkParamRos, Long linkTemplateId) {
        List<LinkParamVo> dbLinkParams = operationPromotionLinkTemplateParamService.getLinkTemplateParamsByLinkTemplateId(linkTemplateId);
        List<String> newParamCodeList = linkParamRos.stream().map(LinkParamRo::getParamCode).collect(Collectors.toList());
        // 遍历数据库
        for (LinkParamVo linkParamVo : dbLinkParams) {
            String oldParamCode = linkParamVo.getParamCode();
            // 数据库存在，请求参数中不存在，做删除
            if (!newParamCodeList.contains(oldParamCode)) {
                LambdaUpdateWrapper<OperationPromotionLinkTemplateParam> delLinkTemplateParam = new LambdaUpdateWrapper<>();
                delLinkTemplateParam.eq(OperationPromotionLinkTemplateParam::getParamCode, oldParamCode).eq(OperationPromotionLinkTemplateParam::getLinkTemplateId, linkTemplateId);
                remove(delLinkTemplateParam);
            }
        }
        List<LinkParamRo> insertLinkParamList = new ArrayList<>();
        // 遍历请求参数
        for (LinkParamRo linkParamRo : linkParamRos) {
            if(linkParamRo == null) {
                continue;
            }
            // 参数类型和参数值校验
            operationPromotionLinkTemplateParamService.verifyLinkParamValue(linkParamRo);
            LambdaQueryWrapper<OperationPromotionLinkTemplateParam> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OperationPromotionLinkTemplateParam::getLinkTemplateId, linkTemplateId)
                    .eq(OperationPromotionLinkTemplateParam::getParamCode, linkParamRo.getParamCode());
            OperationPromotionLinkTemplateParam oldLinkTemplateParam = getOne(queryWrapper);
            // 数据库查不到，做新增
            if (oldLinkTemplateParam == null) {
                insertLinkParamList.add(linkParamRo);
            } else {  // 数据库能查到，做修改
                oldLinkTemplateParam.setParamCode(linkParamRo.getParamCode());
                oldLinkTemplateParam.setParamValue(linkParamRo.getParamValue());
                oldLinkTemplateParam.setValueType(linkParamRo.getValueType());
                oldLinkTemplateParam.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                oldLinkTemplateParam.setModifier(adminTokenUtils.getModifier());
                oldLinkTemplateParam.setModifiedTime(LocalDateTime.now());
                updateById(oldLinkTemplateParam);
            }
        }
        if (!CollectionUtils.isEmpty(insertLinkParamList)) {
            operationPromotionLinkTemplateParamService.saveOperationPromotionLinkTemplateParams(insertLinkParamList, linkTemplateId);
        }
        return true;
    }

    private void verifyLinkParamValue(LinkParamRo linkParamRo) {
        // 继承点位|人工配置，不能有 paramValue
        if ((Objects.equals(linkParamRo.getValueType(), LinkParamValueTypeEnum.INHERITS_PARAM.getCode())
                || Objects.equals(linkParamRo.getValueType(), LinkParamValueTypeEnum.MANUAL_PARAM.getCode()))
                && !StringUtils.isBlank(linkParamRo.getParamValue())) {
            throw new ApiException("链接参数有误!");
        }
        // 固定参数必须有 paramValue
        if (Objects.equals(linkParamRo.getValueType(), LinkParamValueTypeEnum.FIXED_PARAM.getCode())
                && StringUtils.isBlank(linkParamRo.getParamValue())) {
            throw new ApiException("链接参数有误!");
        }
    }
}
