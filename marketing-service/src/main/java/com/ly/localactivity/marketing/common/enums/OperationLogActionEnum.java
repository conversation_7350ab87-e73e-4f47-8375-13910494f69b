package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OperationLogActionEnum
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Getter
@AllArgsConstructor
public enum OperationLogActionEnum {

    Other(0, "其他"),
    Add(1, "新增"),
    Delete(2, "删除"),
    Update(3, "修改"),
    Query(4, "查询"),
    ;
    private Integer code;
    private String name;
}
