package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 运营位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("operation_position")
public class OperationPosition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * code，按规则生成，{position_page_id}_{location}_{output_type}
     */
    @TableField("code")
    private String code;

    /**
     * 运营位名称
     */
    @TableField("name")
    private String name;

    /**
     * 自定义名称
     */
    @TableField("alias")
    private String alias;

    /**
     * 页面id，operation_position_page.id
     */
    @TableField("position_page_id")
    private Long positionPageId;

    /**
     * 位置，配置数据字典，固定格式：{模块}_{纵向位置}_{顺序}_{横向位置}_{顺序}
     */
    @TableField("location")
    private String location;

    /**
     * 输出类型，1-幻灯片，2-图片, 3-ICON, 4-ICON+标题，5-关键词
     */
    @TableField("output_type")
    private Integer outputType;

    /**
     * 图片地址
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 图片宽度
     */
    @TableField("image_width")
    private Integer imageWidth;

    /**
     * 图片高度
     */
    @TableField("image_height")
    private Integer imageHeight;

    /**
     * 图片最大张数
     */
    @TableField("image_max_count")
    private Integer imageMaxCount;

    /**
     * 单位kb
     */
    @TableField("image_max_size")
    private Integer imageMaxSize;

    /**
     * 关键词最大长度
     */
    @TableField("keyword_max_length")
    private Integer keywordMaxLength;

    /**
     * 关键词最大数量
     */
    @TableField("keyword_max_count")
    private Integer keywordMaxCount;

    /**
     * 副标题最大长度
     */
    @TableField("sub_title_max_length")
    private Integer subTitleMaxLength;

    /**
     * 地区覆盖，1-境内，2-境外，多个逗号间隔，数据字典
     */
    @TableField("cover_region_type")
    private String coverRegionType;

    /**
     * 是否允许为空
     */
    @TableField("empty_flag")
    private Integer emptyFlag;

    /**
     * 状态（1有效、 0无效）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否埋点
     */
    @TableField("event_tracking_flag")
    private Boolean eventTrackingFlag;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 失效时间
     */
    @TableField("invalid_time")
    private LocalDateTime invalidTime;

    /**
     * 到期天数
     */
    @TableField("expire_days")
    private Integer expireDays;

    /**
     * 必填已维护标识0-未维护,1-已维护,emptyFlag=0时有效
     */
    @TableField("required_maintained_flag")
    private Boolean requiredMaintainedFlag;

    /**
     * 可弹推广计划数
     */
    @TableField("popup_plan_count")
    private Integer popupPlanCount;

    /**
     * 计划可弹层次数，1-每天一次，2-仅一次，3-每N天一次
     */
    @TableField("popup_frequency_type")
    private Integer popupFrequencyType;

    /**
     * 每N天一次
     */
    @TableField("popup_frequency_days")
    private Integer popupFrequencyDays;

    /**
     * 红包领取方式 1-领取、2-直充
     */
    @TableField("pickup_method")
    private Integer pickupMethod;

    /**
     * 点位使用方 1-平台、2-商家 用逗号分割
     */
    @TableField("position_user")
    private String positionUser;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人工号
     */
    @TableField("creator_no")
    private String creatorNo;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人工号
     */
    @TableField("modifier_no")
    private String modifierNo;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;

    /**
     * 随机数
     */
    @TableField("random_key")
    private String randomKey;


}
