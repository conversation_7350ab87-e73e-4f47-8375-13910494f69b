package com.ly.localactivity.marketing.application.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * BaseRequest
 *
 * @date 2023/12/19
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BaseSignRequest {
    /**
     * 请求id
     */
    @ApiModelProperty(value = "请求id", required = true)
    @NotBlank(message = "requestId不能为空")
    private String requestId;
    /**
     * appKey
     */
    @ApiModelProperty(value = "appKey", required = true)
    @NotBlank(message = "appId不能为空")
    private String appId;
    /**
     * 签名
     */
    @ApiModelProperty(value = "签名", required = true)
    @NotBlank(message = "sign不能为空")
    private String sign;
    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳", required = true)
    @NotNull(message = "timestamp不能为空")
    @Min(value = 1, message = "timestamp必须大于0")
    private long timestamp;
}
