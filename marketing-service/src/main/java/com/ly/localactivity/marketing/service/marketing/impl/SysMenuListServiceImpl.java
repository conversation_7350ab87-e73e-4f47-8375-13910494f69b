package com.ly.localactivity.marketing.service.marketing.impl;

import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.marketing.domain.marketing.SysMenuList;
import com.ly.localactivity.marketing.mapper.marketing.SysMenuListMapper;
import com.ly.localactivity.marketing.service.marketing.ISysMenuListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Service
public class SysMenuListServiceImpl extends ServiceImpl<SysMenuListMapper, SysMenuList> implements ISysMenuListService {

    @MultipleTransaction(name = {TransactionConstant.TcZBActivityMarketing})
//    @Transactional(rollbackFor = Exception.class)
    public void testTrans() {
        SysMenuList sysMenuList = new SysMenuList();
        sysMenuList.setName("test");
        sysMenuList.setCreator("");
        sysMenuList.setCreateTime(LocalDateTime.now());
        sysMenuList.setDeleteFlag(false);
        sysMenuList.setModifier("");
        sysMenuList.setModifiedTime(LocalDateTime.now());
        this.save(sysMenuList);

        if (!sysMenuList.getDeleteFlag()){
            throw new ApiException("test");
        }
    }
}
