package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 鲲鹏红包状态
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponDetailStatusEnum {

    WAIT_AUDIT(0, "待审核"),
    AUDITING(1, "审批中"),
    PASS(2, "审批通过"),
    REJECT(3, "已驳回"),
    CANCEL(4, "已取消"),
    INVALID(5, "无效"),
    NULLIFY(6, "已作废"),
    GENERATING(7, "兑换码生成中"),
    PENDING_GENERATE(8, "兑换码待生成"),
    USED_EXPIRED(9, "使用有效期已过期"),
    ;
    private final Integer code;
    private final String desc;
}
