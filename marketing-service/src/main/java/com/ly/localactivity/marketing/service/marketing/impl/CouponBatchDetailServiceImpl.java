package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.enums.DataFlagEnum;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.marketing.common.constants.DateConst;
import com.ly.localactivity.marketing.common.enums.*;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchValidDateDto;
import com.ly.localactivity.marketing.domain.marketing.ro.BatchPushCouponRo;
import com.ly.localactivity.marketing.domain.marketing.ro.QueryCouponBatchDetailListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchDetailMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchDetailService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchPlatformService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchValidDateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包批次明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
@Slf4j
public class CouponBatchDetailServiceImpl extends ServiceImpl<CouponBatchDetailMapper, CouponBatchDetail> implements ICouponBatchDetailService {
    @Resource
    private ICouponBatchDetailService couponBatchDetailService;
    @Resource
    private CouponBatchDetailMapper couponBatchDetailMapper;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ICouponBatchValidDateService couponBatchValidDateService;
    @Resource
    private ICouponBatchPlatformService couponBatchPlatformService;

    /**
     * 保存或更新
     *
     * @param couponBatchList
     * @param couponBatchDetailDtoList
     * @return
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchDetailDto> couponBatchDetailDtoList) {
        if (couponBatchDetailDtoList == null) {
            return true;
        }
        LambdaQueryWrapper<CouponBatchDetail> couponBatchDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        couponBatchDetailLambdaQueryWrapper.eq(CouponBatchDetail::getCouponBatchId, couponBatchList.getId());
        couponBatchDetailLambdaQueryWrapper.eq(CouponBatchDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        List<CouponBatchDetail> oldCouponBatchDetailList = couponBatchDetailMapper.selectList(couponBatchDetailLambdaQueryWrapper);
        Map<Long, CouponBatchDetail> oldIdMap = oldCouponBatchDetailList.stream().collect(Collectors.toMap(CouponBatchDetail::getId, Function.identity(), (a, b) -> a));
        Map<String, CouponBatchDetail> batchNoMap = oldCouponBatchDetailList.stream().collect(Collectors.toMap(CouponBatchDetail::getBatchNo, Function.identity(), (a, b) -> a));

        List<Long> validIdList = new ArrayList<>();
        for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailDtoList) {
            CouponBatchDetail couponBatchDetail = BeanUtil.copyProperties(couponBatchDetailDto, CouponBatchDetail.class);

            // 去除脏数据
            this.cleanCouponBatchDetail(couponBatchDetail);

            // 默认固定时间段
            couponBatchDetail.setEventValidDateType(ValidDateTypeEnum.LONG_TERM.getCode());
            couponBatchDetail.setCouponBatchId(couponBatchList.getId());
            couponBatchDetail.setModifiedTime(LocalDateTime.now());
            couponBatchDetail.setModifier(adminTokenUtils.getModifier());
            couponBatchDetail.setLastSyncTime(LocalDateTime.now());
            if (couponBatchDetail.getEventValidEndDate() != null) {
                couponBatchDetail.setEventValidEndDate(LocalDateTimeUtil.endOfDay(couponBatchDetail.getEventValidEndDate(), true));
            }

            // 优先使用id匹配
            CouponBatchDetail oldCouponBatchDetail;
            if (couponBatchDetail.getId() != null) {
                oldCouponBatchDetail = oldIdMap.get(couponBatchDetail.getId());
            } else {
                oldCouponBatchDetail = batchNoMap.get(couponBatchDetail.getBatchNo());
            }

            // 新增
            if (oldCouponBatchDetail == null) {
                couponBatchDetail.setRemainStock(couponBatchDetail.getStock());
                couponBatchDetail.setCreator(adminTokenUtils.getModifier());
                couponBatchDetail.setCreateTime(LocalDateTime.now());
                couponBatchDetail.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                couponBatchDetailMapper.insert(couponBatchDetail);
            } else {
                couponBatchDetail.setId(oldCouponBatchDetail.getId());
                validIdList.add(oldCouponBatchDetail.getId());
                couponBatchDetailMapper.updateById(couponBatchDetail);
            }

            // 保存有效期
            couponBatchValidDateService.saveOrUpdate(couponBatchDetail, couponBatchDetailDto.getCouponBatchValidDate());

            // 保存平台信息
            couponBatchPlatformService.saveOrUpdate(couponBatchDetail, couponBatchDetailDto.getPlatformList());
        }

        // 删除无效数据
        List<Long> invalidIdList = oldCouponBatchDetailList.stream().map(CouponBatchDetail::getId).filter(id -> !validIdList.contains(id)).collect(Collectors.toList());

        couponBatchDetailService.deleteByIds(invalidIdList);

        return true;
    }

    private void cleanCouponBatchDetail(CouponBatchDetail couponBatchDetail) {
        if (!CouponTypeEnum.FULL_DISCOUNT.getCode().equals(couponBatchDetail.getCouponType())){
            couponBatchDetail.setDiscountAmount(BigDecimal.ZERO);
        }
        if (!CouponTypeEnum.DISCOUNT.getCode().equals(couponBatchDetail.getCouponType())){
            couponBatchDetail.setDiscountMaxAmount(BigDecimal.ZERO);
            couponBatchDetail.setDiscountRate(0);
        }
    }

    /**
     * 根据主键删除
     * @param ids
     * @return
     */
    public Boolean deleteByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return true;
        }
        // 更新状态为 取消
        CouponBatchDetail cancelBatchDetail = new CouponBatchDetail();
        cancelBatchDetail.setStatus(CouponDetailStatusEnum.CANCEL.getCode());
        cancelBatchDetail.setModifier(adminTokenUtils.getModifier());
        cancelBatchDetail.setModifiedTime(LocalDateTime.now());
        cancelBatchDetail.setDeleteFlag(DeleteFlagEnum.IS_DELETE.getCode());
        LambdaUpdateWrapper<CouponBatchDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CouponBatchDetail::getId, ids);
        couponBatchDetailService.update(cancelBatchDetail, updateWrapper);

        return true;
    }

    @Override
    public Map<Long, List<CouponBatchDetailDto>> selectByCouponBatchId(List<Long> couponBatchIdList) {
        if (CollectionUtil.isEmpty(couponBatchIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchDetail> couponBatchDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        couponBatchDetailLambdaQueryWrapper.in(CouponBatchDetail::getCouponBatchId, couponBatchIdList);
        couponBatchDetailLambdaQueryWrapper.in(CouponBatchDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        List<CouponBatchDetail> couponBatchDetailList = couponBatchDetailMapper.selectList(couponBatchDetailLambdaQueryWrapper);

        List<CouponBatchDetailDto> couponBatchDetailDtoList = BeanUtil.copyToList(couponBatchDetailList, CouponBatchDetailDto.class);

        if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
            return new HashMap<>();
        }

        List<Long> couponBatchDetailIdList = couponBatchDetailDtoList.stream().map(CouponBatchDetailDto::getId).collect(Collectors.toList());
        // 查询有效期
        Map<Long, CouponBatchValidDateDto> couponBatchDetailIdValidDateDtoMap = couponBatchValidDateService.selectByCouponBatchDetailIdList(couponBatchDetailIdList);

        // 查询平台信息
        Map<Long, List<PlatformVo>> couponBatchDetailIdPlatformMap = couponBatchPlatformService.selectByCouponBatchDetailIdList(couponBatchDetailIdList);

        for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailDtoList) {
            couponBatchDetailDto.setEventValidDateRange(Arrays.asList(LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidBeginDate().toLocalDate(), DateConst.DEFAULT_FORMAT), LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidEndDate().toLocalDate(), DateConst.DEFAULT_FORMAT)));
            couponBatchDetailDto.setCouponBatchValidDate(couponBatchDetailIdValidDateDtoMap.get(couponBatchDetailDto.getId()));
            couponBatchDetailDto.setReceiveStock(couponBatchDetailDto.getStock() - couponBatchDetailDto.getRemainStock());
            if (couponBatchDetailDto.getCouponBatchValidDate() != null) {
                CouponBatchValidDateDto couponBatchValidDate = couponBatchDetailDto.getCouponBatchValidDate();
                couponBatchValidDate.setValidBeginDate(DateConst.DEFAULT_TIME.equals(couponBatchValidDate.getValidBeginDate()) ? null : couponBatchValidDate.getValidBeginDate());
                couponBatchValidDate.setValidEndDate(DateConst.DEFAULT_TIME.equals(couponBatchValidDate.getValidEndDate()) ? null : couponBatchValidDate.getValidEndDate());
                couponBatchDetailDto.getCouponBatchValidDate()
                        .setValidDateRange(ObjectUtils.isNull(couponBatchValidDate.getValidBeginDate()) ? Collections.emptyList()
                                : Arrays.asList(LocalDateTimeUtil.format(couponBatchValidDate.getValidBeginDate().toLocalDate(), DateConst.DEFAULT_FORMAT),
                                LocalDateTimeUtil.format(couponBatchValidDate.getValidEndDate().toLocalDate(), DateConst.DEFAULT_FORMAT)));
            }

            couponBatchDetailDto.setPlatformList(couponBatchDetailIdPlatformMap.get(couponBatchDetailDto.getId()));
            if (CollectionUtil.isNotEmpty(couponBatchDetailDto.getPlatformList())) {
                couponBatchDetailDto.setPlatformCodeList(couponBatchDetailDto.getPlatformList().stream().map(PlatformVo::getCode).collect(Collectors.toList()));
            }
            if (couponBatchDetailDto.getReceiveUserLimit() < 0) {
                couponBatchDetailDto.setReceiveUserLimitType(couponBatchDetailDto.getReceiveUserLimit());
            } else {
                couponBatchDetailDto.setReceiveUserLimitType(DataFlagEnum.Valid.getCode());
            }

            // 计算批次状态
            calcDetailMarketingStatus(couponBatchDetailDto);
        }

        return couponBatchDetailDtoList.stream().collect(Collectors.groupingBy(CouponBatchDetailDto::getCouponBatchId));
    }

    /**
     * 计算批次状态
     *
     * @param couponBatchDetailDto 优惠券批次明细
     */
    public void calcDetailMarketingStatus(CouponBatchDetailDto couponBatchDetailDto) {
        if (couponBatchDetailDto == null) {
            return;
        }
        couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.INVALID.getCode());
        if (CouponDetailStatusEnum.PASS.getCode().equals(couponBatchDetailDto.getStatus())) {
            LocalDateTime currentDay = LocalDateTime.now();
            if (currentDay.isBefore(couponBatchDetailDto.getEventValidBeginDate())){
                couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.NOT_START.getCode());
            }else if (currentDay.isAfter(couponBatchDetailDto.getEventValidEndDate())){
                couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.EXPIRED.getCode());
            }else {
                couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.ACTIVE.getCode());
            }

            // 计算是否属于使用有效期内
            if (couponBatchDetailDto.getCouponBatchValidDate() != null){
                CouponBatchValidDateDto couponBatchValidDate = couponBatchDetailDto.getCouponBatchValidDate();
                LocalDateTime mostStartTime = couponBatchDetailDto.getEventValidBeginDate();
                LocalDateTime mostEndTime = couponBatchDetailDto.getEventValidEndDate();
                if (ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchValidDate.getValidDateType())){
                    mostStartTime = couponBatchValidDate.getValidBeginDate();
                    mostEndTime = couponBatchValidDate.getValidEndDate();
                }else if (ValidDateTypeEnum.SPECIFIED_PERIOD.getCode().equals(couponBatchValidDate.getValidDateType())){
                    Integer startDay = couponBatchValidDate.getDynamicStartDay();
                    Integer durationDay = couponBatchValidDate.getDynamicDurationDay();
                    mostStartTime = mostStartTime.plusDays(startDay);
                    mostEndTime = mostEndTime.plusDays(startDay + durationDay);
                }else if (ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(couponBatchValidDate.getValidDateType())) {
                    Integer countdownHour = couponBatchValidDate.getCountdownHour();
                    mostStartTime = mostStartTime.plusHours(countdownHour);
                    mostEndTime = mostEndTime.plusHours(countdownHour);
                }else if (ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDate.getValidDateType())) {
                    Integer countdownMinute = couponBatchValidDate.getCountdownMinute();
                    mostStartTime = mostStartTime.plusMinutes(countdownMinute);
                    mostEndTime = mostEndTime.plusMinutes(countdownMinute);
                }

                if(!currentDay.isBefore(mostStartTime) && !currentDay.isAfter(mostEndTime)){
                    couponBatchDetailDto.setInValidDate(Boolean.TRUE);
                }

            }
        }else if (CouponDetailStatusEnum.USED_EXPIRED.getCode().equals(couponBatchDetailDto.getStatus())){
            couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.EXPIRED.getCode());
        } else if (!CouponDetailStatusEnum.WAIT_AUDIT.getCode().equals(couponBatchDetailDto.getStatus()) && !CouponDetailStatusEnum.AUDITING.getCode().equals(couponBatchDetailDto.getStatus())){
            couponBatchDetailDto.setMarketingStatus(CouponMarketingStatusEnum.DE_LISTED.getCode());
        }

    }

    @Override
    public List<CouponBatchDetail> selectNeedRefreshCouponBatchDetailList(BatchPushCouponRo request) {
        return couponBatchDetailMapper.selectNeedRefreshCouponBatchDetailList(request);
    }

    @Override
    public CouponBatchDetail selectByBatchNo(String batchNo) {
        if (StringUtils.isBlank(batchNo)){
            return null;
        }

        return couponBatchDetailMapper.selectOne(new LambdaQueryWrapper<CouponBatchDetail>().eq(CouponBatchDetail::getBatchNo, batchNo).last("limit 1"));
    }


    /**
     * 根据批次号列表查询
     *
     * @param batchNoList 批次号列表
     * @param isDelete
     * @return
     */
    @Override
    public List<CouponBatchDetailDto> selectByBatchNoList(List<String> batchNoList, Boolean isDelete) {
        if (CollectionUtil.isEmpty(batchNoList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<CouponBatchDetail> couponBatchDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        couponBatchDetailLambdaQueryWrapper.in(CouponBatchDetail::getBatchNo, batchNoList);
        if (isDelete != null){
            couponBatchDetailLambdaQueryWrapper.in(CouponBatchDetail::getDeleteFlag, isDelete);
        }
        List<CouponBatchDetail> couponBatchDetailList = couponBatchDetailMapper.selectList(couponBatchDetailLambdaQueryWrapper);

        return BeanUtil.copyToList(couponBatchDetailList, CouponBatchDetailDto.class);
    }

    /**
     * 更新红包批次明细
     * @param couponBatchDetailDto
     * @return
     */
    @Override
    public Boolean updateDetail(CouponBatchDetailDto couponBatchDetailDto) {
        CouponBatchDetail couponBatchDetail = BeanUtil.copyProperties(couponBatchDetailDto, CouponBatchDetail.class);
        couponBatchDetail.setModifiedTime(LocalDateTime.now());
        couponBatchDetail.setModifier(adminTokenUtils.getModifier());
        couponBatchDetail.setLastSyncTime(LocalDateTime.now());
        couponBatchDetailMapper.updateById(couponBatchDetail);
        if (couponBatchDetailDto.getCouponBatchValidDate() != null) {
            couponBatchValidDateService.saveOrUpdate(couponBatchDetail, couponBatchDetailDto.getCouponBatchValidDate());
        }
        return true;
    }

    /**
     * 分页查询红包批次明细列表
     *
     * @param request
     * @return
     */
    @Override
    public CommonPage<CouponBatchDetailDto> pageQuery(QueryCouponBatchDetailListRo request) {
        Page<CouponBatchDetail> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LambdaQueryWrapper<CouponBatchDetail> couponBatchDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        couponBatchDetailLambdaQueryWrapper.eq(CouponBatchDetail::getCouponBatchId, request.getCouponBatchId());
        Page<CouponBatchDetail> pageResult = couponBatchDetailService.page(page, couponBatchDetailLambdaQueryWrapper);
        CommonPage<CouponBatchDetailDto> commonPage = new CommonPage<>();
        commonPage.setTotal(pageResult.getTotal());
        commonPage.setPageIndex(request.getPageIndex());
        commonPage.setPageSize(request.getPageSize());
        List<CouponBatchDetailDto> couponBatchDetailDtoList = BeanUtil.copyToList(pageResult.getRecords(), CouponBatchDetailDto.class);

        for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailDtoList) {
            calcDetailMarketingStatus(couponBatchDetailDto);
            couponBatchDetailDto.setEventValidDateRange(Arrays.asList(LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidBeginDate().toLocalDate(), DateConst.DEFAULT_FORMAT), LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidEndDate().toLocalDate(), DateConst.DEFAULT_FORMAT)));
        }


        commonPage.setList(couponBatchDetailDtoList);

        return commonPage;
    }
}
