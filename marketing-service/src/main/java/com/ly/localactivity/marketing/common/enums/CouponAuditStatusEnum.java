package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 审核状态，1-未提交、2-待审核、3-审核中、4-审核通过、5-审核驳回
 * @date 2024-04-22
 */
@AllArgsConstructor
@Getter
public enum CouponAuditStatusEnum {
    /**
     * 1-未提交
     */
    UN_COMMIT(1, "未提交"),
    /**
     * 2-待审核
     */
    WAIT_AUDIT(2, "待审核"),
    /**
     * 3-审核中
     */
    AUDITING(3, "审核中"),
    /**
     * 4-审核通过
     */
    PASS(4, "审核通过"),
    /**
     * 5-审核驳回
     */
    REJECT(5, "审核驳回")

    ;
    private final Integer code;
    private final String desc;

}
