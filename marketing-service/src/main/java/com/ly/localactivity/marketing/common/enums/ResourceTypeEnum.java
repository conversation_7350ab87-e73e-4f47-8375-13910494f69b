package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ResourceTypeEnum
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {
    /**
     * 一日游
     */
    DayTrip(1, "一日游"),
    /**
     * 景点
     */
    Scenery(2, "景点"),
    /**
     * 度假景点
     */
    Scenery_Vaction(3, "度假景点"),
    ;

    private Integer code;
    private String name;
}
