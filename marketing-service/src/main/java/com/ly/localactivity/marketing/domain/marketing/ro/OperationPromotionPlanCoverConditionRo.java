package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanCoverConditionRo
 *
 * <AUTHOR>
 * @date 2024/2/19
 */
@ApiModel(value = "OperationPromotionPlanCoverConditionRo", description = "当前顺位已经覆盖时间段请求参数")
@Data
public class OperationPromotionPlanCoverConditionRo {
    @ApiModelProperty(value = "推广方式", required = true)
    @NotNull(message = "推广方式不能为空")
    private Integer promoteType;
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "运营点位 ID", required = true)
    @NotNull(message = "运营点位 ID不能为空")
    private Long pointId;
    @ApiModelProperty(value = "顺位", required = true)
    @NotNull(message = "顺位不能为空")
    private Integer indexValue;
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "推广计划id", required = false)
    private Long planId;
    @ApiModelProperty(value = "地区 ids", required = true)
    private List<Long> regionIds;
}
