package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CouponBatchQueryRo implements Serializable {
    private static final long serialVersionUID = -5337091165507012781L;

    @ApiModelProperty(value = "红包id")
    @NotNull(message = "红包id不能为空")
    private Long couponBatchId;

    private Boolean needProductInfo;

}
