package com.ly.localactivity.marketing.application.model.dto;

import com.ly.localactivity.marketing.application.model.ro.BaseCouponRo;
import com.ly.localactivity.marketing.application.model.ro.CouponLimitRo;
import com.ly.localactivity.marketing.common.enums.coupon.CouponExtStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponQueryDto
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponQueryDto {
    private  BaseCouponRo queryRo;
    private CouponExtStatusEnum status;
    private boolean simpleMode;
    private List<String> batchNos;
    private CouponLimitRo limitData;
    private boolean filterPositionCode;
    private String positionCode;

    private Integer timeOut;
}
