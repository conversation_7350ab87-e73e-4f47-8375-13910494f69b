package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.common.enums.CouponSceneTypeEnum;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperationChannel;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationChannelDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchOperationChannelMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchOperationChannelService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包批次运营渠道配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class CouponBatchOperationChannelServiceImpl extends ServiceImpl<CouponBatchOperationChannelMapper, CouponBatchOperationChannel> implements ICouponBatchOperationChannelService {
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ICouponBatchOperationChannelService couponBatchOperationChannelService;
    @Resource
    private CouponBatchOperationChannelMapper couponBatchOperationChannelMapper;

    /**
     * 保存或更新
     * @param couponBatchList
     * @param couponBatchOperationChannelList
     * @param couponBatchOperationList
     * @return
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchOperationChannelDto> couponBatchOperationChannelList, List<CouponBatchOperationDto> couponBatchOperationList) {
        if(CollectionUtils.isEmpty(couponBatchOperationChannelList)) {
            return true;
        }

        // 不是投放导流 则删除所有
        Boolean deleteAll = false;
        if (CollectionUtil.isNotEmpty(couponBatchOperationList)){
            Integer sceneType = couponBatchOperationList.get(0).getSceneType();
            if (!CouponSceneTypeEnum.PUT_DIVERSION.getCode().equals(sceneType)){
                deleteAll = true;
            }
        }

        LambdaQueryWrapper<CouponBatchOperationChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchOperationChannel::getCouponBatchId, couponBatchList.getId());
        List<CouponBatchOperationChannel> oldCouponBatchOperationChannels = couponBatchOperationChannelMapper.selectList(lambdaQueryWrapper);
        Map<String, CouponBatchOperationChannel> batchRefidMap = oldCouponBatchOperationChannels.stream().collect(Collectors.toMap(CouponBatchOperationChannel::getRefid, Function.identity(), (a, b) -> a));

        List<Long> validIdList = new ArrayList<>();
        if(!deleteAll){
            for (CouponBatchOperationChannelDto couponBatchOperationChannelDto : couponBatchOperationChannelList) {
                CouponBatchOperationChannel couponBatchOperationChannel = BeanUtil.copyProperties(couponBatchOperationChannelDto, CouponBatchOperationChannel.class);
                couponBatchOperationChannel.setCouponBatchId(couponBatchList.getId());
                couponBatchOperationChannel.setModifiedTime(LocalDateTime.now());
                couponBatchOperationChannel.setModifier(adminTokenUtils.getModifier());

                // 根据refId匹配
                CouponBatchOperationChannel oldCouponBatchOperationChannel = batchRefidMap.get(couponBatchOperationChannel.getRefid());

                // 新增
                if (oldCouponBatchOperationChannel == null) {
                    couponBatchOperationChannel.setCreator(adminTokenUtils.getModifier());
                    couponBatchOperationChannel.setCreateTime(LocalDateTime.now());
                    couponBatchOperationChannel.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                    couponBatchOperationChannelMapper.insert(couponBatchOperationChannel);
                } else {
                    couponBatchOperationChannel.setId(oldCouponBatchOperationChannel.getId());
                    validIdList.add(oldCouponBatchOperationChannel.getId());
                    couponBatchOperationChannelMapper.updateById(couponBatchOperationChannel);
                }
            }
        }

        // 删除无效数据
        List<Long> invalidIdList = oldCouponBatchOperationChannels.stream().map(CouponBatchOperationChannel::getId).filter(id -> !validIdList.contains(id)).collect(Collectors.toList());
        couponBatchOperationChannelService.deleteByIds(invalidIdList);

        return true;
    }

    /**
     * 根据红包id查询
     * @param couponBatchIdList
     * @return
     */
    @Override
    public Map<Long, List<CouponBatchOperationChannelDto>> selectByCouponBatchId(List<Long> couponBatchIdList) {
        if (CollectionUtil.isEmpty(couponBatchIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchOperationChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchOperationChannel::getCouponBatchId, couponBatchIdList);
        List<CouponBatchOperationChannel> couponBatchOperationChannels = couponBatchOperationChannelMapper.selectList(lambdaQueryWrapper);
        List<CouponBatchOperationChannelDto> couponBatchOperationChannelDtoList = BeanUtil.copyToList(couponBatchOperationChannels, CouponBatchOperationChannelDto.class);
        return couponBatchOperationChannelDtoList.stream().collect(Collectors.groupingBy(CouponBatchOperationChannelDto::getCouponBatchId));
    }

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    @Override
    public Boolean deleteByIds(List<Long> invalidIdList) {
        if (CollectionUtil.isEmpty(invalidIdList)){
            return true;
        }
        return couponBatchOperationChannelMapper.deleteByIds(invalidIdList, adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo());
    }

}
