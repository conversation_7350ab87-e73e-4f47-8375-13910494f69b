package com.ly.localactivity.marketing.domain.marketing.vo;

import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanSingleEditVo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */

@ApiModel(value = "OperationPromotionPlanSingleEditVo", description = "运营推广方案单个编辑Vo(同编辑Ro)")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperationPromotionPlanSingleEditVo {
    @ApiModelProperty(value = "运营点位id", required = true)
    private Long positionId;
    @ApiModelProperty(value = "运营点位Code", required = true)
    private String positionCode;
    @ApiModelProperty(value = "平台", required = true)
    private String platform;
    @ApiModelProperty(value = "推广方式", required = true)
    private Integer promoteType;
    @ApiModelProperty(value = "输出形式", required = true)
    private Integer outputType;
    @ApiModelProperty(value = "推广地区ids(按照推广地区分开维护则为一条数据)", required = true)
    private List<Long> regionIds;
    @ApiModelProperty(value = "id", required = true)
    private Long id;
    @ApiModelProperty(value = "推广计划名称", required = true)
    private String name;
    @ApiModelProperty(value = "自定义名称")
    private String alias;

    @ApiModelProperty(value = "是否占位", required = true)
    private Boolean placeFlag;
    @ApiModelProperty(value = "有效期类型，1-长期有效，2-固定日期段", required = true)
    private Integer validDateType;
    @ApiModelProperty(value = "有效开始日期")
    private LocalDateTime validBeginDate;
    @ApiModelProperty(value = "有效结束日期")
    private LocalDateTime validEndDate;
    @ApiModelProperty(value = "顺位", required = true)
    private Integer indexValue;
    @ApiModelProperty(value = "是否置顶/锁定", required = true)
    private Boolean topFlag;
    @ApiModelProperty(value = "是否兜底", required = true)
    private Boolean defaultFlag;
    @ApiModelProperty(value = "默认展示展示方式，1-最后顺位，2-兜底展示", required = true)
    private Integer defaultShowType;
    @ApiModelProperty(value = "推广原因 数据字典:promotion_reason", required = true)
    private String reasonType;
    @ApiModelProperty(value = "推广原因备注")
    private String reasonRemark;
    @ApiModelProperty(value = "有效标记", required = true)
    @NotNull(message = "有效标记不能为空")
    private Boolean validFlag;
    @ApiModelProperty(value = "图片地址", required = true)
    @NotBlank(message = "图片地址不能为空")
    private String imageUrl;
    @ApiModelProperty(value = "图片名称", required = true)
    private String imageName;
    @ApiModelProperty(value = "图片上传信息", required = true)
    private String imageInfo;
    @ApiModelProperty(value = "关键字(推广标题)")
    private String keyword;
    @ApiModelProperty(value = "链接")
    private List<OperationPromotionPlanLinkDto> links;
    @ApiModelProperty(value = "是否指定渠道")
    private Boolean designatedChannels;
    @ApiModelProperty(value = "渠道列表")
    private List<OperationPromotionPlanChannelVo> channels;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "自定义排序")
    private Integer customSort;

}
