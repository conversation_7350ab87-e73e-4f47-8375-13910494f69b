package com.ly.localactivity.marketing.mapper.marketing;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanListForApiDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanPageDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.ImageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanPageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.PeriodVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运营推广计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface OperationPromotionPlanMapper extends BaseMapper<OperationPromotionPlan> {

    /**
     * 推广计划分页查询
     *
     * @param page  第页
     * @param model 型号
     * @return {@link List}<{@link OperationPromotionPlanPageVo}>
     */
    IPage<OperationPromotionPlanPageVo> listPage(@Param("page") Page<OperationPromotionPlanPageVo> page,
                                                 @Param("model") OperationPromotionPlanPageDto model);

    /**
     * 获取已经锁定的顺位
     *
     * @param query 查询参数
     * @return {@link List}<{@link String}>
     */
    List<String> listLockedIndex(@Param("query") OperationPromotionListLockedIndexRo query);

    /**
     * 获取已经占位的顺位
     *
     * @param query 查询参数
     * @return {@link List}<{@link String}>
     */
    List<String> listPlaceholderIndex(@Param("query") OperationPromotionListPlaceholderIndexRo query);

    /**
     * 获取已经维护的顺位
     *
     * @param query 查询参数
     * @return {@link List}<{@link String}>
     */
    List<String> listMaintainIndex(@Param("query") OperationPromotionListMaintainIndexRo query);

    /**
     * 获取不可用日期
     *
     * @param query 查询参数
     * @return {@link List}<{@link PeriodVo}>
     */
    List<PeriodVo> listUnavailableDate(@Param("query") OperationPromotionPlanUnavailableDateRo query);

    /**
     * api接口查询推广计划id
     *
     * @param model 查询对象
     * @return {@link List}<{@link Long}>
     */
    List<Long> listIdsForApi(@Param("model") OperationPromotionPlanListForApiDto model);


    /**
     * api接口查询推广计划id
     *
     * @param model 查询对象
     * @return {@link List}<{@link Long}>
     */
    List<OperationPromotionPlan> listForApi(@Param("model") OperationPromotionPlanListForApiDto model);

    /**
     * 图片数量
     *
     * @param imageName  图像名称
     * @param positionId 位置id
     * @param id         id
     * @return int
     */
    int imageCount(@Param("id") Long id, @Param("platform") String platform, @Param("positionId") Long positionId, @Param("imageName") String imageName);

    /**
     * 列表图像
     *
     * @param positionId 位置id
     * @return {@link List}<{@link ImageVo}>
     */
    List<ImageVo> listImageByPositionId(@Param("positionId") Long positionId);

    /**
     * 存在时间交叉的数量
     *
     * @param editRo 编辑ro
     */
    Integer countTimeCross(OperationPromotionPlanSingleEditRo editRo);

    /**
     * 推广计划覆盖情况
     *
     * @param request 请求
     * @return {@link List}<{@link OperationPromotionPlan}>
     */
    List<OperationPromotionPlan> listPlanCoverCondition(OperationPromotionPlanCoverConditionRo request);

    /**
     * 计数
     *
     * @param positionId 位置id
     */
    int count(@Param("positionId") Long positionId);

}
