package com.ly.localactivity.marketing.common.utils;

import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.List;

/**
 * MapUtils
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
public class MapUtils {
    private static final double R = 6371;


    /**
     * 计算两个经纬度之间的距离
     *
     * @param sourceLon 源lon
     * @param sourceLat 源lat
     * @param targetLon 目标lon
     * @param targetLat 目标lat
     * @return double 单位km
     */
    public static double getDistance(double sourceLon, double sourceLat, double targetLon, double targetLat) {
        double latDistance = Math.toRadians(targetLat - sourceLat);
        double lonDistance = Math.toRadians(targetLon - sourceLon);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(sourceLat)) * Math.cos(Math.toRadians(targetLat))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }


    /**
     * 判断坐标点是否在多边形区域内
     *
     * @param pointLon 要判断的点的经度
     * @param pointLat 要判断的点的纬度
     * @param lon      区域各顶点的经度数组
     * @param lat      区域各顶点的纬度数组
     * @return true：范围内; false：范围外
     */
    public static boolean isInPolygon(double pointLon, double pointLat, double[] lon, double[] lat) {
        // 将要判断的横纵坐标组成一个点
        Point2D.Double point = new Point2D.Double(pointLon, pointLat);
        // 将区域各顶点的横纵坐标放到一个点集合里面
        List<Point2D.Double> pointList = new ArrayList<>();
        double polygonPointToX;
        double polygonPointToY;
        for (int i = 0; i < lon.length; i++) {
            polygonPointToX = lon[i];
            polygonPointToY = lat[i];
            Point2D.Double polygonPoint = new Point2D.Double(polygonPointToX, polygonPointToY);
            pointList.add(polygonPoint);
        }
        return check(point, pointList);
    }

    /**
     * 坐标点是否在多边形内
     *
     * @param point   要判断的点的横纵坐标
     * @param polygon 组成的顶点坐标集合
     */
    private static boolean check(Point2D.Double point, List<Point2D.Double> polygon) {
        GeneralPath generalPath = new GeneralPath();

        Point2D.Double first = polygon.get(0);
        // 通过移动到指定坐标（以双精度指定），将一个点添加到路径中
        generalPath.moveTo(first.x, first.y);
        polygon.remove(0);
        for (Point2D.Double d : polygon) {
            // 通过绘制一条从当前坐标到新指定坐标（以双精度指定）的直线，将一个点添加到路径中。
            generalPath.lineTo(d.x, d.y);
        }
        // 将几何多边形封闭
        generalPath.lineTo(first.x, first.y);
        generalPath.closePath();
        // 测试指定的 Point2D 是否在 Shape 的边界内。
        return generalPath.contains(point);
    }

    /**
     * 判断坐标点是否在圆形区域内
     * 计算这个坐标点和圆心点之间的距离，然后跟圆的半径进行比较，如果比半径大，就不在圆形区域内，如果小于等于圆的半径，则该坐标点在圆形区域内
     *
     * @param longitude1 第一点的经度
     * @param latitude1  第一点的纬度
     * @param longitude2 第二点的经度
     * @param latitude2  第二点的纬度
     * @param radius     圆形范围半径（单位：米）
     * @return true：不在区域内; false:在区域内
     */
    public static boolean isInCircle(double longitude1, double latitude1, double longitude2, double latitude2, String radius) {
        if (StringUtils.isBlank(radius)) {
            throw new RuntimeException("请输入范围半径");
        }
        return getDistance(longitude1, latitude1, longitude2, latitude2) > Double.parseDouble(radius);
    }
}
