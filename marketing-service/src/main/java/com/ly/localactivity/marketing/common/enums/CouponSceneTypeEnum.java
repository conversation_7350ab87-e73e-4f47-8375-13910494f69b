package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡劵类型 0-满减，1-折扣
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponSceneTypeEnum {

    CROSS_CONNECTION(1, "交叉发放"),

    ACTIVITY_THEME(2, "活动主题"),

    IN_STATION_MARKET(3, "站内营销"),

    PUT_DIVERSION(4, "投放&导流"),
    ;
    private final Integer code;
    private final String desc;
}
