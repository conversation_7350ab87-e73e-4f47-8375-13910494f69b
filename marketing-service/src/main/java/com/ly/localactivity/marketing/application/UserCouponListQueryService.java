package com.ly.localactivity.marketing.application;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.model.dto.CouponBatchQueryDto;
import com.ly.localactivity.marketing.application.model.dto.CouponQueryDto;
import com.ly.localactivity.marketing.application.model.enums.CouponSourceEnum;
import com.ly.localactivity.marketing.application.model.ro.CouponLimitRo;
import com.ly.localactivity.marketing.application.model.vo.CouponVo;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.constants.RedisConstants;
import com.ly.localactivity.marketing.common.enums.coupon.CouponExtStatusEnum;
import com.ly.localactivity.marketing.common.utils.CollectionUtils;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.domain.third.member.MemberInfoEntity;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoDetailExtDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.CouponListRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponOrderListRequest;
import com.ly.localactivity.marketing.external.coupon.service.CouponCoreService;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponSearchService;
import com.ly.localactivity.marketing.service.third.IMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询用户已领优惠券列表
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Slf4j
@Service
public class UserCouponListQueryService {
    @Resource
    private CouponCommonService couponCommonService;
    @Resource
    private ICouponSearchService couponSearchService;
    @Resource
    private CouponCoreService couponCoreService;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private IMemberService memberService;
    @Resource
    private RedisUtils redisUtils;

    // 调用鲲鹏接口的线程 线程需求最大，不需要队列
    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2,  100, TimeUnit.SECONDS, new SynchronousQueue<>(), new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("http-coupon-list-thread");
            return thread;
        }
    }, new ThreadPoolExecutor.CallerRunsPolicy());

    @PostConstruct
    public void init(){
        Runtime.getRuntime().addShutdownHook(new Thread(THREAD_POOL::shutdown));
    }
    /**
     * 查询用户优惠券列表
     *
     * @param queryDto        查询dto
     * @param takedCouponList
     * @return {@link List}<{@link CouponVo}>
     */
    public List<CouponVo> queryUserCoupon(CouponQueryDto queryDto, boolean queryLimitData, List<CouponEsIndexDto> takedCouponList) {
        SkyLogUtils.infoByMethod(log, "CouponAppQueryService=>query:" + JSON.toJSONString(queryDto), "", queryDto.getQueryRo().getRequestId());
        CouponListRequest listRequest = new CouponListRequest();
        couponCommonService.getExternalRequest(queryDto.getQueryRo(), listRequest);
        List<CouponEsIndexDto> batchEsData = new ArrayList<>();
        //传了批次号，就查询批次下的券，没有传批次号
        listRequest.setRequestId(queryDto.getQueryRo().getRequestId());

        // 查询es并且传了批次号或者限制条件则先查询es
        List<String> validBatchNoList = new ArrayList<>();
        if (queryLimitData && (CollectionUtil.isNotEmpty(queryDto.getBatchNos()) || queryDto.getLimitData() != null)){
            List<String> queryValidBatchNoList = queryHasReceivedEsBatchNos(queryDto, batchEsData, takedCouponList);
            if (CollectionUtil.isEmpty(queryValidBatchNoList)){
                return new ArrayList<>();
            }
            validBatchNoList.addAll(queryValidBatchNoList);
            listRequest.setBatchNoList(validBatchNoList);
        }

        if (queryDto.getStatus() != null) {
            listRequest.setStatus(queryDto.getStatus().getCode());
        } else {
            listRequest.setStatus(CouponExtStatusEnum.All.getCode());
        }
        // 红包批次过多则查询所有已领红包
        String thresholdConfig = ConfigUtils.get(ConfigConst.COUPON_LIST_API_AUTO_CHANGE_THRESHOLD);
        int threshold = 20;
        if(StringUtils.isNotEmpty(thresholdConfig)){
            threshold = Integer.parseInt(thresholdConfig);
        }

        if (listRequest.getBatchNoList() != null && listRequest.getBatchNoList().size() > threshold) {
            SkyLogUtils.infoByMethod(log, StringUtils.format("CouponAppQueryService=>满足条件红包总数:{}",  listRequest.getBatchNoList().size()), "", queryDto.getQueryRo().getRequestId());
            listRequest.setBatchNoList(null);
        }

//        SkyLogUtils.infoByMethod(log, "CouponAppQueryService=>开始查询红包列表接口:" + JSON.toJSONString(listRequest), "", queryDto.getQueryRo().getRequestId());
        listRequest.setTimeOut(queryDto.getTimeOut());
        long start = System.currentTimeMillis();

        List<CouponInfoDetailExtDto> couponInfoDetailExtDtoList = new CopyOnWriteArrayList<>();

        // 但状态查询使用orderlist接口
        if(CouponExtStatusEnum.All.getCode().equals(listRequest.getStatus())) {
            String useOrderApi = ConfigUtils.get(ConfigConst.COUPON_USER_USE_ORDER_API);
            if ("asyncOrderApi".equals(useOrderApi)){
                queryAllUserCouponList(listRequest, validBatchNoList, couponInfoDetailExtDtoList);
            }else if("syncOrderApi".equals(useOrderApi)) {
                List<Integer> statusList = new ArrayList<>();
                statusList.add(CouponExtStatusEnum.Unused.getCode());
                statusList.add(CouponExtStatusEnum.Occupy.getCode());
                statusList.add(CouponExtStatusEnum.Used.getCode());
                statusList.add(CouponExtStatusEnum.Expired.getCode());
                queryUserCouponListWithStatus(listRequest, validBatchNoList, couponInfoDetailExtDtoList, statusList);
            }else if ("newListApi".equals(useOrderApi)){
                queryAllPlatformUserCouponList(listRequest, couponInfoDetailExtDtoList);
            }else {
                if ("mixQuery".equals(useOrderApi) && CollectionUtil.isEmpty(validBatchNoList)){
                    queryAllUserCouponList(listRequest, validBatchNoList, couponInfoDetailExtDtoList);
                }else {
                    CouponCommonResult<List<CouponInfoDetailExtDto>> couponListResult = couponCoreService.getCouponList(listRequest);
                    if (couponListResult == null) {
                        return new ArrayList<>();
                    }
                    couponInfoDetailExtDtoList = couponListResult.getResult();
                }
            }
        }else {
            querySingleStatusUserCouponList(listRequest, validBatchNoList, couponInfoDetailExtDtoList);
        }

        SkyLogUtils.infoByMethod(log, StringUtils.format("CouponAppQueryService=>CouponList cost:{}，已领取过的红包总数:{}", (System.currentTimeMillis() - start), CollectionUtils.isEmpty(couponInfoDetailExtDtoList) ? 0 : couponInfoDetailExtDtoList.size()), "", queryDto.getQueryRo().getRequestId());
//        start = System.currentTimeMillis();
        List<CouponVo> couponVos = convertCouponVoWithEs(couponInfoDetailExtDtoList, false, batchEsData);;
//        List<CouponVo> couponVos = convertCouponVoWithEs(couponListResult.getResult(), queryDto.isSimpleMode(), batchEsData);
        SkyLogUtils.infoByMethod(log, StringUtils.format("CouponAppQueryService=>convertCouponVo res:{}，couponInfoDetailExtDtoList:{},batchEsData:{}", couponVos, couponInfoDetailExtDtoList, batchEsData), "", queryDto.getQueryRo().getRequestId());
        //按领取时间排序
        couponVos.sort(Comparator.comparing(CouponVo::getReceiveTime).reversed());
        return couponVos;
    }

    private void querySingleStatusUserCouponList(CouponListRequest listRequest, List<String> validBatchNoList, List<CouponInfoDetailExtDto> couponInfoDetailExtDtoList) {
        CouponOrderListRequest couponOrderListRequest = BeanUtil.copyProperties(listRequest, CouponOrderListRequest.class);
        couponOrderListRequest.setStatus(listRequest.getStatus());
        log.info(MessageFormat.format("querySingleStatusUserCouponList: couponOrderListRequest:{0}", JSON.toJSONString(couponOrderListRequest)));

        CouponCommonResult<List<CouponInfoDetailExtDto>> couponListResult = couponCoreService.getCouponOrderList_ConnectPool(couponOrderListRequest);
        List<CouponInfoDetailExtDto> userCouponList = new ArrayList<>();
        if (couponListResult != null && CollectionUtil.isNotEmpty(couponListResult.getResult())) {
            userCouponList = couponListResult.getResult();
            if (CollectionUtil.isNotEmpty(validBatchNoList)) {
                userCouponList = userCouponList.stream().filter(couponInfoDetailExtDto -> validBatchNoList.contains(couponInfoDetailExtDto.getBatchNo())).collect(Collectors.toList());
            }
        }
        log.info(MessageFormat.format("querySingleStatusUserCouponList: userCouponList:{0}", JSON.toJSONString(userCouponList)));

        couponInfoDetailExtDtoList.addAll(userCouponList);
    }

    private void queryAllPlatformUserCouponList(CouponListRequest listRequest, List<CouponInfoDetailExtDto> couponInfoDetailExtDtoList) {
        String redisKey = RedisConstants.COUPON_ALL_MEMBER_INFO_PREFIX + listRequest.getUserkey();
        String cacheJson = redisUtils.get(redisKey);
        List<String> userKeyList = new ArrayList<>();
        if (StringUtils.isEmpty(cacheJson)){
            List<MemberInfoEntity> allMemberInfoList = null;
            if (CouponSourceEnum.UnionId.getCode().equals(listRequest.getSource())) {
                allMemberInfoList = memberService.getAllMemberInfoList(listRequest.getUserkey(), null);
            } else if (CouponSourceEnum.MemberId.getCode().equals(listRequest.getSource())){
                allMemberInfoList = memberService.getAllMemberInfoList(null, Long.parseLong(listRequest.getUserkey()));
            }
            // 查询到会员信息
            log.info(MessageFormat.format("queryAllPlatformUserCouponList查询到会员信息: userKey:{0}, allMemberInfoList:{1}", listRequest.getUserkey(), allMemberInfoList));
            if (CollectionUtil.isNotEmpty(allMemberInfoList)){
                Set<String> userKeySet = new HashSet<>();
                for (MemberInfoEntity memberInfoEntity : allMemberInfoList) {
                    if (StringUtils.isNotEmpty(memberInfoEntity.getUnionId())){
                        userKeySet.add(memberInfoEntity.getUnionId());
                    }
                    if (memberInfoEntity.getMemberId() != null && memberInfoEntity.getMemberId() != 0L){
                        userKeySet.add(String.valueOf(memberInfoEntity.getMemberId()));
                    }
                }
                userKeyList.addAll(userKeySet);
            }

            log.info(MessageFormat.format("queryAllPlatformUserCouponList查询到去重会员信息: userKey:{0}, userKeyList:{1}", listRequest.getUserkey(), userKeyList));
        }else {
            userKeyList = JSON.parseArray(cacheJson, String.class);
            log.info(MessageFormat.format("queryAllPlatformUserCouponList查询到缓存会员信息: userKey:{0}, userKeyList:{1}", listRequest.getUserkey(), userKeyList));
        }
        // 缓存2分钟 重复刷新
        redisUtils.setex(redisKey, JSON.toJSONString(userKeyList), 2 * 60L + RandomUtil.randomInt(20));

        if (CollectionUtil.isEmpty(userKeyList) || userKeyList.size() == 1){
            CouponCommonResult<List<CouponInfoDetailExtDto>> couponListResult = couponCoreService.getCouponList(listRequest);
            if (couponListResult != null && CollectionUtil.isNotEmpty(couponListResult.getResult())) {
                couponInfoDetailExtDtoList.addAll(couponListResult.getResult());
            }
            return;
        }

        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (String userKey : userKeyList) {
            CouponListRequest curListRequest = BeanUtil.copyProperties(listRequest, CouponListRequest.class);
            curListRequest.setOpenId(null);
            curListRequest.setIsDefault(1);
            curListRequest.setUserkey(userKey);
            if (StringUtils.isNumeric(userKey)){
                curListRequest.setSource(CouponSourceEnum.MemberId.getCode());
            }else {
                curListRequest.setSource(CouponSourceEnum.UnionId.getCode());
            }
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                CouponCommonResult<List<CouponInfoDetailExtDto>> couponListResult = couponCoreService.getCouponList(curListRequest);
                if (couponListResult != null && CollectionUtil.isNotEmpty(couponListResult.getResult())) {
                    couponInfoDetailExtDtoList.addAll(couponListResult.getResult());
                }
            }, THREAD_POOL);
            completableFutureList.add(completableFuture);
        }

        CompletableFuture.allOf(completableFutureList.stream().toArray(CompletableFuture[]::new)).join();
    }

    private void queryUserCouponListWithStatus(CouponListRequest listRequest, List<String> validBatchNoList, List<CouponInfoDetailExtDto> couponInfoDetailExtDtoList, List<Integer> statusList) {
        if (CollectionUtil.isEmpty(statusList)){
            return;
        }
        CouponOrderListRequest couponOrderListRequest = BeanUtil.copyProperties(listRequest, CouponOrderListRequest.class);
        for (Integer status : statusList) {
            couponOrderListRequest.setStatus(status);
            String version = ConfigUtils.get(ConfigConst.COUPON_API_VERSION);
            CouponCommonResult<List<CouponInfoDetailExtDto>> couponListResult;
            if (StringUtils.isNotBlank(version) && "2".equals(version)) {
                couponListResult = couponCoreService.getCouponOrderList_ConnectPool(couponOrderListRequest);
            } else {
                couponListResult = couponCoreService.getCouponOrderList(couponOrderListRequest);
            }
            List<CouponInfoDetailExtDto> userCouponList = new ArrayList<>();
            if (couponListResult != null && CollectionUtil.isNotEmpty(couponListResult.getResult())){
                userCouponList = couponListResult.getResult();
                if(CollectionUtil.isNotEmpty(validBatchNoList)){
                    userCouponList = userCouponList.stream().filter(couponInfoDetailExtDto -> validBatchNoList.contains(couponInfoDetailExtDto.getBatchNo())).collect(Collectors.toList());
                }
            }
            couponInfoDetailExtDtoList.addAll(userCouponList);
        }
    }

    private void queryAllUserCouponList(CouponListRequest listRequest, List<String> validBatchNoList, List<CouponInfoDetailExtDto> couponInfoDetailExtDtoList) {
        // 查询未使用、已占用、已使用和已过期的红包
        List<Integer> allStatusList = Arrays.asList(CouponExtStatusEnum.Unused.getCode(), CouponExtStatusEnum.Occupy.getCode(), CouponExtStatusEnum.Used.getCode(), CouponExtStatusEnum.Expired.getCode());

        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (Integer status : allStatusList) {
            CouponOrderListRequest couponOrderListRequest = BeanUtil.copyProperties(listRequest, CouponOrderListRequest.class);
            couponOrderListRequest.setStatus(status);
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                log.info(MessageFormat.format("getCouponOrderList_ConnectPool: couponOrderListRequest:{0}", JSON.toJSONString(couponOrderListRequest)));
                CouponCommonResult<List<CouponInfoDetailExtDto>> usedCouponListResult = couponCoreService.getCouponOrderList_ConnectPool(couponOrderListRequest);
                List<CouponInfoDetailExtDto> usedCouponList = new ArrayList<>();
                if (usedCouponListResult != null && CollectionUtil.isNotEmpty(usedCouponListResult.getResult())){
                    usedCouponList = usedCouponListResult.getResult();
                    if(CollectionUtil.isNotEmpty(validBatchNoList)){
                        usedCouponList = usedCouponList.stream().filter(couponInfoDetailExtDto -> validBatchNoList.contains(couponInfoDetailExtDto.getBatchNo())).collect(Collectors.toList());
                    }
                    couponInfoDetailExtDtoList.addAll(usedCouponList);
                    log.info(MessageFormat.format("getCouponOrderList_ConnectPool: usedCouponList:{0}", JSON.toJSONString(usedCouponList)));

                }
            }, THREAD_POOL);
            completableFutureList.add(completableFuture);
        }

        CompletableFuture.allOf(completableFutureList.stream().toArray(CompletableFuture[]::new)).join();
    }

    public List<String> queryHasReceivedEsBatchNos(CouponQueryDto queryDto, List<CouponEsIndexDto> batchEsData, List<CouponEsIndexDto> takedCouponList) {
        List<CouponEsIndexDto> couponEsDtoList = new ArrayList<>();
        List<String> batchNos = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(takedCouponList)){
            couponEsDtoList = takedCouponList;
        }else {
            if (CollectionUtil.isNotEmpty(queryDto.getBatchNos())) {
                //如果传了批次号，就用传的批次号
                batchNos = queryDto.getBatchNos();
            } else if (queryDto.getLimitData() != null) {
                //如果没有传批次号，就用限制条件查询
                //不需要过滤日期和状态
//            long start = System.currentTimeMillis();
                if (!queryDto.isFilterPositionCode()) {
                    couponEsDtoList = searchCouponBatchs(queryDto.getQueryRo().getRequestId()
                            , queryDto.getQueryRo().getPlatform()
                            , ""
                            , ""
                            , queryDto.getLimitData()
                            , false);
                } else {
                    couponEsDtoList = searchCouponBatchs(queryDto.getQueryRo().getRequestId()
                            , queryDto.getQueryRo().getPlatform()
                            , queryDto.getQueryRo().getRefid()
                            , queryDto.getPositionCode()
                            , queryDto.getLimitData()
                            , false);
                }

//            SkyLogUtils.infoByMethod(log, StringUtils.format("CouponAppQueryService=>searchCouponBatchs cost:{}::{}", (System.currentTimeMillis() - start), queryDto.isFilterPositionCode()), "", queryDto.getQueryRo().getRequestId());

            } else {
//            long start = System.currentTimeMillis();
                //如果没有传限制条件，就只返回该平台下所有红包，搜索接口调用 todo 没传限制条件则后查es
//            couponEsDtoList = couponSearchService.searchEsBatchListByPlatform(queryDto.getQueryRo().getRequestId()
//                    , queryDto.getQueryRo().getPlatform().toString());
//            SkyLogUtils.infoByMethod(log, StringUtils.format("CouponAppQueryService=>searchEsBatchListByPlatform cost:{}", (System.currentTimeMillis() - start)), "", queryDto.getQueryRo().getRequestId());
            }
        }

        if (CollectionUtils.isNotEmpty(couponEsDtoList)){
            batchEsData.addAll(couponEsDtoList);
        }

        if (CollectionUtils.isNotEmpty(batchEsData)) {
            batchNos = batchEsData.stream().map(CouponEsIndexDto::getBatchNo).distinct().collect(Collectors.toList());
        }

        return batchNos;
    }

    private List<CouponEsIndexDto> searchCouponBatchs(String requestId, Integer platform, String refid, String positionCode, CouponLimitRo limitRo, boolean filterAvaliabled) {
        CouponBatchQueryDto queryDto = CouponBatchQueryDto.builder()
                .requestId(requestId)
                .platform(platform.toString())
                .positionCode(positionCode)
                .refid(refid)
                .limitData(limitRo)
                .filterAvaliabled(filterAvaliabled)
                .build();

        return couponSearchService.searchEsBatchList(queryDto);
    }

    public List<CouponVo> convertCouponVoWithRedis(List<CouponInfoDetailExtDto> couponInfoDetailDtoList, boolean simpleMode) {
        List<CouponVo> couponVoList = new CopyOnWriteArrayList<>();
        if (CollectionUtil.isEmpty(couponInfoDetailDtoList)) {
            return couponVoList;
        }


        couponInfoDetailDtoList.forEach(s -> {
            if (s == null) {
                return;
            }
//            CouponEsIndexDto indexDto = null;
//            if (CollectionUtil.isNotEmpty(finalBatchEsData)) {
//                Optional<CouponEsIndexDto> first = finalBatchEsData.parallelStream().filter(m -> m.getBatchNo().equals(s.getBatchNo())).findFirst();
//                if (first.isPresent()) {
//                    indexDto = first.get();
//                }
//            }

            // 依赖缓存
            CouponBatchCacheDto batchDetail = couponQueryService.getBatchDetail(s.getBatchNo(), false);
            CouponVo couponVo = CouponVo.from(s, batchDetail, simpleMode);
            couponVoList.add(couponVo);
        });
        return couponVoList;
    }

    public List<CouponVo> convertCouponVoWithEs(List<CouponInfoDetailExtDto> couponInfoDetailDtoList, boolean simpleMode, List<CouponEsIndexDto> batchEsData) {
        List<CouponVo> couponVoList = new CopyOnWriteArrayList<>();
        if (CollectionUtil.isEmpty(couponInfoDetailDtoList)) {
            return couponVoList;
        }
        if (CollectionUtils.isEmpty(batchEsData)) {
            //如果外面没有传入批次信息对象，就去查询ES
            batchEsData = couponSearchService.searchEsByBatchNos(couponInfoDetailDtoList.stream().map(CouponInfoDetailExtDto::getBatchNo).collect(Collectors.toList()));
        }
        if (CollectionUtil.isEmpty(batchEsData)){
            return couponVoList;
        }

        Map<String, CouponEsIndexDto> batchNoEsIndexMap = batchEsData.stream().collect(Collectors.toMap(CouponEsIndexDto::getBatchNo, Function.identity(), (a, b) -> a));

        couponInfoDetailDtoList.stream().filter(couponDetail -> couponDetail != null && batchNoEsIndexMap.containsKey(couponDetail.getBatchNo())).forEach(couponDetail -> {
            CouponEsIndexDto couponEsIndexDto = batchNoEsIndexMap.get(couponDetail.getBatchNo());
            CouponVo couponVo = CouponVo.from(couponDetail, couponEsIndexDto, simpleMode);
            couponVoList.add(couponVo);
        });
        return couponVoList;
    }
}
