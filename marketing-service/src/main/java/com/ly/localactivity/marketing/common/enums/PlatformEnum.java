package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * PlatformEnum
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Getter
@AllArgsConstructor
public enum PlatformEnum {
    /**
     * App(IOS)
     */
    App_IOS("434", "同程APP-IOS", "1207"),
    /**
     * App(Android)
     */
    App_Android("433", "同程APP-Android", "1207"),

    App_All("431", "鲲鹏-同程APP", "1207"),

    App_IOS_Android("1207", "同程APP", "1207"),
    /**
     * 微信小程序
     */
    Wx_App("852", "微信小程序", "852"),

    /**
     * 微信小程序H5
     */
    Wx_App_H5("501", "微信小程序/h5", "501"),

    ALL_PLATFORM("823", "不区分平台", "0")

    ;
    private String code;
    private String name;
    private String relationChannelCode;

    public static String codeToName(String code) {
        PlatformEnum reasonTypeEnum = codeOf(code);
        return Objects.isNull(reasonTypeEnum) ? null : reasonTypeEnum.getName();
    }
    public static PlatformEnum codeOf(String code) {
        if (Objects.isNull(code)){
            return null;
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }

    public static String getRelationCode(String code) {
        PlatformEnum reasonTypeEnum = codeOf(code);
        if (reasonTypeEnum != null) {
            return reasonTypeEnum.getRelationChannelCode();
        }
        return "";
    }
}
