package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.base.OperationLogDto;
import com.ly.localactivity.marketing.domain.marketing.SysOperationLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 操作日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface ISysOperationLogService extends IService<SysOperationLog> {

    /**
     * 保存日志
     *
     * @param logDto 日志dto
     */
    void saveLog(OperationLogDto logDto);
}
