package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * MarketAreaEnum
 * 地理区域划分
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Getter
@AllArgsConstructor
public enum MarketAreaEnum {
    /**
     * 华东,7
     */
    EAST(7, "华东"),
    /**
     * 华南
     */
    SOUTH(8, "华南"),
    /**
     * 华北
     */
    NORTH(9, "华北"),
    /**
     * 华中
     */
    CENTRAL(10, "华中"),
    /**
     * 西南

     */
    SOUTH_WEST(11, "西南"),
    /**
     * 东北
     */
    NORTH_EAST(12, "东北"),
    /**
     * 西北
     */
    NORTH_WEST(13, "西北")

    ;
    private Integer code;
    private String name;
    public static String codeToName(Integer code) {
        MarketAreaEnum reasonTypeEnum = codeOf(code);
        return Objects.isNull(reasonTypeEnum) ? null : reasonTypeEnum.getName();
    }
    public static MarketAreaEnum codeOf(Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
