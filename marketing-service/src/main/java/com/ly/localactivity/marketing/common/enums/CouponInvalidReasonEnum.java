package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponInvalidReasonEnum {
    TC_INVALID("同程运营下架"),
    SUPPLIER_INVALID("供应商自主下架"),
    KUN_PENG_INVALID("关联红包批次已无效"),
    NO_BATCH_DETAIL("未关联红包批次"),
    NO_RULE("未关联产品"),
    NOT_START("平台红包活动有效期还未开始"),
    ;
    private final String reason;
}
