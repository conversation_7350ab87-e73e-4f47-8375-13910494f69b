package com.ly.localactivity.marketing.mapper.resource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.localactivity.marketing.domain.resource.WRMainResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 玩乐主资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface WRMainResourceMapper extends BaseMapper<WRMainResource> {

    List<WRMainResource> getCanSaleProductByMainResourceSerialIds(List<String> mainResourceSerialIdList);

    List<WRMainResource> getCanSaleProductBySupplierId(@Param("supplierId") Long supplierId, @Param("firstCategoryId") Integer firstCategoryId);
}
