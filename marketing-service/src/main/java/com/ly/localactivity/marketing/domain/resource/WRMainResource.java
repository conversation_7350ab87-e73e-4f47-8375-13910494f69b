package com.ly.localactivity.marketing.domain.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 玩乐主资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WRMainResource")
public class WRMainResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "MRId", type = IdType.AUTO)
    private Long MRId;

    /**
     * 流水号
     */
    @TableField("MRSerialId")
    private String MRSerialId;

    /**
     * 一级品类
     */
    @TableField("MRFirstCategoryId")
    private Integer MRFirstCategoryId;

    /**
     * 二级品类
     */
    @TableField("MRSecondCategroyId")
    private Integer MRSecondCategroyId;

    /**
     * 标题
     */
    @TableField("MRTitle")
    private String MRTitle;

    /**
     * 子标题
     */
    @TableField("MRSubTitle")
    private String MRSubTitle;

    /**
     * 产品主图
     */
    @TableField("MRMainImage")
    private String MRMainImage;

    /**
     * 出发城市id(新系统废弃)
     */
    @TableField("MRDepartureCityId")
    private String MRDepartureCityId;

    /**
     * 出发城市名称(新系统废弃)
     */
    @TableField("MRDepartureCity")
    private String MRDepartureCity;

    /**
     * 出港城市id
     */
    @TableField("MRLeavePortCityId")
    private String MRLeavePortCityId;

    /**
     * 出港城市名称
     */
    @TableField("MRLeavePortCity")
    private String MRLeavePortCity;

    /**
     * 目的地城市id
     */
    @TableField("MRDestinationCityId")
    private String MRDestinationCityId;

    /**
     * 目的地城市名称
     */
    @TableField("MRDestinationCity")
    private String MRDestinationCity;

    /**
     * 审核状态(0：待申请，1：待审核，2：已审核，3：驳回)
     */
    @TableField("MRAuditStatus")
    private Integer MRAuditStatus;

    /**
     * 有效性
     */
    @TableField("MRDataFlag")
    private Integer MRDataFlag;

    /**
     * 是否删除（0：否，1：是）
     */
    @TableField("MRDelete")
    private Integer MRDelete;

    /**
     * 来源（0：同程自主，1：EB，2：选品）
     */
    @TableField("MRSource")
    private Integer MRSource;

    /**
     * 外部主资源id
     */
    @TableField("MROutMainResourceId")
    private String MROutMainResourceId;

    /**
     * 子资源数量
     */
    @TableField("MRSubResourceNum")
    private Integer MRSubResourceNum;

    /**
     * 创建人姓名
     */
    @TableField("MRCreateName")
    private String MRCreateName;

    /**
     * 创建人工号
     */
    @TableField("MRCreateJobNum")
    private String MRCreateJobNum;

    /**
     * 创建时间
     */
    @TableField("MRCreateDate")
    private LocalDateTime MRCreateDate;

    /**
     * 更新人姓名
     */
    @TableField("MRUpdateName")
    private String MRUpdateName;

    /**
     * 更新人工号
     */
    @TableField("MRUpdateJobNum")
    private String MRUpdateJobNum;

    /**
     * 更新时间
     */
    @TableField("MRUpdateDate")
    private LocalDateTime MRUpdateDate;

    /**
     * 目的区域Id
     */
    @TableField("MRRegionId")
    private Long MRRegionId;

    /**
     * 目的区域
     */
    @TableField("MRRegionName")
    private String MRRegionName;

    /**
     * 项目归属 （0 全球玩乐项目 1 国内游）
     */
    @TableField("MRProjectOwnership")
    private Integer MRProjectOwnership;

    /**
     * 供应商id
     */
    @TableField("MRSupplierId")
    private Long MRSupplierId;

    /**
     * 外部主资源名称
     */
    @TableField("MROutMainResourceName")
    private String MROutMainResourceName;

    /**
     * 供应商名称
     */
    @TableField("MRSupplierName")
    private String MRSupplierName;

    /**
     * 下架来源 1.商家2.同程
     */
    @TableField("MRInValidSource")
    private Integer MRInValidSource;

    /**
     * 下架原因
     */
    @TableField("MRInValidReason")
    private String MRInValidReason;

    /**
     * 审核时间
     */
    @TableField("MRAuditTime")
    private LocalDateTime MRAuditTime;

    /**
     * 上架时间
     */
    @TableField("MRValidTime")
    private LocalDateTime MRValidTime;

    /**
     *  下架时间
     */
    @TableField("MRInValidTime")
    private LocalDateTime MRInValidTime;


}
