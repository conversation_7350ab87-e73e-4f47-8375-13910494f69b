package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.domain.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * OperationPromotionLinkTemplatePageRo
 *
 * <AUTHOR>
 * @date 2024-4-9
 */
@Data
@ApiModel(value = "OperationPromotionLinkTemplatePageRo", description = "链接模板分页请求参数")
public class OperationPromotionLinkTemplatePageRo extends BasePageRequest {
    /**
     * 平台模式
     */
    @ApiModelProperty(value = "平台模式")
    @NotNull(message = "平台模式不能为空")
    private String platform;

    /**
     * 自定义名称
     */
    @ApiModelProperty(value = "自定义名称")
    private String customerName;
}
