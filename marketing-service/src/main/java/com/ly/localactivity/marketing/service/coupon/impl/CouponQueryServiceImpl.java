package com.ly.localactivity.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.mq.TurboMqUtils;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.enums.CouponStatusEnum;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchApiStatusEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchExtStatusEnum;
import com.ly.localactivity.marketing.common.utils.EsSearchUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.*;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchSyncDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchListExtDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.CouponBatchInfoRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponBatchListInfoRequest;
import com.ly.localactivity.marketing.external.coupon.service.CouponService;
import com.ly.localactivity.marketing.mapper.marketing.*;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 红包查询服务
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
@Slf4j
@Service
public class CouponQueryServiceImpl implements ICouponQueryService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private CouponBatchRuleDetailMapper ruleDetailMapper;
    @Resource
    private CouponService couponService;
    @Resource
    private CouponBatchRuleMapper ruleMapper;
    @Resource
    private CouponBatchDetailMapper couponBatchDetailMapper;
    @Resource
    private CouponBatchListMapper couponBatchListMapper;
    @Resource
    private CouponBatchOperationMapper couponBatchOperationMapper;
    @Resource
    private CouponBatchOperationChannelMapper couponBatchOperationChannelMapper;
    @Resource
    private CouponBatchPlatformMapper couponBatchPlatformMapper;
    @Resource
    private CouponBatchStockLimitMapper couponBatchStockLimitMapper;
    @Resource
    private CouponBatchValidDateMapper couponBatchValidDateMapper;
    @Resource
    private TurboMqUtils turboMqUtils;

    // 调用redis的线程 不要占用过多线程
    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(2, 4, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(4), new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("自定义redis查询线程");
            return thread;
        }
    }, new ThreadPoolExecutor.CallerRunsPolicy());

    @PostConstruct
    public void init(){
        Runtime.getRuntime().addShutdownHook(new Thread(THREAD_POOL::shutdown));
    }

    /**
     * 获取优惠券批次
     *
     * @param batchNo 批号
     * @return {@link CouponBatchCacheDto}
     */
    @Override
    public CouponBatchCacheDto getCouponBatch(String batchNo) {
        //查询红包批次明细，调用红包接口
        CouponBatchExtDto externalCoupon = getExternalCoupon(batchNo);
        if (externalCoupon == null) {
            return null;
        }

        //获取详细信息
        CouponBatchCacheDto cacheDto = getBatchDetail(batchNo, false);
        cacheDto.setBaseInfo(externalCoupon);
        return cacheDto;
    }

    /**
     * 获取优惠券批次
     *
     * @param batchNos 批号
     * @param useCache
     * @return {@link List}<{@link CouponBatchCacheDto}>
     */
    @Override
    public List<CouponBatchCacheDto> getCouponBatch(List<String> batchNos, boolean useCache) {
        if (CollectionUtil.isEmpty(batchNos)) {
            return null;
        }

        // 使用缓存 直接返回
        if (useCache){
            return getCouponBatchCache(batchNos);
        }

        //调用鲲鹏接口获取红包批次信息

        List<CouponBatchExtDto> batchListInfo = new ArrayList<>();
        CouponBatchListInfoRequest request = new CouponBatchListInfoRequest();
        request.setBatchNos(batchNos);
        CouponCommonResult<CouponBatchListExtDto> couponBatchListInfo = couponService.getCouponBatchListInfo(request);
        if (couponBatchListInfo != null && couponBatchListInfo.getResult() != null) {
            batchListInfo = couponBatchListInfo.getResult().getBatchList();
        }

        //获取本地红包详情
        List<CouponBatchCacheDto> cacheDtos = batchNos.stream().map(batchNo -> this.getBatchDetail(batchNo, false)).collect(Collectors.toList());

        //拼接数据
        if (CollectionUtil.isEmpty(cacheDtos)) {
            return cacheDtos;
        }
        if (CollectionUtil.isEmpty(batchListInfo)) {
            return cacheDtos;
        }
        List<CouponBatchExtDto> finalBatchListInfo = batchListInfo;
        cacheDtos.forEach(cacheDto -> {
            if (cacheDto == null) {
                return;
            }
            CouponBatchExtDto extDto = finalBatchListInfo.stream().filter(ext ->
                            StringUtils.isNotBlank(ext.getBatchNo()) && ext.getBatchNo().equals(cacheDto.getBatchNo()))
                    .findFirst().orElse(null);
            if (extDto != null) {
                cacheDto.setBaseInfo(extDto);
            }
        });
        return cacheDtos;
    }

    public List<CouponBatchCacheDto> getCouponBatchCache(List<String> batchNos) {
        if (CollectionUtil.isEmpty(batchNos)) {
            return null;
        }

        if (batchNos.size() <= 8){
            return batchNos.stream().map(batchNo -> this.getBatchDetail(batchNo, false)).collect(Collectors.toList());
        }

        long start = System.currentTimeMillis();
        List<CouponBatchCacheDto> couponBatchCacheDtoList = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (String batchNo : batchNos) {
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                CouponBatchCacheDto batchDetail = this.getBatchDetail(batchNo, false);
                if (batchDetail != null) {
                    couponBatchCacheDtoList.add(batchDetail);
                }
            }, THREAD_POOL);
            completableFutureList.add(completableFuture);
        }
        CompletableFuture.allOf(completableFutureList.stream().toArray(CompletableFuture[]::new)).join();

        SkyLogUtils.infoByMethod(log, "getCouponBatchCache->cost:" + (System.currentTimeMillis() - start), "", "");
        return couponBatchCacheDtoList;
    }

    /**
     * 获取红包批次详情，包括detail和list信息
     *
     * @param batchNo 批号
     * @return {@link CouponBatchCacheDto}
     */
    @Override
    public CouponBatchCacheDto getBatchDetail(String batchNo, Boolean ignoreCache) {
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(batchNo)) {
            return null;
        }
        String key = getBatchKey(batchNo);
        if (!Boolean.TRUE.equals(ignoreCache)){
            String cacheString = redisUtils.get(key);
            if (StringUtils.isNotBlank(cacheString)) {
                SkyLogUtils.infoByMethod(log, StringUtils.format("getBatchDetail-> cost cache:{}获取红包批次缓存:{}", (System.currentTimeMillis() - start), batchNo), batchNo, "");
                return JSON.parseObject(cacheString, CouponBatchCacheDto.class);
            }
        }

        CouponBatchCacheDto cacheDto = new CouponBatchCacheDto();
        cacheDto.setBatchNo(batchNo);

        //获取本地detail信息
        CouponBatchDetail couponBatchDetail = couponBatchDetailMapper.selectOne(
                new LambdaQueryWrapper<CouponBatchDetail>()
                        .eq(CouponBatchDetail::getBatchNo, cacheDto.getBatchNo())
                        .last("limit 1"));
        cacheDto.setDetailInfo(couponBatchDetail);
        if (couponBatchDetail == null) {
            return cacheDto;
        }

        //获取鲲鹏基础信息
        cacheDto.setBaseInfo(this.getExternalCoupon(batchNo));

        //查询本地红包使用规则
        List<CouponBatchRuleDetail> ruleDetails = ruleDetailMapper.listByCouponBatchNo(cacheDto.getBatchNo());
        if (CollectionUtil.isNotEmpty(ruleDetails)) {
            cacheDto.setLimitDataIds(ruleDetails.stream().map(CouponBatchRuleDetail::getDataId).collect(Collectors.toList()));
        }

        List<CouponBatchRule> batchRules = ruleMapper.listByBatchNo(cacheDto.getBatchNo());
        if (CollectionUtil.isNotEmpty(batchRules)) {
            CouponBatchRule couponBatchRule = batchRules.get(0);
            setCouponBatchRuleDefaultValue(couponBatchRule);
            cacheDto.setRule(couponBatchRule);
        }
        //获取本地list信息
        CouponBatchList couponBatchList = couponBatchListMapper.selectById(couponBatchDetail.getCouponBatchId());
        setCouponBatchListDefaultValue(couponBatchList);
        cacheDto.setListInfo(couponBatchList);
        // 获取点位信息
        List<CouponBatchOperation> couponBatchOperations = couponBatchOperationMapper.selectList(new LambdaQueryWrapper<CouponBatchOperation>()
                .eq(CouponBatchOperation::getCouponBatchId, couponBatchDetail.getCouponBatchId())
                .eq(CouponBatchOperation::getDeleteFlag, false));
        if (CollectionUtil.isNotEmpty(couponBatchOperations)) {
            cacheDto.setOperationCodes(couponBatchOperations.stream().map(CouponBatchOperation::getOperationPositionCode).collect(Collectors.toList()));
        }
        // refId信息
        List<CouponBatchOperationChannel> couponBatchOperationChannels = couponBatchOperationChannelMapper.selectList(new LambdaQueryWrapper<CouponBatchOperationChannel>()
                .eq(CouponBatchOperationChannel::getCouponBatchId, couponBatchDetail.getCouponBatchId())
                .eq(CouponBatchOperationChannel::getDeleteFlag, false));
        if (CollectionUtil.isNotEmpty(couponBatchOperationChannels)) {
            cacheDto.setRefIds(couponBatchOperationChannels.stream().map(CouponBatchOperationChannel::getRefid).collect(Collectors.toList()));
        }
        CouponBatchStockLimit stockLimit = couponBatchStockLimitMapper.selectOne(new LambdaQueryWrapper<CouponBatchStockLimit>()
                .eq(CouponBatchStockLimit::getBatchNo, batchNo)
                .last("limit 1"));
        if (stockLimit != null) {
            cacheDto.setDayLimit(stockLimit.getDayLimitFlag());
            cacheDto.setStockLimit(stockLimit.getStockLimitFlag());
        } else {
            cacheDto.setDayLimit(false);
            cacheDto.setStockLimit(false);
        }
        //查询本地红包使用规则
        LambdaQueryWrapper<CouponBatchValidDate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchValidDate::getCouponBatchDetailId, couponBatchDetail.getId());
        lambdaQueryWrapper.eq(CouponBatchValidDate::getDeleteFlag, false);

        CouponBatchValidDate couponBatchValidDate = couponBatchValidDateMapper.selectOne(lambdaQueryWrapper);
        cacheDto.setLastUseTime(this.calculateLastUseTime(couponBatchDetail, couponBatchValidDate));

        //缓存1天
        redisUtils.del(key);
        redisUtils.setex(key, JSON.toJSONString(cacheDto), 60 * 60 * 24 * 7);
        if (!ignoreCache){
            SkyLogUtils.infoByMethod(log, StringUtils.format("getBatchDetail-> db cost no cache:{}获取红包批次缓存:{}", (System.currentTimeMillis() - start), batchNo), batchNo, "");
        }else {
            SkyLogUtils.infoByMethod(log, StringUtils.format("getBatchDetail-> db cost pre hot:{}获取红包批次缓存:{}", (System.currentTimeMillis() - start), batchNo), batchNo, "");

        }
        return cacheDto;
    }

    private void setCouponBatchRuleDefaultValue(CouponBatchRule couponBatchRule) {
        couponBatchRule.setCreateTime(null);
        couponBatchRule.setCreator(null);
        couponBatchRule.setModifier(null);
        couponBatchRule.setModifiedTime(null);
        couponBatchRule.setDeleteFlag(null);
    }

    private LocalDateTime calculateLastUseTime(CouponBatchDetail couponBatchDetail, CouponBatchValidDate couponBatchValidDate) {
        LocalDateTime eventValidEndDate = couponBatchDetail.getEventValidEndDate();
        if (couponBatchValidDate == null) {
            return eventValidEndDate;
        }else if (ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchValidDate.getValidDateType())) {
            return couponBatchValidDate.getValidEndDate();
        }else if (ValidDateTypeEnum.SPECIFIED_PERIOD.getCode().equals(couponBatchValidDate.getValidDateType())){
            return LocalDateTimeUtil.offset(eventValidEndDate, couponBatchValidDate.getDynamicDurationDay() + couponBatchValidDate.getDynamicStartDay() , ChronoUnit.DAYS);
        }else if (ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(couponBatchValidDate.getValidDateType())){
            return LocalDateTimeUtil.offset(eventValidEndDate, couponBatchValidDate.getCountdownHour() , ChronoUnit.HOURS);
        }else if (ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDate.getValidDateType())){
            return LocalDateTimeUtil.offset(eventValidEndDate, couponBatchValidDate.getCountdownMinute(), ChronoUnit.MINUTES);
        }

        return eventValidEndDate;
    }

    private void setCouponBatchListDefaultValue(CouponBatchList couponBatchList) {
        couponBatchList.setAuditTime(null);
        couponBatchList.setAuditComment(null);
        couponBatchList.setAuditUser(null);
        couponBatchList.setAuditUserNo(null);
        couponBatchList.setDeleteFlag(null);
        couponBatchList.setCreator(null);
        couponBatchList.setCreateTime(null);
        couponBatchList.setCreatorNo(null);
        couponBatchList.setModifier(null);
        couponBatchList.setModifiedTime(null);
        couponBatchList.setModifierNo(null);
    }

    private String getBatchKey(String batchNo) {
        return "coupon:batchdetail:" + batchNo;
    }

    /**
     * 获取红包批次信息-查询鲲鹏接口
     *
     * @param batchNo 批号
     * @return {@link CouponBatchExtDto}
     */
    @Override
    public CouponBatchExtDto getExternalCoupon(String batchNo) {
        //调用红包接口
        CouponBatchInfoRequest request = new CouponBatchInfoRequest();
        request.setBatchNo(batchNo);
        CouponCommonResult<CouponBatchExtDto> couponBatchInfo = couponService.getCouponBatchInfo(request);
        if (couponBatchInfo == null || couponBatchInfo.getResult() == null) {
            return null;
        }

        return couponBatchInfo.getResult();
    }

    /**
     * 获取外部优惠券批次列表-查询鲲鹏接口
     *
     * @param batchNos 批号
     * @return {@link List}<{@link CouponBatchExtDto}>
     */
    @Override
    public List<CouponBatchExtDto> getExternalCouponBatchList(List<String> batchNos) {
        CouponBatchListInfoRequest request = new CouponBatchListInfoRequest();
        request.setBatchNos(batchNos);
        CouponCommonResult<CouponBatchListExtDto> couponBatchListInfo = couponService.getCouponBatchListInfo(request);
        if (couponBatchListInfo != null && couponBatchListInfo.getResult() != null) {
            return couponBatchListInfo.getResult().getBatchList();
        }
        return null;
    }

    // region 刷新缓存

    /**
     * 刷新缓存
     *
     * @param batchNo 批次号
     */
    @Override
    public int refreshCache(String batchNo) {
        if (StringUtils.isBlank(batchNo) || batchNo.equalsIgnoreCase("all")) {
            return refreshAllCache("");
        } else {
//            refreshCacheByBatchNo(batchNo, "");
            refreshCacheByBatchNoMq(batchNo);
            return 1;
        }
    }

    /**
     * 刷新缓存
     *
     * @param batchNo 批次号
     * @param env     环境
     */
    @Override
    public void refreshCache(String batchNo, String env) {
        if (StringUtils.isBlank(batchNo) || batchNo.equalsIgnoreCase("all")) {
            refreshAllCache(env);
        } else {
//            refreshCacheByBatchNo(batchNo, env);
            refreshCacheByBatchNoMq(batchNo);
        }
    }

    /**
     * 刷新所有缓存
     *
     * @param env 环境
     */
    private int refreshAllCache(String env) {
        List<String> batchNos = couponBatchDetailMapper.selectList(new LambdaQueryWrapper<CouponBatchDetail>()
                        .select(CouponBatchDetail::getBatchNo)
//                        .in(CouponBatchDetail::getStatus, CollectionUtil.newArrayList(CouponDetailStatusEnum.WAIT_AUDIT.getCode()
//                                , CouponDetailStatusEnum.AUDITING.getCode()
//                                , CouponDetailStatusEnum.PASS.getCode()))
//                        .eq(CouponBatchDetail::getDeleteFlag, false)
                )
                .stream().map(CouponBatchDetail::getBatchNo).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(batchNos)) {
            return 0;
        }
        batchNos = batchNos.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        SkyLogUtils.infoByMethod(log, StringUtils.format("开始全量刷新红包批次缓存:{}", JSON.toJSONString(batchNos)), "", "");
        batchNos.stream().forEach(s -> {
            try {
//                refreshCacheByBatchNo(s, env);
                refreshCacheByBatchNoMq(s);
            } catch (Exception e) {
                SkyLogUtils.errorByMethod(log, StringUtils.format("刷新红包批次缓存异常:{}", s), s, "", e);
            }
        });
        return batchNos.size();
    }

    private void refreshCacheByBatchNoMq(String batchNo) {
        turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC
                , CouponBatchSyncDto.builder().batchNo(batchNo).build());
    }

    @Override
    public void refreshCacheByBatchNo(String batchNo, String env) {
        SkyLogUtils.infoByMethod(log, StringUtils.format("开始刷新红包批次缓存:{}", batchNo), batchNo, "");
        //删除缓存
        redisUtils.del(getBatchKey(batchNo));
        CouponBatchCacheDto batchDetail = getBatchDetail(batchNo, true);
        SkyLogUtils.infoByMethod(log, StringUtils.format("获取红包批次信息:{}", JSON.toJSONString(batchDetail)), batchNo, "");
        if (batchDetail == null || batchDetail.getBaseInfo() == null) {
            //红包在鲲鹏不存在
            return;
        }
        boolean available = checkBatch(batchDetail);

        CouponEsIndexDto indexDto = CouponEsIndexDto.fromExt(batchDetail);
        // 非过期红包才可以无效
        if (!CouponBatchApiStatusEnum.Expired.getCode().equals(indexDto.getStatus()) && !available) {
            indexDto.setStatus(CouponBatchApiStatusEnum.UnAvailable.getCode());
        }
        // 过期才清缓存 无效已领是可以使用的
        if (CouponBatchApiStatusEnum.Expired.getCode().equals(indexDto.getStatus())) {
            //删除缓存
            redisUtils.del(getBatchKey(batchNo));
            SkyLogUtils.infoByMethod(log, StringUtils.format("红包批次过期，清除缓存:{}", batchNo), batchNo, "");
        }
        SkyLogUtils.infoByMethod(log, StringUtils.format("红包批次，推送es:{}，data:{}", batchNo, JSON.toJSONString(indexDto)), batchNo, "");
        EsSearchUtils.putObject(EsSearchUtils.EsIndex.CouponBatchList
                , CollectionUtil.newArrayList(indexDto)
                , env);
    }

    /**
     * 判断批次有效性
     * 先判断本地批次状态，再判断远程批次状态、库存、有效期
     *
     * @param batchDetail 详细信息
     * @return boolean
     */
    private boolean checkBatch(CouponBatchCacheDto batchDetail) {
        CouponBatchList listInfo = batchDetail.getListInfo();
        CouponBatchDetail detailInfo = batchDetail.getDetailInfo();
        if (listInfo == null || detailInfo == null) {
            return false;
        }
        if (!detailInfo.getStatus().equals(CouponBatchExtStatusEnum.Normal.getCode())
                || !listInfo.getStatus().equals(CouponStatusEnum.VALID.getCode())) {
            return false;
        }

        //获取远程红包信息
        CouponBatchExtDto externalCoupon = getExternalCoupon(detailInfo.getBatchNo());
        if (externalCoupon == null) {
            return false;
        }
        //超出有效期
        LocalDateTime dtNow = LocalDateTime.now();
        if (dtNow.isBefore(externalCoupon.getBeginDate()) || dtNow.isAfter(externalCoupon.getEndDate())) {
            return false;
        }
        //状态不一致
        if (!externalCoupon.getBatchStatus().equals(CouponBatchExtStatusEnum.Normal.getCode())) {
            return false;
        }
        //库存=0
        if (externalCoupon.getRemainStock() == null || externalCoupon.getRemainStock() <= 0) {
            return false;
        }
        //日期配置问题
        if (ValidDateTypeEnum.LONG_TERM.getCode().equals(externalCoupon.getExpType()) && externalCoupon.getEndDate() != null && externalCoupon.getExpEndDate() != null && externalCoupon.getEndDate().isAfter(externalCoupon.getExpEndDate())) {
            return false;
        }
        //当日领取限制
        if ((batchDetail.getDayLimit() != null && batchDetail.getDayLimit())) {
            return false;
        }
        //库存限制
        if (batchDetail.getStockLimit() != null && batchDetail.getStockLimit()) {
            return false;
        }
        return true;
    }

    // endregion
}
