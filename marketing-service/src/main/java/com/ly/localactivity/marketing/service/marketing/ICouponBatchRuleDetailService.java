package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponBatchRule;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包使用规则明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchRuleDetailService extends IService<CouponBatchRuleDetail> {

    Boolean saveOrUpdate(CouponBatchRule couponBatchRule, List<String> dataIdList);

    Boolean deleteByIds(List<Long> invalidIdList);

    Map<Long, List<CouponBatchRuleDetail>> selectByCouponBatchRuleId(List<Long> ruleIdList);
}
