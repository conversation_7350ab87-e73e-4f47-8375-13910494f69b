package com.ly.localactivity.marketing.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.mq.TurboMqUtils;
import com.ly.localactivity.framework.utils.SignUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.CouponCommonService;
import com.ly.localactivity.marketing.application.ICouponServiceApp;
import com.ly.localactivity.marketing.application.UserCouponListQueryService;
import com.ly.localactivity.marketing.application.model.dto.CouponBatchQueryDto;
import com.ly.localactivity.marketing.application.model.dto.CouponQueryDto;
import com.ly.localactivity.marketing.application.model.ro.*;
import com.ly.localactivity.marketing.application.model.vo.*;
import com.ly.localactivity.marketing.common.bean.CommonResultDto;
import com.ly.localactivity.marketing.common.constants.CouponConst;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.enums.CouponDetailStatusEnum;
import com.ly.localactivity.marketing.common.enums.CouponRuleLimitTypeEnum;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchApiStatusEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchExtStatusEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponExtStatusEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoDetailExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponOccupyExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponSimpleInfoExtDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.*;
import com.ly.localactivity.marketing.external.coupon.service.CouponCoreService;
import com.ly.localactivity.marketing.external.coupon.service.CouponService;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponRechargeService;
import com.ly.localactivity.marketing.service.coupon.ICouponSearchService;
import com.ly.localactivity.marketing.service.marketing.ICouponConsumeRecordService;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * CouponServiceAppImpl
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Slf4j
@Service
public class CouponServiceAppImpl implements ICouponServiceApp {
    @Resource
    private CouponCommonService couponCommonService;
    @Resource
    private TurboMqUtils turboMqUtils;
    @Resource
    private CouponService couponService;
    @Resource
    private CouponCoreService couponCoreService;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private ICouponRechargeService couponRechargeService;
    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private ICouponSearchService couponSearchService;
    @Resource
    private UserCouponListQueryService couponAppQueryService;
    @Resource
    private ICouponConsumeRecordService couponConsumeRecordService;

    /**
     * 查询优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    @Override
    public CommonResult<CouponQueryResponse> queryCouponListV3(CouponQueryRo queryRo) {
        SkyLogUtils.infoByMethod(log, "queryCouponList->start;", "", "");
        CouponQueryResponse queryResponse = new CouponQueryResponse();
        String validMsg = couponCommonService.checkReuqest(queryRo);
        if (StringUtils.isNotBlank(validMsg)) {
            return CommonResult.failed(validMsg);
        }
        // 防止并发修改
        CouponQueryRo couponQueryRo = JSON.parseObject(JSON.toJSONBytes(queryRo), CouponQueryRo.class);
        long start = System.currentTimeMillis();
        long getCouponBatchStart = System.currentTimeMillis();

        // 查询所有平台红包
        List<CouponEsIndexDto> allCouponEsIndexDtoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(queryRo.getBatchNos())){
            allCouponEsIndexDtoList = searchCouponBatchList(queryRo.getRequestId()
                    , Collections.singletonList(queryRo.getPlatform()), null, null, queryRo.getLimitData(), false, false);
        }else {
            allCouponEsIndexDtoList = couponSearchService.searchEsByBatchNos(queryRo.getBatchNos());
        }

        // 无数据直接返回
        if (CollectionUtil.isEmpty(allCouponEsIndexDtoList)){
            return CommonResult.success(queryResponse);
        }

        // 可领红包
        List<CouponEsIndexDto> canTakeCouponList = CouponEsIndexDto.filterBathchList(allCouponEsIndexDtoList, queryRo.getBatchNos(), queryRo.getRefid(), queryRo.getPositionCode()
                , true
                , null);

        List<CouponBatchVo> couponBatchList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(canTakeCouponList)) {
            queryRo.setBatchNos(canTakeCouponList.stream().map(CouponEsIndexDto::getBatchNo).collect(Collectors.toList()));
            couponBatchList = this.getCouponBatch(queryRo, true);
        }

        queryResponse.setCouponBatchList(couponBatchList);
        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->batchFuture cost:{0};thread:{1};", (System.currentTimeMillis() - getCouponBatchStart), Thread.currentThread().getId()), "", "");

        boolean queryLimitWithQueryUserCoupon = true;
        boolean isDetailPage = couponQueryRo.getIsDetailPage() != null && couponQueryRo.getIsDetailPage();

        // 已领红包（排除无效数据）
        List<CouponEsIndexDto> takedCouponList = CouponEsIndexDto.filterBathchList(allCouponEsIndexDtoList, couponQueryRo.getBatchNos(), couponQueryRo.getRefid(), couponQueryRo.getPositionCode()
                , false
                , isDetailPage);

        CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                .queryRo(couponQueryRo)
                .status(null)
                .simpleMode(Boolean.TRUE.equals(couponQueryRo.getSimple()))
                .filterPositionCode(!isDetailPage) // 不是详情页才过滤点位
                .build();

        if (queryLimitWithQueryUserCoupon){
            couponQueryDto.setLimitData(couponQueryRo.getLimitData());
            couponQueryDto.setBatchNos(couponQueryRo.getBatchNos());
            couponQueryDto.setPositionCode(couponQueryRo.getPositionCode());
        }

        long couponFutureStart = System.currentTimeMillis();
        // 超时时间
//            couponQueryDto.setTimeOut(100);
        List<CouponVo> couponVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(takedCouponList)){
            couponVoList = couponAppQueryService.queryUserCoupon(couponQueryDto, queryLimitWithQueryUserCoupon, takedCouponList);
        }

        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->couponFuture cost:{0};thread:{1};", (System.currentTimeMillis() - couponFutureStart), Thread.currentThread().getId()) , "", "");
        queryResponse.setCouponList(couponVoList);

        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search cost:{0};thread:{1}", (System.currentTimeMillis() - start), Thread.currentThread().getId()) , "", "");

//        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search result{0}", JSON.toJSONString(queryResponse)) , "", "");


//        start = System.currentTimeMillis();
        // 根据条件过滤已领取红包
        if (!queryLimitWithQueryUserCoupon && CollectionUtil.isNotEmpty(queryResponse.getCouponList())){
            queryResponse.setCouponList(this.calcCouponListByLimitData(queryResponse.getCouponList(), couponQueryRo));
        }
        // 计算红包是否可领取状态
        setCouponBatchStatus(queryResponse.getCouponBatchList(), queryResponse.getCouponList());
//        log.info("queryCouponList->setCouponBatchStatus cost:{}", System.currentTimeMillis() - start);
//        start = System.currentTimeMillis();
        // 过滤掉不可领取 及 refid不匹配 的红包
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.setCouponBatchList(queryResponse.getCouponBatchList().stream().filter(s -> {
                boolean available = CouponBatchApiStatusEnum.Available.getCode().equals(s.getStatus());
                if (!available) {
                    return false;
                }
                //如果是按运营点位查的，需要再过滤下refid
                if (StringUtils.isNotBlank(queryRo.getPositionCode())) {
                    if (CollectionUtil.isEmpty(s.getRefIdList())) {
                        //红包没有配置refid，都显示
                        return true;
                    }
                    if (StringUtils.isBlank(queryRo.getRefid())) {
                        return false;
                    }
                    return s.getRefIdList().contains(queryRo.getRefid());
                }
                return true;
            }).collect(Collectors.toList()));
        }

//        log.info("queryCouponList->过滤掉不可领取 cost:{}", System.currentTimeMillis() - start);

        // 过滤掉不可使用的数据
//        start = System.currentTimeMillis();
        //过滤掉已经使用过的红包数据
//        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search res:{0}", JSON.toJSONString(queryResponse)) , "", "");

        if (CollectionUtil.isNotEmpty(queryResponse.getCouponList())) {
            queryResponse.setCouponList(queryResponse.getCouponList()
                    .stream()
                    .filter(s -> s.getStatus().equals(CouponExtStatusEnum.Unused.getCode()))
                    .collect(Collectors.toList()));
        }
//        log.info("queryCouponList->过滤掉已经使用过的红包数据 cost:{}", System.currentTimeMillis() - start);
//        start = System.currentTimeMillis();
        // 未领取 批次号排序
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.getCouponBatchList().sort(Comparator.comparing(CouponBatchVo::getBatchNo));
        }
//        log.info("queryCouponList->排序 cost:{}", System.currentTimeMillis() - start);
        long cost = System.currentTimeMillis() - start;
        if (cost > 30){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost30 v3:" + cost, "", queryRo.getRequestId());
        }
        if (cost > 60){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost60 v3:" + cost, "", queryRo.getRequestId());
        }
        if (cost > 90){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost90 v3:" + cost, "", queryRo.getRequestId());
        }
        return CommonResult.success(queryResponse);
    }

    /**
     * 查询优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    @Override
    public CommonResult<CouponQueryResponse> queryCouponListV2(CouponQueryRo queryRo) {
        SkyLogUtils.infoByMethod(log, "queryCouponList->start;", "", "");
        CouponQueryResponse queryResponse = new CouponQueryResponse();
        String validMsg = couponCommonService.checkReuqest(queryRo);
        if (StringUtils.isNotBlank(validMsg)) {
            return CommonResult.failed(validMsg);
        }
        // 防止并发修改
        CouponQueryRo couponQueryRo = JSON.parseObject(JSON.toJSONBytes(queryRo), CouponQueryRo.class);
        long start = System.currentTimeMillis();
        CompletableFuture<List<CouponBatchVo>> batchFuture = CompletableFuture.supplyAsync(() -> {
            long getCouponBatchStart = System.currentTimeMillis();
            if (CollectionUtil.isEmpty(queryRo.getBatchNos())) {
                queryRo.setBatchNos(searchCouponBatchNos(queryRo.getRequestId()
                        , queryRo.getPlatform()
                        , queryRo.getRefid()
                        , queryRo.getPositionCode()
                        , queryRo.getLimitData()
                        , true
                        , true));
            }
//            SkyLogUtils.infoByMethod(log, "queryCouponList->开始查询红包批次:" + JSON.toJSONString(queryRo.getBatchNos()), "", queryRo.getRequestId());
            if (CollectionUtil.isEmpty(queryRo.getBatchNos())) {
                return Collections.emptyList();
            }
            List<CouponBatchVo> couponBatchList = getCouponBatch(queryRo, true);
            SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->batchFuture cost:{0};thread:{1};", (System.currentTimeMillis() - getCouponBatchStart), Thread.currentThread().getId()), "", "");
            return couponBatchList;
        });

//        CompletableFuture<List<String>> validReceivedBatchNoFuture = CompletableFuture.supplyAsync(() -> {
//            boolean isDetailPage = couponQueryRo.getIsDetailPage() != null && couponQueryRo.getIsDetailPage();
//            CouponQueryDto couponQueryDto = CouponQueryDto.builder()
//                    .queryRo(couponQueryRo)
//                    .status(null)
//                    .limitData(couponQueryRo.getLimitData())
//                    .batchNos(couponQueryRo.getBatchNos())
//                    .simpleMode(Boolean.TRUE.equals(couponQueryRo.getSimple()))
//                    .positionCode(couponQueryRo.getPositionCode())
//                    .filterPositionCode(!isDetailPage)
//                    .build();
////            SkyLogUtils.infoByMethod(log, "queryCouponList->开始查询红包批次:" + JSON.toJSONString(queryRo.getBatchNos()), "", queryRo.getRequestId());
//            long getCouponBatchStart = System.currentTimeMillis();
//            List<String> couponbatchNoList = couponAppQueryService.queryEsBatchNos(couponQueryDto, null);
//            SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->validReceivedBatchNoFuture cost:{0};thread:{1}", (System.currentTimeMillis() - getCouponBatchStart), Thread.currentThread().getId()) , "", "");
//            return couponbatchNoList;
//        });

        boolean queryLimitWithQueryUserCoupon = true;
        CompletableFuture<List<CouponVo>> couponFuture = CompletableFuture.supplyAsync(() -> {
            boolean isDetailPage = couponQueryRo.getIsDetailPage() != null && couponQueryRo.getIsDetailPage();

            CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                    .queryRo(couponQueryRo)
                    .status(null)
//                    .limitData(couponQueryRo.getLimitData())
//                    .batchNos(couponQueryRo.getBatchNos())
                    .simpleMode(Boolean.TRUE.equals(couponQueryRo.getSimple()))
//                    .positionCode(couponQueryRo.getPositionCode())
                    .filterPositionCode(!isDetailPage) // 不是详情页才过滤点位
                    .build();

            if (queryLimitWithQueryUserCoupon){
                couponQueryDto.setLimitData(couponQueryRo.getLimitData());
                couponQueryDto.setBatchNos(couponQueryRo.getBatchNos());
                couponQueryDto.setPositionCode(couponQueryRo.getPositionCode());
            }

            long couponFutureStart = System.currentTimeMillis();
            // 超时时间
//            couponQueryDto.setTimeOut(100);
            List<CouponVo> couponVoList = couponAppQueryService.queryUserCoupon(couponQueryDto, queryLimitWithQueryUserCoupon, null);
            SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->couponFuture cost:{0};thread:{1};", (System.currentTimeMillis() - couponFutureStart), Thread.currentThread().getId()) , "", "");
            return couponVoList;
        });

        try {
            List<CouponBatchVo> couponBatchVoList = batchFuture.join();
            queryResponse.setCouponBatchList(couponBatchVoList);
        } catch (Exception e) {
            log.error("queryCouponList->batchFuture.join error", e);
        }
//        List<String> validReceivedBatchList = new ArrayList<>();
//        try {
//            List<String> receivedBatchList = validReceivedBatchNoFuture.join();
//            if (CollectionUtil.isNotEmpty(receivedBatchList)) {
//                validReceivedBatchList.addAll(receivedBatchList);
//            }
//        } catch (Exception e) {
//            log.error("queryCouponList->validReceivedBatchNoFuture.join error", e);
//        }
        // 可领红包无数据则直接返回不用等待鲲鹏接口
//        if (CollectionUtil.isEmpty(validReceivedBatchList)) {
//            SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search cost:{0};thread:{1};current:{2}", (System.currentTimeMillis() - start), Thread.currentThread().getId(), System.currentTimeMillis()) , "", "");
//            return CommonResult.success(queryResponse);
//        }

        try {
            // 已领红包依赖可领红包
            queryResponse.setCouponList(couponFuture.join());
        } catch (Exception e) {
            log.error("queryCouponList->couponFuture.join error", e);
        }

        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search cost:{0};thread:{1}", (System.currentTimeMillis() - start), Thread.currentThread().getId()) , "", "");

//        start = System.currentTimeMillis();
        // 根据条件过滤已领取红包
         if (!queryLimitWithQueryUserCoupon && CollectionUtil.isNotEmpty(queryResponse.getCouponList())){
             queryResponse.setCouponList(this.calcCouponListByLimitData(queryResponse.getCouponList(), couponQueryRo));
         }
        // 计算红包是否可领取状态
        setCouponBatchStatus(queryResponse.getCouponBatchList(), queryResponse.getCouponList());
//        log.info("queryCouponList->setCouponBatchStatus cost:{}", System.currentTimeMillis() - start);
//        start = System.currentTimeMillis();
        // 过滤掉不可领取 及 refid不匹配 的红包
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.setCouponBatchList(queryResponse.getCouponBatchList().stream().filter(s -> {
                boolean available = CouponBatchApiStatusEnum.Available.getCode().equals(s.getStatus());
                if (!available) {
                    return false;
                }
                //如果是按运营点位查的，需要再过滤下refid
                if (StringUtils.isNotBlank(queryRo.getPositionCode())) {
                    if (CollectionUtil.isEmpty(s.getRefIdList())) {
                        //红包没有配置refid，都显示
                        return true;
                    }
                    if (CollectionUtil.isEmpty(s.getRefIdList()) || StringUtils.isBlank(queryRo.getRefid())) {
                        return false;
                    }
                    return s.getRefIdList().contains(queryRo.getRefid());
                }
                return true;
            }).collect(Collectors.toList()));
        }

//        log.info("queryCouponList->过滤掉不可领取 cost:{}", System.currentTimeMillis() - start);

        // 过滤掉不可使用的数据
//        start = System.currentTimeMillis();
        //过滤掉已经使用过的红包数据
        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryCouponList->search res:{0}", JSON.toJSONString(queryResponse)) , "", "");

        if (CollectionUtil.isNotEmpty(queryResponse.getCouponList())) {
            queryResponse.setCouponList(queryResponse.getCouponList()
                    .stream()
                    .filter(s -> s.getStatus().equals(CouponExtStatusEnum.Unused.getCode()))
                    .collect(Collectors.toList()));
        }
//        log.info("queryCouponList->过滤掉已经使用过的红包数据 cost:{}", System.currentTimeMillis() - start);
//        start = System.currentTimeMillis();
        // 未领取 批次号排序
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.getCouponBatchList().sort(Comparator.comparing(CouponBatchVo::getBatchNo));
        }
//        log.info("queryCouponList->排序 cost:{}", System.currentTimeMillis() - start);
        long cost = System.currentTimeMillis() - start;
        if (cost > 30){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost30: v2" + cost, "", queryRo.getRequestId());
        }
        if (cost > 60){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost60: v2" + cost, "", queryRo.getRequestId());
        }
        if (cost > 90){
            SkyLogUtils.warnByMethod(log, "queryCouponList->cost90: v2" + cost, "", queryRo.getRequestId());
        }
        return CommonResult.success(queryResponse);
    }

    /**
     * 计算可用红包
     * @param couponList
     * @param couponQueryRo
     * @return
     */
    private List<CouponVo> calcCouponListByLimitData(List<CouponVo> couponList, CouponQueryRo couponQueryRo) {
        if (CollectionUtil.isEmpty(couponList)){
            return couponList;
        }
        SkyLogUtils.infoByMethod(log, MessageFormat.format("calcCouponListByLimitData, req:{0}，couponQueryRo:{1}", JSON.toJSONString(couponList), JSON.toJSONString(couponQueryRo)), "", "");

        // batchNo 不为空，则只返回指定红包批次的
        if (CollectionUtil.isNotEmpty(couponQueryRo.getBatchNos())){
            return couponList.stream().filter(s -> couponQueryRo.getBatchNos().contains(s.getBatchNo())).collect(Collectors.toList());
        }
       List<CouponVo> filterCouponList = couponList.stream().filter(coupon -> {
            if (CollectionUtil.isEmpty(coupon.getPlatforms())){
                return false;
            }
            return coupon.getPlatforms().contains(couponQueryRo.getPlatform().toString());
        }).collect(Collectors.toList());

        // 不是详情页需要过滤点位和refId
        if (!Boolean.TRUE.equals(couponQueryRo.getIsDetailPage())){
            // 交集
            filterCouponList = filterCouponList.stream().filter(coupon ->{
                boolean delete = StringUtils.isNotBlank(couponQueryRo.getPositionCode()) && (CollectionUtil.isEmpty(coupon.getOperationPositionCodes()) || !coupon.getOperationPositionCodes().contains(couponQueryRo.getPositionCode()));
                if (delete){
                    return false;
                }
                delete = StringUtils.isNotBlank(couponQueryRo.getRefid()) && CollectionUtil.isNotEmpty(coupon.getRefIdList()) && !coupon.getRefIdList().equals(couponQueryRo.getRefid());
                return !delete;
            }).collect(Collectors.toList());
        }
        CouponLimitRo limitData = couponQueryRo.getLimitData();
        if (limitData != null){
            List<String> limitResourceIdList = StringUtils.isNotBlank(limitData.getResourceIds()) ? Arrays.asList(limitData.getResourceIds().split(",")) : new ArrayList<>();
            List<String> limitCityIdList = StringUtils.isNotBlank(limitData.getCityIds()) ? Arrays.asList(limitData.getCityIds().split(",")) : new ArrayList<>();
            List<String> limitSupplierIdList = new ArrayList<>();
            if (limitData.getSupplierId() != null){
                limitSupplierIdList.add(String.valueOf(limitData.getSupplierId()));
            }else if (StringUtils.isNotBlank(limitData.getSupplierIds())){
                limitSupplierIdList.addAll(Arrays.asList(limitData.getSupplierIds().split(",")));
            }

            // 并集
            filterCouponList = filterCouponList.stream().filter(coupon -> {
                CouponResourceRuleInfo couponRule = coupon.getRule();
                if (couponRule == null){
                    return false;
                }
                // 平台全部红包
                if (CouponBatchListTypeEnum.PLATFORM.getCode().equals(coupon.getBatchType()) && CouponRuleLimitTypeEnum.ALL.getCode().equals(couponRule.getLimitType())){
                    return true;
                }
                // 限制资源
                if (CollectionUtil.isNotEmpty(limitResourceIdList) && CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponRule.getLimitType()) && CollectionUtil.containsAny(couponRule.getLimitData(), limitResourceIdList)){
                    return true;
                }
                // 限制城市
                if (CollectionUtil.isNotEmpty(limitCityIdList) && CouponRuleLimitTypeEnum.DEPARTURE.getCode().equals(couponRule.getLimitType()) && CollectionUtil.containsAny(couponRule.getLimitData(), limitCityIdList)){
                    return true;
                }
                // 限制供应商
                return CollectionUtil.isNotEmpty(limitSupplierIdList) && CouponRuleLimitTypeEnum.ALL.getCode().equals(couponRule.getLimitType()) && limitSupplierIdList.contains(String.valueOf(coupon.getSupplierId()));
            }).collect(Collectors.toList());
        }

        SkyLogUtils.infoByMethod(log, MessageFormat.format("calcCouponListByLimitData, resp:{0}", JSON.toJSONString(filterCouponList)), "", "");

        if (Boolean.TRUE.equals(couponQueryRo.getSimple()) && CollectionUtil.isNotEmpty(filterCouponList)){
            for (CouponVo couponVo : filterCouponList) {
                couponVo.setOperationPositionCodes(null);
                couponVo.setPlatforms(null);
                couponVo.setRefIdList(null);
            }
        }

        return filterCouponList;
    }

    /**
     * 查询优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    @Override
    public CommonResult<CouponQueryResponse> queryCouponList(CouponQueryRo queryRo) {
        CouponQueryResponse queryResponse = new CouponQueryResponse();
        String validMsg = couponCommonService.checkReuqest(queryRo);
        if (StringUtils.isNotBlank(validMsg)) {
            return CommonResult.failed(validMsg);
        }
        // 防止并发修改
        CouponQueryRo couponQueryRo = JSON.parseObject(JSON.toJSONBytes(queryRo), CouponQueryRo.class);
        long start = System.currentTimeMillis();
        CompletableFuture<List<CouponBatchVo>> batchFuture = CompletableFuture.supplyAsync(() -> {
            if (CollectionUtil.isEmpty(queryRo.getBatchNos())) {
                queryRo.setBatchNos(searchCouponBatchNos(queryRo.getRequestId()
                        , queryRo.getPlatform()
                        , queryRo.getRefid()
                        , queryRo.getPositionCode()
                        , queryRo.getLimitData()
                        , false
                        , true));
            }
//            SkyLogUtils.infoByMethod(log, "queryCouponList->开始查询红包批次:" + JSON.toJSONString(queryRo.getBatchNos()), "", queryRo.getRequestId());
            if (CollectionUtil.isEmpty(queryRo.getBatchNos())) {
                return Collections.emptyList();
            }
            long getCouponBatchStart = System.currentTimeMillis();
            List<CouponBatchVo> couponBatchList = getCouponBatch(queryRo, false);
            SkyLogUtils.infoByMethod(log, "queryCouponList->batchFuture cost:{}" + (System.currentTimeMillis() - getCouponBatchStart), "", "");
            return couponBatchList;
        });
        CompletableFuture<List<CouponVo>> couponFuture = CompletableFuture.supplyAsync(() -> {
            boolean isDetailPage = couponQueryRo.getIsDetailPage() != null && couponQueryRo.getIsDetailPage();
            CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                    .queryRo(couponQueryRo)
                    .status(null)
                    .simpleMode(false)
                    .limitData(couponQueryRo.getLimitData())
                    .batchNos(couponQueryRo.getBatchNos())
                    .simpleMode(Boolean.TRUE.equals(couponQueryRo.getSimple()))
                    .positionCode(couponQueryRo.getPositionCode())
                    .filterPositionCode(!isDetailPage)
                    .build();
            long couponFutureStart = System.currentTimeMillis();
            List<CouponVo> couponVoList = couponAppQueryService.queryUserCoupon(couponQueryDto, true, null);
            SkyLogUtils.infoByMethod(log, "queryCouponList->couponFuture cost:{}" + (System.currentTimeMillis() - couponFutureStart), "", "");
            return couponVoList;
        });

        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(batchFuture, couponFuture);
        voidCompletableFuture.whenComplete((v, e) -> {
            if (e != null) {
                log.error("queryCouponList error", e);
                return;
            }

            if (!batchFuture.isCompletedExceptionally()) {
                queryResponse.setCouponBatchList(batchFuture.join());
            }
            if (!couponFuture.isCompletedExceptionally()) {
                queryResponse.setCouponList(couponFuture.join());
            }
        });

        try {
            voidCompletableFuture.join();
        } catch (Exception e) {
            log.error("queryCouponList->voidCompletableFuture.join error", e);
        }

        log.info("queryCouponList->search cost:{}", System.currentTimeMillis() - start);
        start = System.currentTimeMillis();
        // 计算红包是否可领取状态
        setCouponBatchStatus(queryResponse.getCouponBatchList(), queryResponse.getCouponList());
//        log.info("queryCouponList->setCouponBatchStatus cost:{}", System.currentTimeMillis() - start);
        start = System.currentTimeMillis();
        // 过滤掉不可领取 及 refid不匹配 的红包
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.setCouponBatchList(queryResponse.getCouponBatchList().stream().filter(s -> {
                boolean available = CouponBatchApiStatusEnum.Available.getCode().equals(s.getStatus());
                if (!available) {
                    return false;
                }
                //如果是按运营点位查的，需要再过滤下refid
                if (StringUtils.isNotBlank(queryRo.getPositionCode())) {
                    if (CollectionUtil.isEmpty(s.getRefIdList())) {
                        //红包没有配置refid，都显示
                        return true;
                    }
                    if (CollectionUtil.isEmpty(s.getRefIdList()) || StringUtils.isBlank(queryRo.getRefid())) {
                        return false;
                    }
                    return s.getRefIdList().contains(queryRo.getRefid());
                }
                return true;
            }).collect(Collectors.toList()));
        }

//        log.info("queryCouponList->过滤掉不可领取 cost:{}", System.currentTimeMillis() - start);

        // 过滤掉不可使用的数据
        start = System.currentTimeMillis();
        //过滤掉已经使用过的红包数据
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponList())) {
            queryResponse.setCouponList(queryResponse.getCouponList()
                    .stream()
                    .filter(s -> s.getStatus().equals(CouponExtStatusEnum.Unused.getCode()))
                    .collect(Collectors.toList()));
        }
//        log.info("queryCouponList->过滤掉已经使用过的红包数据 cost:{}", System.currentTimeMillis() - start);
        start = System.currentTimeMillis();
        // 未领取 批次号排序
        if (CollectionUtil.isNotEmpty(queryResponse.getCouponBatchList())) {
            queryResponse.getCouponBatchList().sort(Comparator.comparing(CouponBatchVo::getBatchNo));
        }
//        log.info("queryCouponList->排序 cost:{}", System.currentTimeMillis() - start);
        return CommonResult.success(queryResponse);
    }

    //region 查询红包批次

    private void setCouponBatchStatus(List<CouponBatchVo> batchVoList, List<CouponVo> couponVoList) {
        // 计算所有红包是否可领取 不强依赖已领取的红包
        if (CollectionUtil.isEmpty(batchVoList)) {
            return;
        }

        batchVoList.forEach(s -> {
            //检查红包批次，判断是否可领取
            boolean availableStatus = getBatchAvailableStatus(s, couponVoList);
            if (!availableStatus) {
                s.setStatus(CouponBatchApiStatusEnum.UnAvailable.getCode());
            }
        });

    }

    private boolean getBatchAvailableStatus(CouponBatchVo batchVo, List<CouponVo> couponVoList) {
        if (CollectionUtil.isEmpty(couponVoList)){
            couponVoList = new ArrayList<>();
        }
        //检查红包有效期
        LocalDateTime dtNow = LocalDateTime.now();
        if (dtNow.isBefore(batchVo.getBeginDate()) || dtNow.isAfter(batchVo.getEndDate())) {
            return false;
        }

        //检查红包剩余库存
        if (batchVo.getRemainStock() != null && batchVo.getRemainStock() <= 0) {
            return false;
        }

        //设置红包已领数量
        batchVo.setReceivedCount(couponVoList.stream().filter(s -> s.getBatchNo().equals(batchVo.getBatchNo())).count());

        //检查个人领取限制 0 不限制 todo 过滤掉超出每日限制  || batchVo.getDayLimit()
        if (batchVo.getPersonLimit() != null && batchVo.getPersonLimit() > 0 && CollectionUtil.isNotEmpty(couponVoList)) {
            if (batchVo.getReceivedCount() != null && batchVo.getReceivedCount() >= batchVo.getPersonLimit()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 查询红包批次
     *
     * @param queryRo  查询ro
     * @param useCache
     * @return {@link List}<{@link CouponBatchVo}>
     */
    private List<CouponBatchVo> getCouponBatch(CouponQueryRo queryRo, boolean useCache) {

        List<CouponBatchCacheDto> cacheDtos = couponQueryService.getCouponBatch(queryRo.getBatchNos(), useCache);
        if (CollectionUtil.isEmpty(cacheDtos)) {
            return Collections.emptyList();
        }
        List<CouponBatchVo> couponBatchVoList = new CopyOnWriteArrayList<>();

        // todo 鲲鹏未查询到 过滤掉
        cacheDtos.stream().filter(dto -> Objects.nonNull(dto.getBaseInfo())).forEach(s -> {
            if (!s.getBaseInfo().getBatchStatus().equals(CouponBatchExtStatusEnum.Normal.getCode())) {
                //过滤掉鲲鹏状态不可用的红包
                return;
            }

            if (s.getDetailInfo() != null && CouponDetailStatusEnum.CANCEL.getCode().equals(s.getDetailInfo().getStatus())){
                // 防止本地删除后鲲鹏红包未删除，显示依然可用
                return;
            }

            couponBatchVoList.add(CouponBatchVo.fromExt(s, Boolean.TRUE.equals(queryRo.getSimple())));
        });

        return couponBatchVoList.stream().filter(s ->
                        CollectionUtil.isNotEmpty(s.getPlatforms()) && s.getPlatforms().contains(queryRo.getPlatform().toString()))
                .collect(Collectors.toList());
    }

    /**
     * 查询红包批次
     *
     * @param queryRo  查询ro
     * @param useCache
     * @return {@link List}<{@link CouponBatchVo}>
     */
    private List<CouponBatchVo> getCouponBatch(AllCouponQueryRo queryRo, boolean useCache) {

        List<CouponBatchCacheDto> cacheDtos = couponQueryService.getCouponBatch(queryRo.getBatchNos(), useCache);
        if (CollectionUtil.isEmpty(cacheDtos)) {
            return Collections.emptyList();
        }

        // todo 鲲鹏未查询到 过滤掉
        List<CouponBatchVo> couponBatchVoList = cacheDtos.stream().filter(dto -> Objects.nonNull(dto.getBaseInfo()) && CouponBatchExtStatusEnum.Normal.getCode().equals(dto.getBaseInfo().getBatchStatus())).map(s -> CouponBatchVo.fromExt(s, Boolean.TRUE.equals(queryRo.getSimple()))).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(queryRo.getPlatforms())){
            couponBatchVoList = couponBatchVoList.stream().filter(s ->
                            CollectionUtil.isNotEmpty(s.getPlatforms()) && s.getPlatforms().stream().anyMatch(platform -> queryRo.getPlatforms().contains(Integer.parseInt(platform))))
                    .collect(Collectors.toList());
        }

        return couponBatchVoList;
    }


    /**
     * 搜索优惠券批次号
     *
     * @param requestId    请求id
     * @param platform     站台
     * @param refid        refid
     * @param positionCode 位置代码
     * @param limitRo      限制ro
     * @return {@link List}<{@link String}>
     */
    private List<String> searchCouponBatchNos(String requestId, Integer platform, String refid, String positionCode, CouponLimitRo limitRo, boolean filterAvaliabled, boolean onlyResourceId) {
        List<CouponEsIndexDto> couponEsIndexDtos = searchCouponBatchs(requestId, platform, refid, positionCode, limitRo, filterAvaliabled, onlyResourceId, null);
        if (CollectionUtil.isNotEmpty(couponEsIndexDtos)) {
            return couponEsIndexDtos.stream().map(CouponEsIndexDto::getBatchNo).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 搜索优惠券批次号
     *
     * @param requestId    请求id
     * @param platformList     平台
     * @return {@link List}<{@link String}>
     */
    private List<CouponEsIndexDto> searchCouponBatchList(String requestId, List<Integer> platformList, String refid, String positionCode, CouponLimitRo limitRo, boolean filterAvaliabled, boolean onlyResourceId) {
        List<String> platforms = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(platformList)){
            platforms = platformList.stream().map(String::valueOf).collect(Collectors.toList());
        }

        List<CouponEsIndexDto> couponEsIndexDtos = searchCouponBatchs(requestId, null, refid, positionCode, limitRo, filterAvaliabled, onlyResourceId, platforms);
        return couponEsIndexDtos;
    }

    private List<CouponEsIndexDto> searchCouponBatchs(String requestId, Integer platform, String refid, String positionCode, CouponLimitRo limitRo, boolean filterAvaliabled, boolean onlyResourceId, List<String> platforms) {
        CouponBatchQueryDto queryDto = CouponBatchQueryDto.builder()
                .requestId(requestId)
                .positionCode(positionCode)
                .refid(refid)
                .limitData(limitRo)
                .onlyResourceId(onlyResourceId)
                .filterAvaliabled(filterAvaliabled)
                .build();

        if (platform != null){
            queryDto.setPlatform(String.valueOf(platform));
        }
        if (CollectionUtil.isNotEmpty(platforms)){
            queryDto.setPlatforms(platforms);
        }

        return couponSearchService.searchEsBatchList(queryDto);
    }

    //endregion

    /**
     * 充值券
     *
     * @param rechargeRo 充值ro
     * @return {@link CommonResult}<{@link CouponRechargeResponse}>
     */
    @Override
    public CommonResult<CouponRechargeResponse> rechargeCoupon(CouponRechargeRo rechargeRo) {
        // 校验请求参数
        String validMsg = couponCommonService.checkReuqest(rechargeRo);
        if (StringUtils.isNotBlank(validMsg)) {
            return CommonResult.failed(validMsg);
        }
        String tradeNo = StringUtils.isBlank(rechargeRo.getTradeNo()) ? IdUtil.fastSimpleUUID() : rechargeRo.getTradeNo();
        SkyLogUtils.infoByMethod(log, "rechargeCoupon->tradeNo:" + tradeNo, "", rechargeRo.getRequestId());
        //发放优惠券
        CouponRechargeDto rechargeDto = CouponRechargeDto.builder()
                .batchNo(rechargeRo.getBatchNos())
                .refId(rechargeRo.getRefid())
                .from("la-rechargeCoupon")
                .platform(rechargeRo.getPlatform().toString())
                .memberId(rechargeRo.getMemberId())
                .openId(rechargeRo.getOpenId())
                .unionId(rechargeRo.getUnionId())
                .deviceId(rechargeRo.getDeviceId())
                .sendTime(LocalDateTime.now())
                .tradeNo(IdUtil.fastSimpleUUID())
                .build();
        String sign = SignUtils.getSign(CouponConst.SIGN_KEY, (JSONObject) JSON.toJSON(rechargeDto));
        rechargeDto.setSign(sign);
        SkyLogUtils.infoByMethod(log, "rechargeCoupon->rechargeDto:" + JSON.toJSONString(rechargeDto), "", rechargeRo.getRequestId());
        if (rechargeRo.getAsync() != null && rechargeRo.getAsync()) {
            //异步发送
            turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, JSON.toJSONString(rechargeDto));
            CouponRechargeResponse response = new CouponRechargeResponse();
            response.setTradeNo(tradeNo);
            SkyLogUtils.infoByMethod(log, "rechargeCoupon->异步发送成功", "", rechargeRo.getRequestId());
            return CommonResult.success(response);
        } else {
            //同步发送
            CommonResultDto rechargeResult = couponRechargeService.recharge(rechargeDto, false);
            SkyLogUtils.infoByMethod(log, "rechargeCoupon->发送成功" + JSON.toJSONString(rechargeResult), "", rechargeRo.getRequestId());
            CouponRechargeResponse response = new CouponRechargeResponse();
            response.setTradeNo(tradeNo);
            response.setRechargeResult(rechargeResult);
            if (rechargeResult.getIsSuccess()) {
                return CommonResult.success(response);
            } else {
                CommonResult<CouponRechargeResponse> commonResult = CommonResult.failed(rechargeResult.getMessage());
                commonResult.setData(response);
                return commonResult;
            }
        }
    }

    /**
     * 下单查询
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponOrderQueryResponse}>
     */
    @Override
    public CommonResult<CouponOrderQueryResponse> queryCouponOrderList(CouponOrderQueryRo queryRo) {

        CompletableFuture<List<CouponInfoDetailExtDto>> couponFuture = CompletableFuture.supplyAsync(() -> {
            //查询鲲鹏下单查询接口
            CouponOrderListRequest couponOrderListRo = new CouponOrderListRequest();
            couponCommonService.getExternalRequest(queryRo, couponOrderListRo);
            couponOrderListRo.setOrderAmount(queryRo.getOrderAmount());
            couponOrderListRo.setChannel(queryRo.getPlatform());

            if (PlatformEnum.Wx_App.getCode().equals(String.valueOf(queryRo.getPlatform()))) {
                couponOrderListRo.setSource(1);
                couponOrderListRo.setUserkey(queryRo.getUnionId());
            } else if (PlatformEnum.App_Android.getCode().equals(String.valueOf(queryRo.getPlatform()))
                    || PlatformEnum.App_IOS.getCode().equals(String.valueOf(queryRo.getPlatform()))) {
                couponOrderListRo.setSource(2);
                couponOrderListRo.setUserkey(queryRo.getMemberId().toString());
            }

            CouponCommonResult<List<CouponInfoDetailExtDto>> couponOrderList = couponCoreService.getCouponOrderList(couponOrderListRo);
            if (couponOrderList == null || couponOrderList.getSuccess() == null) {
                return null;
            }
            return couponOrderList.getResult();
        });

        CompletableFuture<List<String>> couponBatchRuleFeatureFuture = CompletableFuture.supplyAsync(() -> {
            //根据限制条件查询本地可用红包批次号
            return searchCouponBatchNos(queryRo.getRequestId()
                    , queryRo.getPlatform()
                    , ""
                    , ""
                    , queryRo.getLimitData()
                    , false
                    , false);
        });

        CompletableFuture.allOf(couponFuture, couponBatchRuleFeatureFuture).join();

        List<CouponInfoDetailExtDto> detailExtDtos = couponFuture.join();
        List<String> batchNos = couponBatchRuleFeatureFuture.join();
        if (CollectionUtil.isEmpty(detailExtDtos) || CollectionUtil.isEmpty(batchNos)) {
            return CommonResult.failed("空数据");
        }

        if (StringUtils.isNotBlank(queryRo.getCouponCode())) {
            //如果传了红包号，就只返回这个红包
            detailExtDtos = detailExtDtos.stream()
                    .filter(s -> s.getCouponCode().equals(queryRo.getCouponCode()) && batchNos.contains(s.getBatchNo()))
                    .collect(Collectors.toList());
        } else {
            detailExtDtos = detailExtDtos.stream()
                    .filter(s -> batchNos.contains(s.getBatchNo()))
                    .collect(Collectors.toList());
        }
        //过滤不符合规则的数据
        List<CouponVo> couponVoList = couponAppQueryService.convertCouponVoWithEs(detailExtDtos, false, null);

        if (CollectionUtil.isEmpty(couponVoList)) {
            return CommonResult.success();
        }

        //过滤掉无效红包
        LocalDateTime dtNow = LocalDateTime.now();
        couponVoList.removeIf(s -> {
            //将不在有效期范围内的过滤掉
            if (s.getEndDate() == null || s.getEndDate().isBefore(dtNow)) {
                return true;
            }
            if (s.getBeginDate() == null || s.getBeginDate().isAfter(dtNow)) {
                return true;
            }
            //红包状态不是未使用的过滤掉
            if (!s.getStatus().equals(CouponExtStatusEnum.Unused.getCode())) {
                return true;
            }
            return false;
        });

        //增加红包类型和规则信息
        couponVoList.forEach(couponVo -> {
            //获取本地红包批次信息，取红包批次类型，商家或平台
            CouponBatchCacheDto couponBatch = couponQueryService.getBatchDetail(couponVo.getBatchNo(), false);
            if (couponBatch != null && couponBatch.getListInfo() != null) {
                couponVo.setBatchType(couponBatch.getListInfo().getType());
            }
            if (couponBatch != null && couponBatch.getRule() != null) {
                couponVo.setRule(BeanUtil.copyProperties(couponBatch.getRule(), CouponResourceRuleInfo.class));
            }
        });

        return CommonResult.success(CouponOrderQueryResponse.builder()
                .couponList(couponVoList)
                .build());
    }

    /**
     * 占用优惠券
     *
     * @param occupyRo 占领ro
     * @return {@link CommonResult}
     */
    @Override
    public CommonResult occupyCoupon(CouponOccupyRo occupyRo) {
        CouponOccupyRequest occupyRequest = CouponOccupyRequest.builder()
                .orderAmount(occupyRo.getOrderAmount())
                .couponNo(occupyRo.getCouponCode())
                .build();
        couponCommonService.getExternalRequest(occupyRo, occupyRequest);
        CouponCommonResult<CouponOccupyExtDto> occupyResponse = couponCoreService.occupyCoupon(occupyRequest);
        //TODO:记录日志
        if (occupyResponse == null || occupyResponse.getSuccess() == null) {
            return CommonResult.failed("空数据");
        }
        if (occupyResponse.getSuccess()) {
            return CommonResult.success();
        } else {
            return CommonResult.failed(occupyResponse.getMsg());
        }
    }

    /**
     * 恢复占用
     *
     * @param recoverRo 恢复ro
     * @return {@link Boolean}
     */
    @Override
    public CommonResult recoverCoupon(CouponRecoverRo recoverRo) {
        CouponRecoverRequest occupyRequest = CouponRecoverRequest.builder()
                .couponNo(recoverRo.getCouponCode())
                .build();
        couponCommonService.getExternalRequest(recoverRo, occupyRequest);
        CouponCommonResult<CouponCommonResult<Object>> recoverResponse = couponService.couponRecover(occupyRequest);
        //TODO:记录日志
        if (recoverResponse == null || recoverResponse.getSuccess() == null) {
            return CommonResult.failed("空数据");
        }
        if (recoverResponse.getSuccess()) {
            return CommonResult.success();
        } else {
            return CommonResult.failed(recoverResponse.getMsg());
        }
    }

    /**
     * 消费优惠券
     *
     * @param consumeRo 消耗ro
     * @return {@link Boolean}
     */
    @Override
    public CommonResult consumeCoupon(CouponConsumeRo consumeRo) {
        CouponConsumeRequest occupyRequest = CouponConsumeRequest.builder()
                .couponNo(consumeRo.getCouponCode())
                .orderNo(consumeRo.getOrderNo())
                .orderAmount(consumeRo.getOrderAmount())
                .amount(consumeRo.getAmount())
                .build();
        couponCommonService.getExternalRequest(consumeRo, occupyRequest);
        CouponCommonResult<CouponSimpleInfoExtDto> consumeResponse = couponCoreService.couponConsume(occupyRequest);
        //TODO:记录日志
        if (consumeResponse == null || consumeResponse.getSuccess() == null) {
            return CommonResult.failed("空数据");
        }
        if (consumeResponse.getSuccess()) {
            ThreadUtil.execAsync(new Runnable() {
                @Override
                public void run() {
                    try {
                        String batchNo = (consumeResponse.getResult() != null && StringUtils.isNotBlank(consumeResponse.getResult().getBatchNo()))
                                ? consumeResponse.getResult().getBatchNo() : "";
                        couponConsumeRecordService.consumeCoupon(batchNo
                                , consumeRo.getCouponCode()
                                , consumeRo.getOrderAmount()
                                , consumeRo.getAmount());
                    } catch (Exception e) {
                        log.error("consumeCoupon->consumeCoupon saveRecord error", e);
                    }
                }
            });
            return CommonResult.success();
        } else {
            return CommonResult.failed(consumeResponse.getMsg());
        }
    }

    /**
     * 退还优惠券
     *
     * @param returnRo 返回ro
     * @return {@link Boolean}
     */
    @Override
    public CommonResult returnCoupon(CouponReturnRo returnRo) {
        CouponReturnRequest occupyRequest = CouponReturnRequest.builder()
                .couponNo(returnRo.getCouponCode())
                .orderNo(returnRo.getOrderNo())
                .build();
        couponCommonService.getExternalRequest(returnRo, occupyRequest);
        CouponCommonResult<Object> returnResponse = couponCoreService.couponReturn(occupyRequest);
        //TODO:记录日志
        if (returnResponse == null || returnResponse.getSuccess() == null) {
            return CommonResult.failed("空数据");
        }
        if (returnResponse.getSuccess()) {
            ThreadUtil.execAsync(new Runnable() {
                @Override
                public void run() {
                    try {
                        couponConsumeRecordService.returnCoupon(returnRo.getCouponCode());
                    } catch (Exception e) {
                        log.error("returnCoupon->returnCoupon saveRecord error", e);
                    }
                }
            });
            return CommonResult.success();
        } else {
            return CommonResult.failed(returnResponse.getMsg());
        }
    }

    /**
     * 搜索列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}
     */
    @Override
    public CommonResult<List<CouponVo>> searchList(CouponListQueryRo queryRo) {
        long start = System.currentTimeMillis();
        List<String> batchNo = queryRo.getBatchNos();
        if (CollectionUtil.isEmpty(batchNo)) {
            batchNo = searchCouponBatchNos(queryRo.getRequestId(), queryRo.getPlatform(), "", "", queryRo.getLimitData(), false, false);
            log.info("searchByLimit cost:{}", System.currentTimeMillis() - start);
        }

        if (CollectionUtil.isEmpty(batchNo)) {
            return CommonResult.success(Collections.emptyList());
        }
        start = System.currentTimeMillis();
        log.info("BeanUtil.toBean cost:{}", System.currentTimeMillis() - start);

        CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                .queryRo(queryRo)
                .status(CouponExtStatusEnum.Unused)
                .simpleMode(true)
                .limitData(queryRo.getLimitData())
                .batchNos(queryRo.getBatchNos())
                .positionCode("")
                .filterPositionCode(false)
                .build();
        return CommonResult.success(couponAppQueryService.queryUserCoupon(couponQueryDto, true, null));
    }

    /**
     * 获取优惠券详细信息
     *
     * @param detailRo 细节ro
     * @return {@link CommonResult}
     */
    @Override
    public CommonResult<List<CouponVo>> getCouponDetail(CouponDetailRo detailRo) {
        if (CollectionUtil.isEmpty(detailRo.getCouponCodes())) {
            return CommonResult.failed();
        }
        List<CouponVo> couponVoList = new CopyOnWriteArrayList<>();

        detailRo.getCouponCodes().forEach(s -> {
            CouponVo couponDetail = getCouponDetail(detailRo, s);
            if (couponDetail != null) {
                couponVoList.add(couponDetail);
            }
        });

        couponVoList.stream().map(CouponVo::getBatchNo).forEach(s -> {
            //获取本地红包批次信息，取红包批次类型，商家或平台
            CouponBatchCacheDto couponBatch = couponQueryService.getBatchDetail(s, false);
            if (couponBatch != null && couponBatch.getListInfo() != null) {
                couponVoList.stream().filter(m -> m.getBatchNo().equals(s)).forEach(m -> m.setBatchType(couponBatch.getListInfo().getType()));
            }
        });

        return CommonResult.success(couponVoList);
    }

    /**
     * 获取用户优惠券
     *
     * @param userQueryRo 用户查询ro
     * @return {@link CommonResult}<{@link List}<{@link CouponVo}>>
     */
    @Override
    public CommonResult<List<CouponVo>> getUserCouponV2(CouponUserQueryRo userQueryRo) {
        List<CouponVo> couponVos = new CopyOnWriteArrayList<>();
        long start = System.currentTimeMillis();
        if (CollectionUtil.isNotEmpty(userQueryRo.getCouponCodes())) {
            //传了具体的券号，就直接查询并返回
            couponVos = getCouponDetail(userQueryRo, userQueryRo.getCouponCodes());
            SkyLogUtils.infoByMethod(log, "getUserCoupon->batchNos1:" + StringUtils.join(",", userQueryRo.getCouponCodes()), "", userQueryRo.getRequestId());
            return CommonResult.success(couponVos);
        }

        CouponExtStatusEnum status = CouponExtStatusEnum.Unused;
        if (userQueryRo.getStatus() != null) {
            status = EnumUtil.getBy(CouponExtStatusEnum::getCode, userQueryRo.getStatus(), CouponExtStatusEnum.Unused);
        }
        boolean queryLimitWithQueryUserCoupon = false;
        //开始查询
        start = System.currentTimeMillis();

        CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                .queryRo(userQueryRo)
                .status(status)
                .simpleMode(userQueryRo.getSimple() != null && userQueryRo.getSimple())
                .limitData(userQueryRo.getLimitData())
                .batchNos(userQueryRo.getBatchNos())
                .timeOut(userQueryRo.getTimeOut())
                .positionCode(null)
                .filterPositionCode(false)
                .build();
        couponVos = couponAppQueryService.queryUserCoupon(couponQueryDto, queryLimitWithQueryUserCoupon, null);

        // 根据条件过滤已领取红包
        CouponQueryRo couponQueryRo = JSON.parseObject(JSON.toJSONBytes(userQueryRo), CouponQueryRo.class);
        if (!queryLimitWithQueryUserCoupon && CollectionUtil.isNotEmpty(couponVos)){
            couponVos = this.calcCouponListByLimitData(couponVos, couponQueryRo);
        }

        SkyLogUtils.infoByMethod(log, "获取红包列表耗时：" + (System.currentTimeMillis() - start), "", userQueryRo.getRequestId());
        SkyLogUtils.infoByMethod(log, "getUserCoupon->couponVos:" + JSON.toJSONString(couponVos), "", userQueryRo.getRequestId());
        if (CollectionUtil.isEmpty(couponVos)) {
            return CommonResult.success(null, "暂无匹配数据");
        }
        return CommonResult.success(couponVos);
    }

    /**
     * 获取用户优惠券
     *
     * @param userQueryRo 用户查询ro
     * @return {@link CommonResult}<{@link List}<{@link CouponVo}>>
     */
    @Override
    public CommonResult<List<CouponVo>> getUserCoupon(CouponUserQueryRo userQueryRo) {
        SkyLogUtils.infoByMethod(log, "getUserCoupon->start;", "", "");
//        String version = ConfigUtils.get(ConfigConst.COUPON_LIST_API_VERSION);
//        if ("2".equals(version)){
//            return getUserCouponV2(userQueryRo);
//        }
        List<CouponVo> couponVos = new CopyOnWriteArrayList<>();
        long start = System.currentTimeMillis();
        if (CollectionUtil.isNotEmpty(userQueryRo.getCouponCodes())) {
            //传了具体的券号，就直接查询并返回
            couponVos = getCouponDetail(userQueryRo, userQueryRo.getCouponCodes());
            SkyLogUtils.infoByMethod(log, "getUserCoupon->batchNos1:" + StringUtils.join(",", userQueryRo.getCouponCodes()), "", userQueryRo.getRequestId());
            return CommonResult.success(couponVos);
        }

        CouponExtStatusEnum status = CouponExtStatusEnum.Unused;
        if (userQueryRo.getStatus() != null) {
            status = EnumUtil.getBy(CouponExtStatusEnum::getCode, userQueryRo.getStatus(), CouponExtStatusEnum.Unused);
        }

        //开始查询
        start = System.currentTimeMillis();

        CouponQueryDto couponQueryDto = CouponQueryDto.builder()
                .queryRo(userQueryRo)
                .status(status)
                .simpleMode(userQueryRo.getSimple() != null && userQueryRo.getSimple())
                .limitData(userQueryRo.getLimitData())
                .batchNos(userQueryRo.getBatchNos())
                .timeOut(userQueryRo.getTimeOut())
                .positionCode(null)
                .filterPositionCode(false)
                .build();
        couponVos = couponAppQueryService.queryUserCoupon(couponQueryDto, true, null);
        long cost = System.currentTimeMillis() - start;
        SkyLogUtils.infoByMethod(log, "获取红包列表耗时：" + cost, "", userQueryRo.getRequestId());
        SkyLogUtils.infoByMethod(log, "getUserCoupon->couponVos:" + JSON.toJSONString(couponVos), "", userQueryRo.getRequestId());
        if (cost > 30){
            SkyLogUtils.warnByMethod(log, "getUserCoupon->cost30:" + cost, "", userQueryRo.getRequestId());
        }
        if (cost > 60){
            SkyLogUtils.warnByMethod(log, "getUserCoupon->cost60:" + cost, "", userQueryRo.getRequestId());
        }
        if (cost > 90){
            SkyLogUtils.warnByMethod(log, "getUserCoupon->cost90:" + cost, "", userQueryRo.getRequestId());
        }
        if (CollectionUtil.isEmpty(couponVos)) {
            return CommonResult.success(null, "暂无匹配数据");
        }
        return CommonResult.success(couponVos);
    }

    /**
     * 点位红包查询
     *
     * @param couponPositionQueryRo 位置查询ro
     * @return
     */
    @Override
    public CommonResult<CouponPositionVo> couponPosition(CouponPositionQueryRo couponPositionQueryRo) {
        OperationPosition operationPosition = operationPositionService.getByCode(couponPositionQueryRo.getPositionCode());
        if (operationPosition == null) {
            return CommonResult.failed("点位不存在");
        }
        CouponPositionVo couponPositionVo = new CouponPositionVo();
        couponPositionVo.setOperationPosition(BeanUtil.copyProperties(operationPosition, OperationPositionVo.class));
        return CommonResult.success(couponPositionVo);
    }

    @Override
    public CommonResult<List<CouponBatchVo>> queryAllCouponList(AllCouponQueryRo queryRo) {
        SkyLogUtils.infoByMethod(log, "queryAllCouponList->start;", "", "");
        long getCouponBatchStart = System.currentTimeMillis();
        // 查询所有可领红包红包
        List<CouponEsIndexDto> canTakeCouponList;
        if (CollectionUtil.isEmpty(queryRo.getBatchNos())){
            canTakeCouponList = searchCouponBatchList(queryRo.getRequestId()
                    , queryRo.getPlatforms(), null, queryRo.getPositionCode(), queryRo.getLimitData(), true, false);
        }else {
            canTakeCouponList = couponSearchService.searchEsByBatchNos(queryRo.getBatchNos());
        }
        List<CouponBatchVo> couponBatchList = new ArrayList<>();

        // 无数据直接返回
        if (CollectionUtil.isEmpty(canTakeCouponList)){
            return CommonResult.success(couponBatchList);
        }

        if (CollectionUtil.isNotEmpty(canTakeCouponList)) {
            queryRo.setBatchNos(canTakeCouponList.stream().map(CouponEsIndexDto::getBatchNo).collect(Collectors.toList()));
            couponBatchList = this.getCouponBatch(queryRo, true);
        }

        SkyLogUtils.infoByMethod(log, MessageFormat.format("queryAllCouponList->batchFuture cost:{0};thread:{1};", (System.currentTimeMillis() - getCouponBatchStart), Thread.currentThread().getId()), "", "");

        // 计算红包是否可领取状态
        setCouponBatchStatus(couponBatchList, null);

        // 过滤掉不可领取 及 refid不匹配 的红包
        if (CollectionUtil.isNotEmpty(couponBatchList)) {
            couponBatchList = couponBatchList.stream().filter(s -> CouponBatchApiStatusEnum.Available.getCode().equals(s.getStatus())).collect(Collectors.toList());
        }

        // 未领取 批次号排序
        if (CollectionUtil.isNotEmpty(couponBatchList)) {
            couponBatchList.sort(Comparator.comparing(CouponBatchVo::getBatchNo));
        }

        return CommonResult.success(couponBatchList);
    }

    /**
     * 获取优惠券详细信息
     *
     * @param couponRo   优惠券ro
     * @param couponCode 优惠券代码
     * @return {@link CouponVo}
     */
    private CouponVo getCouponDetail(BaseCouponRo couponRo, String couponCode) {
        CouponDetailRequest couponQueryRo = new CouponDetailRequest();
        couponCommonService.getExternalRequest(couponRo, couponQueryRo);
        couponQueryRo.setCouponNo(couponCode);
        CouponCommonResult<CouponInfoDetailExtDto> couponDetail = couponCoreService.getCouponDetail(couponQueryRo);
        if (couponDetail != null && couponDetail.getResult() != null) {
            CouponBatchCacheDto batchDetail = couponQueryService.getBatchDetail(couponDetail.getResult().getBatchNo(), false);
            return CouponVo.fromExt(couponDetail.getResult(), batchDetail, false);
        }
        return null;
    }

    private List<CouponVo> getCouponDetail(BaseCouponRo couponRo, List<String> couponCodes) {
        List<CouponVo> couponVoList = new CopyOnWriteArrayList<>();
        if (CollectionUtil.isEmpty(couponCodes)) {
            return couponVoList;
        }

        couponCodes.forEach(s -> {
            if (StringUtils.isEmpty(s)) {
                return;
            }
            CouponVo couponDetail = getCouponDetail(couponRo, s);
            if (couponDetail != null) {
                couponVoList.add(couponDetail);
            }
        });

        return couponVoList;
    }
}
