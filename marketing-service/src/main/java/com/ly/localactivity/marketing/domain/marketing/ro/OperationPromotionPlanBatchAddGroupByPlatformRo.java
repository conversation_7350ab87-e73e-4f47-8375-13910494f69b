package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPromotionPlanBatchEditRo
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@ApiModel("推广计划批量新增对象,按照平台分类")
@Data
public class OperationPromotionPlanBatchAddGroupByPlatformRo {
    @ApiModelProperty(value = "运营点位Id", required = true)
    @NotNull(message = "运营点位Id不能为空")
    private Long positionId;
    @ApiModelProperty(value = "运营点位Code", required = true)
    @NotBlank(message = "运营点位Code不能为空")
    private String positionCode;
    @ApiModelProperty(value = "平台 数据字典 Code:platform", required = true)
    @NotNull(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "输出形式", required = true)
    @NotNull(message = "输出形式不能为空")
    private Integer outputType;
    @ApiModelProperty(value = "是否分开维护", required = true)
    private Boolean defaultIsSeparate;
    @ApiModelProperty(value = "推广方式内容集合,按照地区进行分类(按照推广地区分开维护则为多条)", required = true)
    @NotEmpty(message = "推广方式内容集合不能为空")
    @Valid
    private List<OperationPromotionPlanEditRo> infos;
}
