package com.ly.localactivity.marketing.service.coupon.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.utils.LocalDateTimeUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchStockLimit;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchStockLimitMapper;
import com.ly.localactivity.marketing.service.coupon.ICouponBatchStockControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * CouponBatchStockControlServiceImpl
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Slf4j
@Service
public class CouponBatchStockControlServiceImpl implements ICouponBatchStockControlService {
    @Resource
    private CouponBatchStockLimitMapper couponBatchStockLimitMapper;

    /**
     * 设置优惠券库存限制
     *
     * @param batchNo 批号
     */
    @Override
    public void setCouponStockLimit(String batchNo) {
        SkyLogUtils.infoByMethod(log, StringUtils.format("setCouponStockLimit batchNo:{}", batchNo), batchNo, "");
        if (StringUtils.isBlank(batchNo)) {
            return;
        }
        CouponBatchStockLimit limitData = getLimitDataAndSave(batchNo);
        limitData.setStockLimitFlag(true);
        limitData.setDayLimitFlag(false);
        limitData.setDayValue(LocalDateTimeUtils.getDefault());
        limitData.setModifiedTime(LocalDateTime.now());
        couponBatchStockLimitMapper.updateById(limitData);
    }

    /**
     * 设置优惠券日限制
     *
     * @param batchNo 批号
     */
    @Override
    public void setCouponDayLimit(String batchNo) {
        SkyLogUtils.infoByMethod(log, StringUtils.format("setCouponDayLimit batchNo:{}", batchNo), batchNo, "");
        if (StringUtils.isBlank(batchNo)) {
            return;
        }
        CouponBatchStockLimit limitData = getLimitDataAndSave(batchNo);
        limitData.setStockLimitFlag(false);
        limitData.setDayLimitFlag(true);
        limitData.setDayValue(LocalDate.now().atStartOfDay());
        limitData.setModifiedTime(LocalDateTime.now());
        couponBatchStockLimitMapper.updateById(limitData);
    }

    /**
     * 判断批次是否受限
     *
     * @param batchNo 批号
     * @return {@link Boolean}
     */
    @Override
    public Boolean isStockLimit(String batchNo) {
        CouponBatchStockLimit limitData = getLimitData(batchNo);
        if (limitData == null) {
            return false;
        }

        //总库存受限
        if (limitData.getStockLimitFlag() != null && limitData.getStockLimitFlag()) {
            return true;
        }

        if (limitData.getDayLimitFlag() != null && limitData.getDayLimitFlag()) {
            //当日受限，且日期是当天
            if (LocalDateTime.now().toLocalDate().isEqual(limitData.getDayValue().toLocalDate())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 刷新限制
     *
     * @param couponBatchExtDto 优惠券批量ext dto
     * @param clearDayLimitFlag 清除每日限制标志
     */
    @Override
    public void refreshLimit(CouponBatchExtDto couponBatchExtDto, Boolean clearDayLimitFlag) {
        if (couponBatchExtDto == null || StringUtils.isBlank(couponBatchExtDto.getBatchNo())) {
            return;
        }
        SkyLogUtils.infoByMethod(log, "刷新库存限制:" + couponBatchExtDto.getBatchNo(), couponBatchExtDto.getBatchNo(), "");
        CouponBatchStockLimit limitData = getLimitData(couponBatchExtDto.getBatchNo());
        if (limitData == null) {
            limitData = new CouponBatchStockLimit();
            limitData.setBatchNo(couponBatchExtDto.getBatchNo());
            limitData.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            limitData.setCreator(StringUtils.EMPTY);
            limitData.setCreateTime(LocalDateTime.now());
            limitData.setModifier(StringUtils.EMPTY);
            limitData.setModifiedTime(LocalDateTime.now());
            couponBatchStockLimitMapper.insert(limitData);
            return;
        }
        //判断总库存
        //剩余库存为0时，设置库存限制
        limitData.setStockLimitFlag(couponBatchExtDto.getRemainStock() == null || couponBatchExtDto.getRemainStock() <= 0);

        if (limitData.getDayLimitFlag()) {
            if(clearDayLimitFlag){
                limitData.setDayLimitFlag(false);
            }
            if (limitData.getDayValue().toLocalDate().isBefore(LocalDate.now())){
                //如果是当日限制，并且已经过了一天了，需要将每日领取限制清掉
                limitData.setDayLimitFlag(false);
                limitData.setDayValue(LocalDateTimeUtils.getDefault());
            }
        }
        couponBatchStockLimitMapper.updateById(limitData);
    }

    /**
     * 重置优惠券库存限额
     *
     * @param batchNo 批号
     */
    private void resetCouponStockLimit(String batchNo) {
        SkyLogUtils.infoByMethod(log, StringUtils.format("resetCouponStockLimit batchNo:{}", batchNo), batchNo, "");
        if (StringUtils.isBlank(batchNo)) {
            return;
        }

        CouponBatchStockLimit limitData = getLimitDataAndSave(batchNo);
        limitData.setStockLimitFlag(false);
        limitData.setDayLimitFlag(false);
        limitData.setDayValue(LocalDateTimeUtils.getDefault());
        limitData.setModifiedTime(LocalDateTime.now());
        couponBatchStockLimitMapper.updateById(limitData);
    }

    //TODO:每日同步红包库存限制，如果红包库存变化，需要打开限制，并且刷新es数据

    private CouponBatchStockLimit getLimitData(String batchNo) {
        LambdaQueryWrapper<CouponBatchStockLimit> wrapper = new LambdaQueryWrapper<CouponBatchStockLimit>()
                .eq(CouponBatchStockLimit::getBatchNo, batchNo)
                .last("limit 1");
        return couponBatchStockLimitMapper.selectOne(wrapper);
    }

    private CouponBatchStockLimit getLimitDataAndSave(String batchNo) {
        CouponBatchStockLimit limitData = getLimitData(batchNo);
        if (limitData == null) {
            limitData = new CouponBatchStockLimit();
            limitData.setBatchNo(batchNo);
            limitData.setCreateTime(LocalDateTime.now());
            limitData.setCreator("");
            limitData.setModifiedTime(LocalDateTime.now());
            limitData.setModifier("");
            limitData.setDeleteFlag(false);
            couponBatchStockLimitMapper.insert(limitData);
        }
        return limitData;
    }
}
