package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperationChannel;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationChannelDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包批次运营渠道配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchOperationChannelService extends IService<CouponBatchOperationChannel> {

    /**
     * 保存或更新
     * @param couponBatchList
     * @param couponBatchOperationChannelList
     * @param couponBatchOperationList
     * @return
     */
    Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchOperationChannelDto> couponBatchOperationChannelList, List<CouponBatchOperationDto> couponBatchOperationList);

    /**
     * 根据红包id查询
     * @param couponBatchIdList
     * @return
     */
    Map<Long, List<CouponBatchOperationChannelDto>> selectByCouponBatchId(List<Long> couponBatchIdList);

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    Boolean deleteByIds(List<Long> invalidIdList);
}
