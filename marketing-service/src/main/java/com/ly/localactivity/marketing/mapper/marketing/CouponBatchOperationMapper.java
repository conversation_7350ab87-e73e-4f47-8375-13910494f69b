package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperation;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 红包批次运营配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface CouponBatchOperationMapper extends MyBaseMapper<CouponBatchOperation> {
    /**
     * 根据运营点位查询有效的红包批次号
     *
     * @param dtNow        dt现在
     * @param platform     站台
     * @param positionCode 运营点位code
     * @return {@link List}<{@link String}>
     */
    List<String> listBatchNoByOperationCode(@Param("dtNow") LocalDateTime dtNow
            , @Param("platform") String platform
            , @Param("positionCode") String positionCode);
}
