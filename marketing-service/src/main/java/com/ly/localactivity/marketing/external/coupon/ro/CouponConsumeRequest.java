package com.ly.localactivity.marketing.external.coupon.ro;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
 * CouponConsumeRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponConsumeRo", description = "优惠券核销")
@AllArgsConstructor
@NoArgsConstructor
@Data
@SuperBuilder
public class CouponConsumeRequest extends CouponBaseRequest {
    /**
     * 券号
     */
    @ApiModelProperty(value = "券码", required = true)
    private String couponNo;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;
    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额", required = true)
    private BigDecimal orderAmount;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal amount;

    /**
     * 特殊规则
     * 根据不同的项目传参不同
     */
    @ApiModelProperty(value = "特殊规则,根据不同的项目传参不同")
    private JSONObject extRuleInfo;

    /**
     * 子渠道
     */
    @ApiModelProperty(value = "子渠道")
    private Integer subChannel;
    /**
     * 乘机人信息
     */
    @ApiModelProperty(value = "乘机人信息")
    private List<PersonInfo> consumeUsers;
    /**
     * 传当地时区的时间，国际券必传,(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty(value = "传当地时区的时间，国际券必传")
    private String consumeTime;
    /**
     * 消费币种——实际结算时所付币种，国际站必传
     */
    @ApiModelProperty(value = "消费币种——实际结算时所付币种，国际站必传")
    private String consumeCurrency;
    /**
     * 券币种——页面展示给用的币种，国际站必传
     */
    @ApiModelProperty(value = "券币种——页面展示给用的币种，国际站必传")
    private String couponCurrency;
    /**
     * 优惠金额(人民币)(国际红包传使用人民币，本接口不做校验，自行校验金额是否符合)
     */
    @ApiModelProperty(value = "优惠金额(人民币)(国际红包传使用人民币，本接口不做校验，自行校验金额是否符合)")
    private BigDecimal cnyAmount;
    /**
     * 项目特殊规则是否校验：默认 0 校验；1 不校验
     * <p>
     * 券基础信息依然校验不受影响，包括：有效期，状态，门槛，抵扣金额，二要素，使用渠道，使用项目，
     */
    @ApiModelProperty(value = "项目特殊规则是否校验：默认 0 校验；1 不校验")
    private Integer isCheckRule;
}
