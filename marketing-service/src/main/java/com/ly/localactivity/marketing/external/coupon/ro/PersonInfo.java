package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PersonInfo", description = "用卷人")
public class PersonInfo {
    /**
     * 人员类型，枚举值：
     * 成人：0
     * <p>
     * 儿童：1
     * <p>
     * 婴儿：2
     */
    @ApiModelProperty(value = "人员类型")
    private Integer type;
    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String name;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String certType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String certNo;
}