package com.ly.localactivity.marketing.mapper.marketing;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionListRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 营销运营位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface OperationPositionMapper extends BaseMapper<OperationPosition> {

    /**
     * 分页查询运营点位列表
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return {@link IPage}<{@link OperationPositionVo}>
     */
    IPage<OperationPositionVo> listOperationPositionPage(Page<OperationPositionVo> page, @Param("query") OperationPositionListRo query);

}
