package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OperationPositionModelEnum
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@AllArgsConstructor
@Getter
public enum OperationPositionModelEnum {
    /**
     * Banner,1
     */
    BANNER(1, "Banner"),

    /**
     * feed流,2
     */
    FEED(2, "feed流"),
    /**
     * 金刚位,3
     */
    ICON(3, "金刚位"),
    /**
     * 弹层,4
     */
    POPUP(4, "弹层"),
    /**
     * 浮层,5
     */
    FLOAT_LAYER(5, "浮层"),
    /**
     * toast,6
     */
    TOAST(6, "toast"),
    /**
     * 氛围图,7
     */
    ATMOSPHERE(7, "氛围图"),
    /**
     * 卡片tab,8
     */
    CARD_TAB(8, "卡片tab"),
    /**
     * 热搜关键词,9
     */
    HOT_SEARCH_KEYWORD(9, "热搜关键词"),
    /**
     * 运营位,10
     */
    OPERATION_POSITION(10, "运营位"),
    /**
     * 红包弹层,11
     */
    RED_PACKET_POPUP(11, "红包弹层"),
    /**
     * 红包运营位,12
     */
    RED_PACKET_OPERATION_POSITION(12, "红包运营位"),
    /**
     * IM,13
     */
    IM(13, "IM(咨询)"),
    ;
    private final Integer code;
    private final String name;

}
