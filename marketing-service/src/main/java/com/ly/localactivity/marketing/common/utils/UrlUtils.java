package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.map.MapUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * UrlTemplateUtils
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
public class UrlUtils {
    private static final String[] FILTER_KEYS = {"cityId", "cityName", "resourceId"};
    private static final String WXREFID = "wxrefid";
    private static final String REFID = "refid";

    public static Map<String,String> getUrlMap(Map<String,String> urlParamMap,String urlQuery){
        Map<String, String> urlParams = new HashMap<>();
        if(MapUtil.isNotEmpty(urlParamMap)){
            urlParams = MapUtil.newConcurrentHashMap(urlParamMap);
        } else if (StringUtils.isNotBlank(urlQuery)) {
            urlParams = StringUtils.getUrlParams(urlQuery);
        }
        filterUrlParams(urlParams);
        return urlParams;
    }

    private static void filterUrlParams(Map<String, String> urlParams) {
        if (MapUtil.isNotEmpty(urlParams)) {
            //将系统级参数过滤掉
            Arrays.stream(FILTER_KEYS).parallel().forEach(urlParams::remove);
            //兼容wxrefid，如果有wxrefid优先使用wxrefid
            if (urlParams.containsKey(WXREFID)) {
                String wxRefid = urlParams.get(WXREFID);
                if (StringUtils.isNotBlank(wxRefid)){
                    urlParams.put(REFID, wxRefid);
                }
            }
        }
    }

}
