package com.ly.localactivity.marketing.application.model.vo;

import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.common.validation.group.PlatformValidation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * CouponExportVo
 *
 * <AUTHOR>
 * @date 2024/6/7
 */
@Data
public class CouponExportVo implements Serializable {
    private static final long serialVersionUID = 1314559020394994700L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 红包类型，1-平台红包，2-商家红包
     */
    @ApiModelProperty(value = "红包类型，1-平台红包，2-商家红包")
    private String typeName;

    /**
     * 日游产品品类
     */
    @ApiModelProperty(value = "日游产品品类")
    private Integer productCategoryId;

    /**
     * 红包名称
     */
    @ApiModelProperty(value = "红包名称")
    private String name;

    /**
     * 营销状态 1-未生效，2-活动中，3-未开始，4-已过期，5-已下架
     */
    @ApiModelProperty(value = "营销状态 1-未生效，2-活动中，3-未开始，4-已过期，5-已下架")
    private String marketingStatusName;
    /**
     * 供应商id，对应common库的supplier表.supplierId
     */
    @ApiModelProperty(value = "供应商id，对应common库的supplier表.supplierId")
    private Long supplierId;

    private String supplierName;

    @ApiModelProperty(value = "外部批次编码，鲲鹏批次号")
    private String batchNo;
    @ApiModelProperty(value = "红包内部名称")
    private String batchName;
    @ApiModelProperty(value = "红包名称")
    private String batchCname;

    @ApiModelProperty("卡劵类型 0-满减，1-折扣，2-满减阶梯")
    private String couponTypeName;

    private String notice;

    @ApiModelProperty(value = "活动有效期")
    private String eventValidDateRange;

    @ApiModelProperty(value = "使用有效期")
    private String validDateRange;

    @ApiModelProperty("是否可退 鲲鹏枚举 0 可退 1 不可退")
    private String isRefund;

    @ApiModelProperty("库存")
    @Range(min = 1, max = 10000, message = "库存配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer stock;

    @ApiModelProperty("已领取数量")
    private Integer receiveStock;

    @ApiModelProperty("投放渠道")
    private String platForm;

    /**
     * 数据id，对应地区id、品类id、商品id
     */
    @ApiModelProperty(value = "数据id，对应地区id、品类id、商品id")
    private String productRange;



}
