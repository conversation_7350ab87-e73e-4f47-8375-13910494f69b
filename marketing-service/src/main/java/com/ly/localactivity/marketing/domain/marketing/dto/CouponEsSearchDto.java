package com.ly.localactivity.marketing.domain.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * CouponEsSearchDto
 * es查询模板文档： https://toca.17u.cn/wiki?fid=08d9bad408b447bea78c5777ec026164
 *
 * <AUTHOR>
 * @date 2024/5/21
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponEsSearchDto extends CouponEsBaseSearchDto {
    /**
     * 平台，必传
     */
    private String platforms;
    /**
     * 当前时间戳，必传
     */
    private Long dateNow;

    /**
     * 运营点位
     */
    private String positionCodes;
    /**
     * refids
     */
    private String refids;
    /**
     * 限制数据，例 xx OR xx
     */
    private String limitDatas;

    /**
     * 限制类型，不必传
     */
    private Integer limitType;

    private Boolean queryLimit;

    // 资源限制数据
    private String productLimitDatas;

    // 出发城市限制数据
    private String departureLimitDatas;

    // 供应商限制类型
    private Integer supplierLimitType;

    // 供应商数据
    private String supplierLimitDatas;

    // 平台限制类型
    private Integer platformLimitType;

    /**
     * 供应商id 例 xx OR xx
     */
    private String supplierIds;

    private Integer firstCategoryId;

    private Integer batchType;

    private String requestId;

    private Integer status;

    private Integer seconds;

    private Integer threadCount;

    public static CouponEsSearchDto generate(int limit,boolean filterDate){
        CouponEsSearchDto build = CouponEsSearchDto.builder()
                .from(0)
                .size(limit)
                .build();
        if (filterDate){
            build.setDateNow(System.currentTimeMillis());
        }
        return build;
    }
}
