package com.ly.localactivity.marketing.application.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponBatchExtendInfo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponBatchExtendInfo {
    /**
     * 属性id(1 币种)(2 酒店阶梯红包)（3.鲲鹏阶梯）(4.度假红包-出团时间)(5.度假红包-回团时间)
     */
    @ApiModelProperty(value = "属性id(1 币种)(2 酒店阶梯红包)（3.鲲鹏阶梯）(4.度假红包-出团时间)(5.度假红包-回团时间)")
    private Integer attrId;
    /**
     * attrid=1:币种值——USD 美元，THB泰铢
     * attrid=3: 1：面额 2：票量 3:满额立减 4:满人立减 
     */
    @ApiModelProperty(value = "attrid=1:币种值——USD 美元，THB泰铢 attrid=3: 1：面额 2：票量 3:满额立减 4:满人立减 ")
    private String attrValue;
    /**
     * attrid=1:对应币种门槛金额
     * attrid=3 : ( attrValue=1 面额门槛，attrValue=2 票量门槛 attrValue=3 满额立减门槛，attrValue=4 满人立减 门槛）
     * attrid=4: 出团开始时间
     * attrid=5: 回团开始时间
     */
    @ApiModelProperty(value = "attrid=1:对应币种门槛金额 attrid=3 : ( attrValue=1 面额门槛，attrValue=2 票量门槛 attrValue=3 满额立减门槛，attrValue=4 满人立减 门槛） attrid=4: 出团开始时间 attrid=5: 回团开始时间")
    private String leftValue;
    /**
     * attrid=1:对应币种抵扣金额
     * attrid=3 （attrValue=1 对应红包金额/折扣率,attrValue=2 红包金额/折扣率 attrValue=3 满额立减金额，attrValue=4 满人立减金额）
     * attrid=4: 出团结束时间
     * attrid=5: 回团结束时间
     */
    @ApiModelProperty(value = "attrid=1:对应币种抵扣金额 attrid=3 （attrValue=1 对应红包金额/折扣率,attrValue=2 红包金额/折扣率 attrValue=3 满额立减金额，attrValue=4 满人立减金额） attrid=4: 出团结束时间 attrid=5: 回团结束时间")
    private String rightValue;
    /**
     * attrid=1:对应币种红包名称
     * attrid=3:对应最高折扣金额
     */
    @ApiModelProperty(value = "attrid=1:对应币种红包名称 attrid=3:对应最高折扣金额")
    private String ext1;
    /**
     * attrid=1:对应币种使用说明
     */
    @ApiModelProperty(value = "attrid=1:对应币种使用说明")
    private String ext2;
}
