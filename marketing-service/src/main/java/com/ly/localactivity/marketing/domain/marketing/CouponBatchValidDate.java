package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 红包批次有效期
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_batch_valid_date")
public class CouponBatchValidDate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包批次id
     */
    @TableField("coupon_batch_id")
    private Long couponBatchId;

    /**
     * 红包批次明细id
     */
    @TableField("coupon_batch_detail_id")
    private Long couponBatchDetailId;

    /**
     * 使用有效期类型，1-固定日期段，2-动态有效期，3-倒计时(小时)，4-倒计时(分钟)
     */
    @TableField("valid_date_type")
    private Integer validDateType;

    /**
     * 使用有效期开始
     */
    @TableField("valid_begin_date")
    private LocalDateTime validBeginDate;

    /**
     * 使用有效期结束
     */
    @TableField("valid_end_date")
    private LocalDateTime validEndDate;

    /**
     * 倒计时小时
     */
    @TableField("countdown_hour")
    private Integer countdownHour;

    /**
     * 倒计时分钟
     */
    @TableField("countdown_minute")
    private Integer countdownMinute;

    /**
     * 动态生效天数，x天开始生效
     */
    @TableField("dynamic_start_day")
    private Integer dynamicStartDay;

    /**
     * 动态有效时长
     */
    @TableField("dynamic_duration_day")
    private Integer dynamicDurationDay;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
