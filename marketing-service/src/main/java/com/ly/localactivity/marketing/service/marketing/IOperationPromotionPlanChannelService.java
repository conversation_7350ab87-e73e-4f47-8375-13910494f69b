package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanChannel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanChannelVo;

import java.util.List;

/**
 * <p>
 * 运营位平台 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
public interface IOperationPromotionPlanChannelService extends IService<OperationPromotionPlanChannel> {

    /**
     * 保存渠道
     *
     * @param promotionPlan 推广计划
     * @param channels      渠道
     * @param modifier      修改人
     */
    void saveOrUpdateChannelList(OperationPromotionPlan promotionPlan, List<OperationPromotionPlanChannelVo> channels, String modifier);

    /**
     * 按计划id全部删除
     *
     * @param id       id
     * @param modifier 修改人
     */
    void deleteAllByPlanId(Long id, String modifier);
}
