package com.ly.localactivity.marketing.domain.marketing.ro;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OperationPositionEditRo
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@ApiModel(value = "OperationPositionEditRo", description = "运营点位编辑/新增请求参数")
@EqualsAndHashCode(callSuper = false)
public class OperationPositionEditRo{
    /**
     * 运营点位id
     */
    @ApiModelProperty(value = "运营点位id")
    private Long id;
    /**
     * 运营点位code
     */
    @ApiModelProperty(value = "运营位点code")
    private String code;
    /**
     * 运营点位名称
     */
    @ApiModelProperty(value = "运营点位名称", required = true)
    @NotBlank(message = "运营点位名称不能为空")
    private String name;
    @ApiModelProperty(value = "自定义名称")
    private String alias;
    /**
     * 所属频道页
     */
    @ApiModelProperty(value = "所属频道页", required = true)
    @NotNull(message = "所属频道页不能为空")
    private Long positionPageId;
    /**
     * 模块值
     */
    @ApiModelProperty(value = "模块值", required = true)
    @NotNull(message = "模块值不能为空")
    @NotBlank(message = "模块值不能为空")
    private String moduleValue;
    /**
     * 纵向位置
     */
    @ApiModelProperty(value = "纵向位置")
    private Integer verticalPosition;
    /**
     * 纵向顺序
     */
    @ApiModelProperty(value = "纵向顺序")
    private Integer verticalIndex;
    /**
     * 横向位置
     */
    @ApiModelProperty(value = "横向位置")
    private Integer horizontalPosition;
    /**
     * 横向顺序
     */
    @ApiModelProperty(value = "横向顺序")
    private Integer horizontalIndex;
    /**
     * 输出形式
     */
    @ApiModelProperty(value = "输出形式", required = true)
    @NotNull(message = "输出形式不能为空")
    private Integer outputType;
    /**
     * 所属平台
     */
    @ApiModelProperty(value = "所属平台", required = true)
    @NotNull(message = "所属平台不能为空")
    private List<String> platforms;
    /**
     * 运营点位图片
     */
    @ApiModelProperty(value = "运营点位图片", required = true)
    @NotBlank(message = "运营点位图片不能为空")
    private String imageUrl;
    /**
     * 图片宽度
     */
    @ApiModelProperty(value = "图片宽度", required = true)
    private Integer imageWidth;
    /**
     * 图片高度
     */
    @ApiModelProperty(value = "图片高度", required = true)
    private Integer imageHeight;
    /**
     * 图片大小
     */
    @ApiModelProperty(value = "图片大小", required = true)
    private Integer imageMaxSize;
    /**
     * 图片最大张数
     */
    @ApiModelProperty(value = "图片最大张数", required = true)
    private Integer imageMaxCount;
    /**
     * 关键字最大长度
     */
    @ApiModelProperty(value = "关键字最大长度")
    private Integer keywordMaxLength;
    /**
     * 关键字最大个数
     */
    @ApiModelProperty(value = "关键字最大个数")
    private Integer keywordMaxCount;
    /**
     * 副标题最大长度
     */
    @TableField("sub_title_max_length")
    private Integer subTitleMaxLength;
    /**
     * 覆盖区域
     */
    @ApiModelProperty(value = "覆盖区域", required = true)
    @NotNull(message = "覆盖区域不能为空")
    private List<String> coverAreas;
    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填", required = true)
    @NotNull(message = "是否必填不能为空")
    private Integer emptyFlag;
    /**
     * 是否埋点
     */
    @ApiModelProperty(value = "是否埋点", required = true)
    @NotNull(message = "是否埋点不能为空")
    private Integer eventTrackingFlag;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效", required = true)
    @NotNull(message = "是否有效不能为空")
    private Integer status;
    /**
     * 点位描述
     */
    @ApiModelProperty(value = "点位描述")
    private String remark;
    @ApiModelProperty(value = "可弹推广计划数")
    private Integer popupPlanCount;
    @ApiModelProperty(value = "计划可弹层次数，1-每天一次，2-仅一次，3-每N天一次")
    private Integer popupFrequencyType;
    @ApiModelProperty(value = "每N天一次")
    private Integer popupFrequencyDays;

    @ApiModelProperty(value = "红包领取方式")
    private Integer pickupMethod;
    @ApiModelProperty(value = "点位使用方")
    private List<String> positionUsers;


}
