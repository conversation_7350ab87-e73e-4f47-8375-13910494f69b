package com.ly.localactivity.marketing.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DicInfo
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DictInfo {
    private String name;
    private String typeCode;
    private List<DictDetailInfo> detail;
}
