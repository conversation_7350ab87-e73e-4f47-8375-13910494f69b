package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * OperationPromotionPlanValidRo
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@ApiModel(value = "OperationPromotionPlanValidRo", description = "推广计划有效状态改变请求参数")
@Data
public class OperationPromotionPlanValidRo {
    @ApiModelProperty(value = "推广计划id", required = true)
    @NotNull(message = "推广计划id不能为空")
    @Min(value = 1, message = "推广计划id必须大于0")
    private Long planId;
    @ApiModelProperty(value = "是否有效", required = true)
    @NotNull(message = "是否有效不能为空")
    private Boolean validFlag;
}
