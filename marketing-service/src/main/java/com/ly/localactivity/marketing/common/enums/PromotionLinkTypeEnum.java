package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PromotionLinkTypeEnum
 *
 * <AUTHOR>
 * @date 2024/3/1
 */
@AllArgsConstructor
@Getter
public enum PromotionLinkTypeEnum {
    /**
     * 1-直接输入
     */
    DIRECT_INPUT(1, "直接输入"),
    /**
     * 2-模版
     */
    TEMPLATE(2, "模版"),
    /**
     * 3-固定链接
     */
    FIXED_LINK(3, "固定链接"),

    /**
     * 4-空
     */
    EMPTY(4, "空"),
    ;
    private final Integer code;
    private final String desc;
}
