package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * UseRuleListDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "UseRuleListDto", description = "使用规则")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UseRuleListDto {
    /**
     * 出行时间区间限定
     */
    @ApiModelProperty(value = "出行时间区间限定")
    private List<RuleModelDto> dTime;
    /**
     * 出行时间星期几限定
     */
    @ApiModelProperty(value = "出行时间星期几限定")
    private List<RuleModelDto> weekday;

    @ApiModel(value = "RuleModelDto", description = "规则")
    @Data
    public static class RuleModelDto {
        /**
         * 规则id
         */
        private String id;
        /**
         * 规则名称
         */
        private String name;
        /**
         * 限定规则:0,限定具体时间;1:限定动态天数
         */
        private String ext1;
        /**
         * 具体时间或天数
         */
        private String ext2;
        /**
         * 具体时间或天数
         */
        private String ext3;
    }
}
