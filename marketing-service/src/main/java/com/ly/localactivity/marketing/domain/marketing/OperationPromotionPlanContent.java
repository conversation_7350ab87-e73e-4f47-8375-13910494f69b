package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 运营推广计划内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("operation_promotion_plan_content")
public class OperationPromotionPlanContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运营计划id，operation_plan.id
     */
    @TableField("plan_id")
    private Long planId;

    /**
     * 图片地址
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 图片名称
     */
    @TableField("image_name")
    private String imageName;

    /**
     * 图片信息-上传人(工号)/上传时间
     */
    @TableField("image_info")
    private String imageInfo;

    /**
     * 关键词
     */
    @TableField("keyword")
    private String keyword;

    /**
     * 子标题
     */
    @TableField("sub_title")
    private String subTitle;

    /**
     * 平台code，数据字典
     */
    @TableField("platform")
    private String platform;

    /**
     * 链接类型，1-手动输入，2-模板,3- 固定链接
     */
    @TableField("link_type")
    private Integer linkType;

    /**
     * 推广链接
     */
    @TableField("link_url")
    private String linkUrl;

    /**
     * 链接模板id，link_type=2时才有值
     */
    @TableField("link_template_id")
    private Long linkTemplateId;

    /**
     * 是否自动验证下架，1-是，0-否
     */
    @TableField("auto_invalid_flag")
    private Integer autoInvalidFlag;

    /**
     * 自动验证下架的参数code，多个逗号间隔
     */
    @TableField("auto_invalid_param_codes")
    private String autoInvalidParamCodes;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;

    /**
     * 链接类型，1-原生链接，2-webview链接
     */
    @TableField("link_url_type")
    private Integer linkUrlType;

    /**
     * 链接前缀
     */
    @TableField("link_url_prefix")
    private String linkUrlPrefix;


}
