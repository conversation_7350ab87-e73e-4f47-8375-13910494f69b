package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.enums.DataFlagEnum;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.framework.utils.LocalDateTimeUtils;
import com.ly.localactivity.marketing.common.enums.*;
import com.ly.localactivity.marketing.common.constants.CommonIgnorePropConst;
import com.ly.localactivity.marketing.common.enums.PromoteTypeEnum;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.handler.promotionPlanParam.AbstractCheckHandler;
import com.ly.localactivity.marketing.common.handler.promotionPlanParam.DefaultPlanCheckHandler;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.common.WCCountryArea;
import com.ly.localactivity.marketing.domain.common.WCMarketAreaCity;
import com.ly.localactivity.marketing.domain.common.vo.CountryAreaSimpleVo;
import com.ly.localactivity.marketing.domain.marketing.*;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanPageDto;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.*;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentLinkParamMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanMapper;
import com.ly.localactivity.marketing.service.common.IWCCountryAreaService;
import com.ly.localactivity.marketing.service.common.IWCMarketAreaCityService;
import com.ly.localactivity.marketing.service.marketing.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.time.temporal.ChronoUnit;

/**
 * <p>
 * 运营推广计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Slf4j
@Service
public class OperationPromotionPlanServiceImpl extends ServiceImpl<OperationPromotionPlanMapper, OperationPromotionPlan> implements IOperationPromotionPlanService {

    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private IWCCountryAreaService countryAreaService;
    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private IWCMarketAreaCityService marketAreaCityService;
    @Resource
    private IOperationPromotionPlanRegionService operationPromotionPlanRegionService;
    @Resource
    private IOperationPromotionPlanContentService operationPromotionPlanContentService;
    @Resource
    private OperationPromotionPlanContentMapper operationPromotionPlanContentMapper;
    @Resource
    private OperationPromotionPlanContentLinkParamMapper operationPromotionPlanContentLinkParamMapper;
    @Resource
    private IOperationPromotionPlanService operationPromotionPlanService;
    @Resource
    private IOperationPromotionPlanChannelService operationPromotionPlanChannelService;
    @Resource
    private BizLogUtils bizLogUtils;

    @Resource(name = "nullValueCheckHandler")
    private AbstractCheckHandler paramCheckHandler;

    @Resource
    private DefaultPlanCheckHandler defaultPlanCheckHandler;

    private final static List<Long> HK_MAC_PROVINCE_IDS = Arrays.asList(33L, 34L);
    private final static List<Long> HK_MAC_CITY_IDS = Arrays.asList(395L, 396L);

    /**
     * 批量保存
     *
     * @param addRoList 列表
     * @return {@link Boolean}
     */
    @Override
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    public Boolean saveBatch(OperationPromotionPlanBatchAddRo addRoList) {
        String modifier = adminTokenUtils.getModifier();
        log.info("批量新增推广计划:{}", addRoList);

        List<Long> ids = new ArrayList<>();

        addRoList.getPlanList().forEach(s -> {
                    Integer defaultMaxCount;
                    if (addRoList.getDefaultFlag() && ObjectUtil.isNull(s.getDefaultIsSeparate())) {
                        throw new ApiException("兜底是否分开维护不能为空");
                    } else if (addRoList.getDefaultFlag()) {
                        //兜底是否分开维护
                        defaultMaxCount = s.getDefaultIsSeparate() ? NumberUtils.INTEGER_TWO : NumberUtils.INTEGER_ONE;
                    } else {
                        defaultMaxCount = NumberUtils.INTEGER_ZERO;
                    }
                    AtomicInteger index = new AtomicInteger();
                    index.set(NumberUtils.INTEGER_ZERO);
                    s.getInfos().forEach(info -> info.getDetails()
                            .forEach(detail -> detail
                                    .getDetailsGroupByIndex().parallelStream()
                                    //过滤出需要校验的，既需要保存的
                                    .filter(OperationPromotionPlanEditRo.OperationPromotionPlanEditDetailRo.DetailGroupByIndexRo::getNeedVerify)
                                    .forEach(detailGroupByIndexRo -> {
                                        //数量+1
                                        index.getAndIncrement();
                                        //兜底不分开维护只保存一次
                                        if (addRoList.getDefaultFlag() && index.get() > defaultMaxCount) {
                                            return;
                                        }
                                        OperationPromotionPlanSingleEditRo editRo = new OperationPromotionPlanSingleEditRo();
                                        BeanUtil.copyProperties(detailGroupByIndexRo, editRo);
                                        BeanUtil.copyProperties(detail, editRo);
                                        BeanUtil.copyProperties(info, editRo);
                                        BeanUtil.copyProperties(s, editRo);
                                        if (addRoList.getDefaultFlag()) {
                                            if (!editRo.getDefaultIsSeparate()) {
                                                editRo.setPromoteType(PromoteTypeEnum.DEFAULT.getCode());
                                            }
                                            //兜底不能设置渠道
                                            editRo.setDesignatedChannels(Boolean.FALSE);
                                            editRo.setChannels(Collections.emptyList());
                                        }
                                        ids.add(operationPromotionPlanService.saveSinglePlan(editRo, modifier, false));
                                    })));
                }
        );
        if (CollectionUtils.isNotEmpty(ids)) {
            addRoList.getPlanList().stream().findFirst().ifPresent(e -> {
                        CompletableFuture.runAsync(() -> {
                            operationPositionService.updateRandomKey(adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo(), e.getPositionId());
                            bizLogUtils.writeLog(BizLogDto.builder()
                                    .action(LogActionEnum.New)
                                    .logType1("推广计划")
                                    .logType2("批量新增推广计划")
                                    .filter(ids.stream().filter(ObjectUtil::isNotNull).map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)))
                                    .content(BeanCompareUtils.compareStr(new Object(), addRoList))
                                    .operateTime(LocalDateTime.now())
                                    .operator(adminTokenUtils.getEmployeeName())
                                    .operatorNum(adminTokenUtils.getEmployeeNo())
                                    .build());
                        });
                    }
            );
            return true;
        }
        return false;
    }


    /**
     * 保存计划
     *
     * @param editRo   请求
     * @param modifier 修改人
     * @param single   单条
     * @return Long
     */
    @Override
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    public Long saveSinglePlan(OperationPromotionPlanSingleEditRo editRo, String modifier, Boolean single) {
        boolean isEdit = ObjectUtil.isNotNull(editRo.getId()) && editRo.getId() > 0;
        OperationPromotionPlan promotionPlan = this.getById(editRo.getId());
        if (isEdit && promotionPlan == null) {
            throw new ApiException("推广计划不存在");
        }
        //校验
        paramCheckHandler.handle(editRo, new HashSet<>(4));

        //old推广计划
        OperationPromotionPlanSingleEditRo oldPlan = new OperationPromotionPlanSingleEditRo();
        PromoteTypeEnum oldPromoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, oldPlan.getPromoteType());
        if (isEdit) {
            OperationPromotionPlanSingleEditVo detail = getDetail(editRo.getId());
            BeanUtil.copyProperties(detail, oldPlan);
            //未有任何修改
            if (oldPlan.equals(editRo)) {
                return null;
            }
        }

        if (!isEdit) {
            promotionPlan = new OperationPromotionPlan();
            promotionPlan.setCreateTime(LocalDateTime.now());
            promotionPlan.setCreator(modifier);
        }
        promotionPlan.setModifiedTime(LocalDateTime.now());
        promotionPlan.setModifier(modifier);
        if (!isEdit) {
            int index = operationPromotionPlanService.getCount(editRo.getPositionId()) + NumberUtils.INTEGER_ONE;
            String planCode = editRo.getPositionCode() + StrUtil.UNDERLINE + index;
            //检查code重复
            if (count(new LambdaQueryWrapper<OperationPromotionPlan>()
                    .eq(OperationPromotionPlan::getCode, planCode)
                    .eq(OperationPromotionPlan::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()))
                    > 0) {
                throw new ApiException("推广计划重复");
            }
            promotionPlan.setCode(planCode);
        }
        //复制属性
        BeanUtil.copyProperties(editRo, promotionPlan, CommonIgnorePropConst.ID, CommonIgnorePropConst.CODE);

        //处理修改人修改人工号
        String modifier_ = modifier.substring(0, modifier.indexOf(StrUtil.BRACKET_START));
        String modifierNo = modifier.substring(modifier.indexOf(StrUtil.BRACKET_START) + NumberUtils.INTEGER_ONE, modifier.indexOf(StrUtil.BRACKET_END));
        promotionPlan.setModifier(modifier_);
        promotionPlan.setModifierNo(modifierNo);

        //保存/修改
        if (isEdit) {
            this.updateById(promotionPlan);
        } else {
            promotionPlan.setDeleteFlag(Boolean.FALSE);
            this.save(promotionPlan);
        }
        //保存地区
        PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, editRo.getPromoteType());
        if (promoteTypeEnum == PromoteTypeEnum.ZONE
                || promoteTypeEnum == PromoteTypeEnum.PROVINCE
                || promoteTypeEnum == PromoteTypeEnum.CITY) {
            operationPromotionPlanRegionService.saveRegionList(promotionPlan.getId()
                    , editRo.getRegionIds()
                    , promoteTypeEnum == PromoteTypeEnum.ZONE
                    , ObjectUtil.notEqual(oldPromoteTypeEnum, promoteTypeEnum)
                    , modifier);
        }

        //保存内容
        for (OperationPromotionPlanLinkDto linkDto : editRo.getLinks()) {
            OperationPromotionPlanContentDto contentDto = OperationPromotionPlanContentDto.builder()
                    .platform(linkDto.getPlatform())
                    .planId(promotionPlan.getId())
                    .imageUrl(StringUtils.nullToDefault(editRo.getImageUrl(), StringUtils.EMPTY))
                    .imageName(StringUtils.nullToDefault(editRo.getImageName(), StringUtils.EMPTY))
                    .imageInfo(StringUtils.nullToDefault(editRo.getImageInfo(), StringUtils.EMPTY))
                    .linkType(linkDto.getLinkType())
                    .linkUrlPrefix(linkDto.getLinkUrlPrefix())
                    .linkUrlType(linkDto.getLinkUrlType())
                    .linkUrl(linkDto.getLinkUrl())
                    .linkTemplateId(linkDto.getLinkTemplateId())
                    .autoInvalidFlag(linkDto.getAutoVerifyFlag())
                    .autoInvalidParamCodes(CollectionUtil.join(linkDto.getAutoVerifyCodes(), StrUtil.COMMA))
                    .keyword(StringUtils.nullToDefault(editRo.getKeyword(), StringUtils.EMPTY))
                    .subTitle(StringUtils.nullToDefault(editRo.getSubTitle(), StringUtils.EMPTY))
                    .linkParams(linkDto.getLinkParams())
                    .build();
            operationPromotionPlanContentService.saveOrUpdate(contentDto, modifier);
        }

        List<OperationPromotionPlanContent> operationPromotionPlanContents = operationPromotionPlanContentService.list(new LambdaQueryWrapper<OperationPromotionPlanContent>().eq(OperationPromotionPlanContent::getPlanId, promotionPlan.getId()));
        if (CollectionUtils.isNotEmpty(operationPromotionPlanContents) && isEdit && CollectionUtils.size(editRo.getLinks()) != CollectionUtils.size(operationPromotionPlanContents)) {
            List<Long> contentIds = operationPromotionPlanContents.stream().filter(content -> {
                if (CollectionUtils.isEmpty(editRo.getLinks())) {
                    return true;
                } else {
                    return editRo.getLinks().stream().noneMatch(link -> link.getPlatform().equals(content.getPlatform()));
                }
            }).map(OperationPromotionPlanContent::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(contentIds)) {
                //删除内容
                operationPromotionPlanContentMapper.deleteByIds(contentIds, modifier_, modifierNo);
                //删除链接参数
                List<OperationPromotionPlanContentLinkParam> operationPromotionPlanContentLinkParams = operationPromotionPlanContentLinkParamMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanContentLinkParam>().in(OperationPromotionPlanContentLinkParam::getPlanContentId, contentIds));
                if (CollectionUtils.isNotEmpty(operationPromotionPlanContentLinkParams)) {
                    operationPromotionPlanContentLinkParamMapper.deleteByIds(operationPromotionPlanContentLinkParams.stream().map(OperationPromotionPlanContentLinkParam::getId).collect(Collectors.toList()), modifier_, modifierNo);
                }
            }

        }

        //保存渠道
        if (editRo.getDesignatedChannels()) {
            operationPromotionPlanChannelService.saveOrUpdateChannelList(promotionPlan, editRo.getChannels(), modifier);
        } else if (isEdit && !editRo.getDefaultFlag()) {
            operationPromotionPlanChannelService.deleteAllByPlanId(promotionPlan.getId(), modifier);
        }

        //维护运营点位的必填维护标识
        if (editRo.getValidFlag() && editRo.getDefaultFlag()) {
            CompletableFuture.runAsync(() -> operationPositionService.updateRequiredMaintainedFlag(modifier, editRo.getPositionId()));
        }
        Long planId = promotionPlan.getId();
        //记录日志
        if (single) {
            CompletableFuture.runAsync(() -> {
                bizLogUtils.writeLog(BizLogDto.builder()
                        .action(isEdit ? LogActionEnum.Update : LogActionEnum.New)
                        .logType1("推广计划")
                        .logType2(isEdit ? "编辑" : "新增" + "推广计划")
                        .filter(ObjectUtil.isNotNull(planId) ? planId.toString() : StrUtil.EMPTY)
                        .content(BeanCompareUtils.compareStr(oldPlan, editRo))
                        .operateTime(LocalDateTime.now())
                        .operator(modifier_)
                        .operatorNum(modifierNo)
                        .build());
                operationPositionService.updateRandomKey(modifier_, modifierNo, editRo.getPositionId());
            });
        }
        return promotionPlan.getId();
    }


    /**
     * 判断是否存在时间交叉
     *
     * @param editRo 编辑ro
     * @return boolean
     */
    public boolean isTimeCross(OperationPromotionPlanSingleEditRo editRo) {
        if (ValidDateTypeEnum.LONG_TERM.getCode().equals(editRo.getValidDateType())) {
            return false;
        }
        //判断是否存在时间交叉 >0 存在交叉
        return baseMapper.countTimeCross(editRo) > NumberUtils.INTEGER_ZERO;
    }


    /**
     * 判断是否可锁定
     *
     * @param request 请求
     * @return boolean
     */
    @Override
    public boolean isCanLocked(OperationPromotionIsCanLockedRo request) {
        request.setEndTime(LocalDateTimeUtil.endOfDay(request.getEndTime(), Boolean.TRUE));
        request.setStartTime(LocalDateTimeUtil.beginOfDay(request.getStartTime()));
        return lambdaQuery()
                //有效
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                //排除自己
                .ne(ObjectUtil.isNotNull(request.getPlanId()), OperationPromotionPlan::getId, request.getPlanId())
                //相同平台
                .and(ObjectUtil.notEqual(request.getPlatform(), PlatformEnum.ALL_PLATFORM.getCode()), subSQL -> subSQL.eq(OperationPromotionPlan::getPlatform, request.getPlatform())
                        .or().eq(OperationPromotionPlan::getPlatform, PlatformEnum.ALL_PLATFORM.getCode()))
                //相同位置
                .eq(OperationPromotionPlan::getPositionId, request.getPointId())
                //相同顺位
                .eq(OperationPromotionPlan::getIndexValue, request.getIndexValue())
                //非兜底
                .eq(OperationPromotionPlan::getDefaultFlag, Boolean.FALSE)
                //有效
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                //锁定
                .eq(OperationPromotionPlan::getTopFlag, Boolean.TRUE)
                //指定时间段
                .eq(OperationPromotionPlan::getValidDateType, ValidDateTypeEnum.SPECIFIED_PERIOD.getCode())
                //存在时间交叉
                .le(OperationPromotionPlan::getValidBeginDate, request.getEndTime())
                .ge(OperationPromotionPlan::getValidEndDate, request.getStartTime())
                .count().compareTo(NumberUtils.INTEGER_ZERO) == NumberUtils.INTEGER_ZERO;
    }

    /**
     * 获取详细信息
     *
     * @param planId 计划id
     * @return {@link OperationPromotionPlanDto}
     */
    @Override
    public OperationPromotionPlanSingleEditVo getDetail(Long planId) {
        OperationPromotionPlan promotionPlan = this.getById(planId);
        if (ObjectUtil.isNull(promotionPlan)) {
            throw new ApiException("推广计划不存在");
        }
        OperationPromotionPlanSingleEditVo vo = new OperationPromotionPlanSingleEditVo();
        BeanUtil.copyProperties(promotionPlan, vo);
        //获取推广方式
        PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, promotionPlan.getPromoteType());
        //推广方式为城市、大区、省份才有地区信息
        if (promoteTypeEnum == PromoteTypeEnum.ZONE
                || promoteTypeEnum == PromoteTypeEnum.PROVINCE
                || promoteTypeEnum == PromoteTypeEnum.CITY) {
            List<OperationPromotionPlanRegion> regionList = operationPromotionPlanRegionService.getByPlanId(planId);
            if (CollectionUtils.isNotEmpty(regionList)) {
                List<Long> regionIds = new ArrayList<>();
                regionList.forEach(s -> {
                    //大区
                    if (promoteTypeEnum == PromoteTypeEnum.ZONE) {
                        regionIds.add(s.getRegionZoneId());
                    } else {
                        //省份、城市
                        regionIds.add(s.getRegionId());
                    }
                });
                vo.setRegionIds(regionIds);
            }
        }
        OperationPromotionPlanContentDto contentDto = operationPromotionPlanContentService.getByPlanId(planId);
        if (ObjectUtil.isNotNull(contentDto)) {
            BeanUtil.copyProperties(contentDto, vo, "platform");
        }
        //特殊处理内容
        if (vo.getPlaceFlag()) {
            vo.getLinks().forEach(link -> {
                link.setLinkType(NumberUtils.INTEGER_ONE);
            });
        }

        //渠道
        List<OperationPromotionPlanChannel> operationPromotionPlanChannels = operationPromotionPlanChannelService.list(new LambdaQueryWrapper<OperationPromotionPlanChannel>().eq(OperationPromotionPlanChannel::getPlanId, planId));
        if (CollectionUtil.isNotEmpty(operationPromotionPlanChannels)) {
            vo.setChannels(new ArrayList<>(operationPromotionPlanChannels.size()));
            operationPromotionPlanChannels.forEach(channel -> {
                OperationPromotionPlanChannelVo channelVo = new OperationPromotionPlanChannelVo();
                BeanUtil.copyProperties(channel, channelVo);
                vo.getChannels().add(channelVo);
            });
            vo.setDesignatedChannels(Boolean.TRUE);
        } else {
            vo.setDesignatedChannels(Boolean.FALSE);
            vo.setChannels(Collections.emptyList());
        }
        return vo;
    }

    /**
     * 分页查询
     * 推广计划：分两步，先根据ID精确查询，未匹配到结果走模糊查询 like code or like name
     * 推广方式：境内境外直接条件过滤，大区，省份，城市需联计划地区表
     * <p>
     * 推广日期：查询选择推广日期与有效期存在交集的推广计划
     * <p>
     * 状态:无效状态直接条件过滤
     * - 未生效：当前时间小于有效期开始时间
     * - 运营中：当前时间在有效期范围内
     * - 已过期：当前时间大于有效期结束时间
     * <p>
     * 占位预警：当前时间与有效期开始时间做比较
     * 到期预警：当前时间与有效期结束时间做比较
     * <p>
     * 维护人：匹配最后更新人
     *
     * @param request 请求
     * @return {@link CommonPage}<{@link OperationPromotionPlanPageVo}>
     */
    @Override
    public CommonPage<OperationPromotionPlanPageVo> pageList(OperationPromotionPlanPageDto request) {
        OperationPosition position = operationPositionService.getById(request.getPositionId());
        if (Objects.isNull(position)) {
            throw new ApiException("运营点位不存在");
        }
        Page<OperationPromotionPlanPageVo> page = new Page<>(request.getPageIndex(), request.getPageSize());
        //设置当前时间用于查询
        request.setCurrentDate(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        //占位预警
        if (Objects.nonNull(request.getPlaceWarnDays())) {
            request.setPlaceEndDate(LocalDateTimeUtil.endOfDay(LocalDateTime.now(), Boolean.TRUE).plusDays(request.getPlaceWarnDays()));
        }

        List<Long> regionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getProvinceIds())) {
            regionIds.addAll(request.getProvinceIds());
        }
        if (CollectionUtils.isNotEmpty(request.getCityIds())) {
            regionIds.addAll(request.getCityIds());
        }
        request.setRegionIds(regionIds);

        //到期预警
        if (Objects.nonNull(request.getExpireWarnDays())) {
            request.setExpireEndDate(LocalDateTimeUtil.endOfDay(LocalDateTime.now(), Boolean.TRUE).plusDays(request.getExpireWarnDays()));
        }
        IPage<OperationPromotionPlanPageVo> pageList = baseMapper.listPage(page, request);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().parallelStream().forEach(p -> {
                OperationStatusEnum operationStatus = getPlanOperationStatus(p);
                p.setStatus(p.getValidFlag());
                p.setReasonTypeName(ReasonTypeEnum.codeToName(p.getReasonType()));
                p.setPlatformName(PlatformEnum.codeToName(p.getPlatform()));
                p.setPromoteTypeName(PromoteTypeEnum.codeToName(p.getPromoteType()));
                p.setDefaultShowTypeName(PlanDefaultShowTypeEnum.codeToName(p.getDefaultShowType()));
                p.setOperationStatus(operationStatus.getCode());
                p.setOperationStatusDesc(operationStatus.getName());
                p.setBeginDate(toLocalDate(p.getValidBeginDate()));
                p.setEndDate(toLocalDate(p.getValidEndDate()));
                //计划地区信息
                if (NumberUtils.INTEGER_ONE.equals(p.getDefaultFlag())) {
                    if (PromoteTypeEnum.DEFAULT.getCode().equals(p.getPromoteType())) {
                        p.setRegionNames(Arrays.asList(PromoteTypeEnum.DOMESTIC.getName(), PromoteTypeEnum.OVERSEAS.getName()));
                    } else {
                        PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, p.getPromoteType());
                        p.setRegionNames(Collections.singletonList(promoteTypeEnum.getName()));
                    }
                } else {
                    Pair<List<Long>, List<String>> info = getPlanRegionInfo(p);
                    p.setRegionIds(info.getLeft());
                    p.setRegionNames(info.getRight());
                }
                //有效期（天）
                if (p.getDefaultFlag().equals(NumberUtils.INTEGER_ZERO) && p.getValidEndDate().isAfter(LocalDateTime.now())) {
                    if (p.getPlaceFlag().equals(NumberUtils.INTEGER_ZERO)) {
                        p.setExpireDays(getExpireDays(p.getValidEndDate()));
                    } else {
                        //占位有效期（天）
                        p.setPlaceExpireDays(getExpireDays(p.getValidEndDate()));
                    }
                }
            });
        }
        return CommonPage.restPage(pageList);
    }

    /**
     * 获取推广计划状态
     *
     * @param plan 推广计划
     * @return 状态
     */
    private OperationStatusEnum getPlanOperationStatus(OperationPromotionPlanPageVo plan) {
        if (StatusFlagEnum.DEACTIVATED.getCode().equals(plan.getValidFlag())) {
            return OperationStatusEnum.INVALID;
        }
        //兜底为有效
        if (PlanDefaultFlagEnum.DEFAULT.equals(EnumUtil.getBy(PlanDefaultFlagEnum::getCode, plan.getDefaultFlag()))) {
            return OperationStatusEnum.OPERATING;
        }
        LocalDateTime validBeginDate = plan.getValidBeginDate();
        LocalDateTime validEndDate = plan.getValidEndDate();
        if (Objects.isNull(validBeginDate) || Objects.isNull(validEndDate)) {
            return OperationStatusEnum.INVALID;
        }

        LocalDateTime current = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
        if (current.isAfter(validEndDate)) {
            return OperationStatusEnum.EXPIRED;
        }
        if (current.isBefore(validBeginDate)) {
            return OperationStatusEnum.NOT_BEGUN;
        }
        return OperationStatusEnum.OPERATING;
    }

    /**
     * 获取计划地区信息
     *
     * @param plan 推广计划
     */
    private Pair<List<Long>, List<String>> getPlanRegionInfo(OperationPromotionPlanPageVo plan) {
        List<Long> regionIds = new ArrayList<>();
        List<String> regionNames = new ArrayList<>();
        if (Objects.isNull(plan.getPromoteType())) {
            return Pair.of(regionIds, regionNames);
        }
        //大区
        if (PromoteTypeEnum.ZONE.getCode().equals(plan.getPromoteType())) {
            List<OperationPromotionPlanRegion> regionList = operationPromotionPlanRegionService.getByPlanId(plan.getId());
            regionIds = regionList.stream().map(OperationPromotionPlanRegion::getRegionZoneId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(regionIds)) {
                regionNames = regionIds.stream().map(id -> MarketAreaEnum.codeToName(id.intValue())).collect(Collectors.toList());
            }
        } else if (PromoteTypeEnum.PROVINCE.getCode().equals(plan.getPromoteType()) //省份、城市
                || PromoteTypeEnum.CITY.getCode().equals(plan.getPromoteType())) {
            List<OperationPromotionPlanRegion> regionList = operationPromotionPlanRegionService.getByPlanId(plan.getId());
            regionIds = regionList.stream().map(OperationPromotionPlanRegion::getRegionId)
                    .collect(Collectors.toList());
            List<CountryAreaSimpleVo> countryAreas = countryAreaService.getByIds(regionIds);
            if (CollectionUtils.isNotEmpty(countryAreas)) {
                regionNames = countryAreas.stream().map(CountryAreaSimpleVo::getShortName).collect(Collectors.toList());
            }
        }
        return Pair.of(regionIds, regionNames);
    }

    /**
     * 计算有效期天数
     *
     * @param dateTime 日期时间
     * @return {@link Integer}
     */
    private Integer getExpireDays(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return null;
        }
        return (int) Math.abs(LocalDateTimeUtil.between(dateTime, LocalDateTime.now(), ChronoUnit.DAYS));
    }


    private String toLocalDate(LocalDateTime time) {
        if (Objects.isNull(time)) {
            return StringUtils.EMPTY;
        }
        return LocalDateTimeUtils.getDateString(time);
    }

    /**
     * 获取不可用日期
     *
     * @param request 请求
     * @return {@link List}<{@link PeriodVo}>
     */
    @Override
    public List<PeriodVo> getUnavailableDate(OperationPromotionPlanUnavailableDateRo request) {
        List<PeriodVo> unavailableDateVos = baseMapper.listUnavailableDate(request);
        return mergePeriods(unavailableDateVos);
    }

    /**
     * 获取锁定的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> listLockedIndex(OperationPromotionListLockedIndexRo request) {
        return baseMapper.listLockedIndex(request);
    }

    /**
     * 获取存在占位的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> listPlaceholderIndex(OperationPromotionListPlaceholderIndexRo request) {
        return baseMapper.listPlaceholderIndex(request);
    }

    /**
     * 获取已经维护的顺位
     *
     * @param request 请求
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> listMaintainIndex(OperationPromotionListMaintainIndexRo request) {
        return baseMapper.listMaintainIndex(request);
    }

    /**
     * 获取当前顺位,所选推广时间推广计划覆盖情况(高优先级)
     *
     * @param request 请求
     * @return {@link OperationPromotionPlanCoverConditionVo}
     */
    @Override
    public List<OperationPromotionPlanCoverConditionVo> getCoverCondition(OperationPromotionPlanCoverConditionRo request) {
        PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, request.getPromoteType());
        //处理大区
        List<Long> regionIds = null;
        if (PromoteTypeEnum.ZONE.equals(promoteTypeEnum)) {
            //获取大区下的省份ids和城市ids
            List<WCMarketAreaCity> marketAreaCities = marketAreaCityService.list(new LambdaQueryWrapper<WCMarketAreaCity>()
                    .select(WCMarketAreaCity::getMACCityId, WCMarketAreaCity::getMACProvinceId)
                    .in(WCMarketAreaCity::getMACMarketAreaId, request.getRegionIds())
                    //排除港澳
                    .notIn(WCMarketAreaCity::getMACProvinceId, HK_MAC_PROVINCE_IDS)
                    .eq(WCMarketAreaCity::getMACDataFlag, NumberUtils.INTEGER_ONE)
            );
            if (CollectionUtil.isNotEmpty(marketAreaCities)) {
                List<Long> provinceIds = marketAreaCities.parallelStream().map(WCMarketAreaCity::getMACProvinceId).distinct().collect(Collectors.toList());
                List<Long> cityIds = marketAreaCities.parallelStream().map(WCMarketAreaCity::getMACCityId).collect(Collectors.toList());
                regionIds = CollectionUtil.newArrayList(provinceIds);
                regionIds.addAll(cityIds);
            }
            //处理省份
        } else if (PromoteTypeEnum.PROVINCE.equals(promoteTypeEnum)) {
            List<WCCountryArea> wcCountryAreas = countryAreaService.list(new LambdaQueryWrapper<WCCountryArea>()
                    .select(WCCountryArea::getCAId)
                    .in(WCCountryArea::getCAProvinceId, request.getRegionIds())
                    .eq(WCCountryArea::getCADataFlag, NumberUtils.INTEGER_ONE)
                    .eq(WCCountryArea::getCAType, AdministrativeLevelEnum.CITY.getCode())
            );
            regionIds = CollectionUtil.newArrayList(wcCountryAreas.stream().map(WCCountryArea::getCAId).collect(Collectors.toList()));
        } else if (PromoteTypeEnum.OVERSEAS.equals(promoteTypeEnum) || PromoteTypeEnum.DOMESTIC.equals(promoteTypeEnum)) {
            regionIds = CollectionUtil.newArrayList(HK_MAC_CITY_IDS);
            regionIds.addAll(HK_MAC_PROVINCE_IDS);
        }
        request.setRegionIds(regionIds);
        //所有计划
        List<OperationPromotionPlan> plans = baseMapper.listPlanCoverCondition(request);
        //非锁定计划时间段
        List<PeriodVo> periodVos = plans.stream().filter(e -> e.getTopFlag().equals(NumberUtils.INTEGER_ZERO)).map(e -> PeriodVo.builder()
                .startTime(e.getValidBeginDate())
                .endTime(e.getValidEndDate())
                .build()).collect(Collectors.toList());
        //锁定的计划时间段
        List<PeriodVo> periodVosIsTop = plans.stream().filter(e -> e.getTopFlag().equals(NumberUtils.INTEGER_ONE)).map(e -> PeriodVo.builder()
                .startTime(e.getValidBeginDate())
                .endTime(e.getValidEndDate())
                .build()).collect(Collectors.toList());
        List<OperationPromotionPlanCoverConditionVo> result = new ArrayList<>(NumberUtils.INTEGER_TWO);
        result.add(OperationPromotionPlanCoverConditionVo.builder()
                .topFlag(Boolean.FALSE)
                .periodList(mergePeriods(periodVos))
                .build());
        result.add(OperationPromotionPlanCoverConditionVo.builder()
                .topFlag(Boolean.TRUE)
                .periodList(mergePeriods(periodVosIsTop))
                .build());
        return result;
    }

    /**
     * 获取计划img列表
     *
     * @param positionId 位置id
     * @param planId     计划id
     * @return {@link List}<{@link ImageVo}>
     */
    @Override
    public List<ImageVo> getPlanImgList(Long positionId, Long planId) {
        if (Objects.isNull(positionId) && Objects.isNull(planId)) {
            return null;
        }
        if (ObjectUtil.isNotNull(planId)) {
            OperationPromotionPlan plan = getOne(new LambdaQueryWrapper<OperationPromotionPlan>().eq(OperationPromotionPlan::getId, planId)
                    .eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                    .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
            );
            positionId = ObjectUtil.isNotNull(plan) ? plan.getPositionId() : positionId;
        }
        return ObjectUtil.isNull(positionId) ? Collections.emptyList() : baseMapper.listImageByPositionId(positionId).stream().distinct().collect(Collectors.toList());
    }


    /**
     * 更改推广计划有效状态
     *
     * @param request 请求
     * @return boolean
     * <AUTHOR>
     */
    @Override
    public boolean changeValid(OperationPromotionPlanValidRo request) {
        String modifier = adminTokenUtils.getEmployeeName();
        String employeeNo = adminTokenUtils.getEmployeeNo();

        //old推广计划
        OperationPromotionPlanSingleEditVo old = getDetail(request.getPlanId());
        if (ObjectUtil.isNull(old)) {
            throw new ApiException("推广计划不存在");
        }
        //如果是设置有效 非兜底
        if (request.getValidFlag() && old.getDefaultFlag() == Boolean.FALSE) {
            List<Long> ids = null;
            Integer promoteType = old.getPromoteType();
            PromoteTypeEnum typeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, promoteType);
            if (PromoteTypeEnum.ZONE.equals(typeEnum) || PromoteTypeEnum.PROVINCE.equals(typeEnum) || PromoteTypeEnum.CITY.equals(typeEnum)) {
                List<OperationPromotionPlanRegion> regionList = operationPromotionPlanRegionService.getByPlanId(request.getPlanId());
                regionList = CollectionUtils.isEmpty(regionList) ? Collections.emptyList() : regionList;
                if (PromoteTypeEnum.ZONE.equals(typeEnum)) {
                    ids = regionList.stream().map(OperationPromotionPlanRegion::getRegionZoneId).collect(Collectors.toList());
                } else {
                    ids = regionList.stream().map(OperationPromotionPlanRegion::getRegionId).collect(Collectors.toList());
                }
            }
            //查询当前时间是否存在有效的推广计划
            boolean timeCross = isTimeCross(OperationPromotionPlanSingleEditRo.builder()
                    .validBeginDate(old.getValidBeginDate())
                    .validEndDate(old.getValidEndDate())
                    .positionId(old.getPositionId())
                    .id(old.getId())
                    .indexValue(old.getIndexValue())
                    .platform(old.getPlatform())
                    .promoteType(old.getPromoteType())
                    .regionIds(ids)
                    .build());
            //查询当前时间是否存在有效的推广计划
            if (timeCross) {
                throw new ApiException("当前时间段已存在有效的推广计划");
            }
            //兜底有效
        } else if (request.getValidFlag() && old.getDefaultFlag() == Boolean.TRUE) {
            defaultPlanCheckHandler.handle(BeanUtil.toBean(old, OperationPromotionPlanSingleEditRo.class), new HashSet<>(1));
        }
        boolean update = lambdaUpdate()
                .set(OperationPromotionPlan::getValidFlag, request.getValidFlag())
                .set(OperationPromotionPlan::getModifiedTime, LocalDateTime.now())
                .set(OperationPromotionPlan::getModifier, modifier)
                .set(OperationPromotionPlan::getModifierNo, employeeNo)
                .eq(OperationPromotionPlan::getId, request.getPlanId())
                .ne(OperationPromotionPlan::getValidFlag, request.getValidFlag())
                .update();
        //新推广计划
        OperationPromotionPlanSingleEditVo newPlan = getDetail(request.getPlanId());
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("推广计划")
                .logType2("编辑" + (request.getValidFlag() ? "有效" : "无效") + "推广计划")
                .content(BeanCompareUtils.compareStr(old, newPlan))
                .operateTime(LocalDateTime.now())
                .operator(modifier)
                .operatorNum(employeeNo)
                .build());
        CompletableFuture.runAsync(() -> operationPositionService.updateRandomKey(modifier, employeeNo, old.getPositionId()));

        return update;
    }


    /**
     * 获取推广计划计划统计信息
     *
     * @param request 请求
     * @return {@link PromotionPlanStatisticsVo}
     */
    @Override
    public PromotionPlanStatisticsVo getPromotionPlanStatistics(PromotionPlanStatisticsRo request) {
        //获取推广计划总数
        int total = lambdaQuery().eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getPositionId, request.getPositionId())
                .count();
        //获取占位总数
        int placeTotal = lambdaQuery().eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getPositionId, request.getPositionId())
                .eq(OperationPromotionPlan::getPlaceFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .gt(OperationPromotionPlan::getValidEndDate, LocalDate.now())
                .count();
        //计算占位未来两天到期数
        int placeExpireTotal = lambdaQuery().eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getPositionId, request.getPositionId())
                .eq(OperationPromotionPlan::getPlaceFlag, Boolean.TRUE)
                .lt(OperationPromotionPlan::getValidEndDate, LocalDate.now().plusDays(3))
                .gt(OperationPromotionPlan::getValidEndDate, LocalDateTime.now())
                .count();
        //运营中7天内到期数
        int expireTotal = lambdaQuery().eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getPositionId, request.getPositionId())
                .eq(OperationPromotionPlan::getDefaultFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getPlaceFlag, Boolean.FALSE)
                //设置时间位0点0分0秒
                .ge(OperationPromotionPlan::getValidEndDate, LocalDate.now())
                .lt(OperationPromotionPlan::getValidEndDate, LocalDate.now().plusDays(8))
                .count();

        return PromotionPlanStatisticsVo.builder()
                .total(total)
                .placeCount(placeTotal)
                .placeExpireCount(placeExpireTotal)
                .planExpireCount(expireTotal)
                .build();
    }

    /**
     * 获取最晚过期的天数
     *
     * @param positionId 位置id
     * @return {@link Integer}
     */
    @Override
    public Integer getLatestExpireDay(Long positionId) {
        OperationPromotionPlan plan = lambdaQuery().select(OperationPromotionPlan::getValidEndDate)
                .eq(OperationPromotionPlan::getPositionId, positionId)
                .eq(OperationPromotionPlan::getValidFlag, Boolean.TRUE)
                .eq(OperationPromotionPlan::getDeleteFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getPlaceFlag, Boolean.FALSE)
                .eq(OperationPromotionPlan::getDefaultFlag, Boolean.FALSE)
                .gt(OperationPromotionPlan::getValidEndDate, LocalDateTime.now())
                .orderByDesc(OperationPromotionPlan::getValidEndDate)
                .last("limit 1")
                .one();
        return ObjectUtil.isNotNull(plan) ? getExpireDays(plan.getValidEndDate()) : null;
    }


    /**
     * 合并时间段
     *
     * @param periods 期间
     * @return {@link List}<{@link PeriodVo}>
     */
    public static List<PeriodVo> mergePeriods(List<PeriodVo> periods) {
        if (CollectionUtils.isEmpty(periods)) {
            return Collections.emptyList();
        }
        // 根据 startTime 对时间段进行排序
        periods.sort(Comparator.comparing(PeriodVo::getStartTime));
        LinkedList<PeriodVo> merged = new LinkedList<>();
        for (PeriodVo period : periods) {
            // 如果合并列表为空，或者当前时间段不与最后一个合并的时间段重叠，就将其添加到合并列表
            if (merged.isEmpty() || merged.getLast().getEndTime().plusSeconds(NumberUtils.INTEGER_ONE).isBefore(period.getStartTime())) {
                period.setCount(NumberUtils.INTEGER_ONE);
                merged.add(period);
            } else {
                // 否则，合并时间段，即更新当前合并时间段的结束时间为当前时间段和最后一个合并时间段中最晚的结束时间
                PeriodVo lastMergedPeriod = merged.getLast();
                lastMergedPeriod.setEndTime(
                        max(lastMergedPeriod.getEndTime(), period.getEndTime())
                );
                lastMergedPeriod.setCount(lastMergedPeriod.getCount() + NumberUtils.INTEGER_ONE);
            }
        }
        return merged;
    }


    /**
     * 辅助方法，返回两个 LocalDateTime 中较晚的一个
     *
     * @param a a
     * @param b b
     * @return {@link LocalDateTime}
     */
    private static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        return a.isAfter(b) ? a : b;
    }

    /**
     * 自动无效
     *
     * @param planId 计划id
     * @param reason 原因
     * @return boolean
     */
    @Override
    public boolean invalidByAuto(Long planId, String reason) {
        OperationPromotionPlan promotionPlan = this.getById(planId);
        if (promotionPlan == null) {
            //数据不存在不处理
            return false;
        }

        if (promotionPlan.getValidFlag().equals(DataFlagEnum.Invalid.getCode())) {
            //已经无效不处理
            return false;
        }

        promotionPlan.setValidFlag(DataFlagEnum.Invalid.getCode());
        promotionPlan.setInvalidTime(LocalDateTime.now());
        promotionPlan.setValidReasonType(PlanValidReasonTypeEnum.Product_Invalid.getCode());
        promotionPlan.setValidReasonRemark(StringUtils.isBlank(reason) ? "" : reason);
        return this.updateById(promotionPlan);
    }

    /**
     * 获取数量
     *
     * @param positionId 位置id
     * @return int
     */
    @Override
    public int getCount(Long positionId) {
        return baseMapper.count(positionId);
    }


}

