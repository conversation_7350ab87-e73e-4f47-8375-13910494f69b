package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OperationPositionStatusEnum
 *
 * <AUTHOR>
 * @date 2024/2/1
 */
@AllArgsConstructor
@Getter
public enum OperationPositionStatusEnum {
    /**
     * 0-、无效
     */
    INVALID(0, "无效"),
    /**
     * 1-有效
     */
    VALID(1, "有效")


    ;
    private Integer code;
    private String name;

}
