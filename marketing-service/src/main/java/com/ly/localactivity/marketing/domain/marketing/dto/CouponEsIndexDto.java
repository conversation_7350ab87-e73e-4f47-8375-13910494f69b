package com.ly.localactivity.marketing.domain.marketing.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchApiStatusEnum;
import com.ly.localactivity.marketing.common.utils.CollectionUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.external.coupon.dto.ProjectDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CouponCacheIndexDto
 *
 * <AUTHOR>
 * @date 2024/5/21
 */
@Data
public class CouponEsIndexDto {
    private String id;
    @ApiModelProperty(value = "红包批次类型，1-平台红包，2-商家红包")
    private Integer batchType;
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
    @ApiModelProperty(value = "适用平台")
    private List<String> platforms;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    @ApiModelProperty(value = "批次内部名称")
    private String batchInnerName;
    @ApiModelProperty(value = "批次列表名称，数据来源于coupon_batch_list表的name字段")
    private String batchListName;
    @ApiModelProperty(value = "红包类型，0 立减 1折扣；2次卡；3满返；5满减满返")
    private Integer couponType;

    @ApiModelProperty(value = "是否可退")
    private Boolean isRefund;

    @ApiModelProperty(value = "批次有效期开始时间")
    private Long beginDate;
    @ApiModelProperty(value = "批次有效期结束时间")
    private Long endDate;

    @ApiModelProperty(value = "项目ids")
    private List<Integer> projectIds;
    @ApiModelProperty(value = "批次状态，1-可领取，2-不可领取，3-已过期")
    private Integer status;


    @ApiModelProperty(value = "点位code")
    private List<String> operationPositionCodes;
    @ApiModelProperty(value = "refid")
    private List<String> refIdList;

    @ApiModelProperty(value = "佣金限制，1-不限，2-佣金>0，3-佣金>=0")
    private Integer commissionType;
    @ApiModelProperty(value = "商品限制类型，1-全部(不限)，2-指定出发地，2-指定二级品类，3-指定商品")
    private Integer limitType;
    @ApiModelProperty(value = "限制数据，根据limitType不同，数据不同")
    private List<String> limitData;
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "库存限制,0-不限制，1-限制")
    private Integer stockLimit;
    @ApiModelProperty(value = "当日领取限制,0-不限制，1-限制")
    private Integer dayLimit;
    @ApiModelProperty(value = "一级品类")
    private Integer firstCategoryId;

    public static CouponEsIndexDto fromExt(CouponBatchCacheDto catchDto) {
        CouponEsIndexDto batchVo = new CouponEsIndexDto();
        batchVo.setId(catchDto.getBatchNo());
        batchVo.setBatchNo(catchDto.getBaseInfo().getBatchNo());
        batchVo.setBatchName(catchDto.getBaseInfo().getBatchName());
        batchVo.setBatchInnerName(catchDto.getBaseInfo().getCode());
        batchVo.setCouponType(catchDto.getBaseInfo().getCouponType());

        batchVo.setIsRefund(catchDto.getBaseInfo().getIsRefund() != null && catchDto.getBaseInfo().getIsRefund() == 0);
        batchVo.setBeginDate(catchDto.getBaseInfo().getBeginDate().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000);
        batchVo.setEndDate(catchDto.getBaseInfo().getEndDate().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000);

        if (CollectionUtil.isNotEmpty(catchDto.getBaseInfo().getProjectList())) {
            batchVo.setProjectIds(catchDto.getBaseInfo().getProjectList().stream().map(ProjectDto::getProjectId).collect(Collectors.toList()));
        }
        //状态需要转换
        batchVo.setStatus(CouponBatchApiStatusEnum.getByExtStatus(catchDto.getBaseInfo().getBatchStatus(), catchDto.getLastUseTime()).getCode());

        //使用渠道
        if (StringUtils.isNotBlank(catchDto.getBaseInfo().getUseChannel())) {
            List<String> channels = JSON.parseArray(catchDto.getBaseInfo().getUseChannel(), String.class);
            batchVo.setPlatforms(channels);
        }

        if (catchDto.getListInfo() != null) {
            batchVo.setFirstCategoryId(catchDto.getListInfo().getProductCategoryId());
            batchVo.setBatchType(catchDto.getListInfo().getType());
            batchVo.setSupplierId(catchDto.getListInfo().getType().equals(CouponBatchListTypeEnum.SUPPLIER.getCode()) ? catchDto.getListInfo().getSupplierId() : 0L);
        }
        // 点位code
        if (CollectionUtil.isNotEmpty(catchDto.getOperationCodes())) {
            batchVo.setOperationPositionCodes(catchDto.getOperationCodes());
        }
        // refId
        if (CollectionUtil.isNotEmpty(catchDto.getRefIds())) {
            batchVo.setRefIdList(catchDto.getRefIds());
        }
        // 使用规则
        if (catchDto.getRule() != null) {
            batchVo.setLimitType(catchDto.getRule().getLimitType());
            batchVo.setCommissionType(catchDto.getRule().getCommissionType());
            batchVo.setLimitData(catchDto.getLimitDataIds());
        }
        if (catchDto.getListInfo() != null) {
            batchVo.setBatchListName(catchDto.getListInfo().getName());
        }
        batchVo.setUpdateTime(LocalDateTime.now());

        //库存限制
        if (catchDto.getStockLimit() != null) {
            batchVo.setStockLimit(catchDto.getStockLimit() ? 1 : 0);
        } else {
            batchVo.setStockLimit(0);
        }
        if (catchDto.getDayLimit() != null) {
            batchVo.setDayLimit(catchDto.getDayLimit() ? 1 : 0);
        } else {
            batchVo.setDayLimit(0);
        }

        if (!CouponBatchApiStatusEnum.Expired.getCode().equals(batchVo.getStatus()) && (batchVo.getStockLimit() == 1 || batchVo.getDayLimit() == 1)) {
            //如果有库存限制或者当日领取限制，需要判断是否受限
            batchVo.setStatus(CouponBatchApiStatusEnum.UnAvailable.getCode());
        }

        return batchVo;
    }

    public static List<CouponEsIndexDto> filterBathchList(List<CouponEsIndexDto> allCouponEsIndexDtoList, List<String> batchNos, String refid, String positionCode, Boolean filterAvaliabled, Boolean isDetailPage) {
        if (CollectionUtil.isEmpty(allCouponEsIndexDtoList)){
            return Collections.emptyList();
        }
        // 出入批次号以批次号为准
        if (CollectionUtil.isNotEmpty(batchNos)){
            return allCouponEsIndexDtoList.stream().filter(item -> batchNos.contains(item.getBatchNo())).collect(Collectors.toList());
        }

        return allCouponEsIndexDtoList.stream().filter(dto -> {
            if (StringUtils.isNotBlank(refid) && CollectionUtils.isNotEmpty(dto.getRefIdList()) && !dto.getRefIdList().contains(refid)){
                return false;
            }
            // 可领红包要校验点位 已领红包根据是否是详情页校验点位 详情页不校验点位
            // 不是详情页则校验点位
            if (!Boolean.TRUE.equals(isDetailPage) && StringUtils.isNotBlank(positionCode) && (CollectionUtils.isEmpty(dto.getOperationPositionCodes()) || !dto.getOperationPositionCodes().contains(positionCode))){
                return false;
            }
            if (Boolean.TRUE.equals(filterAvaliabled) && !CouponBatchApiStatusEnum.Available.getCode().equals(dto.getStatus())){
                return false;
            }

            return true;
        }).collect(Collectors.toList());


    }
}
