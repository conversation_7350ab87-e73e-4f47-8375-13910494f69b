package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.common.constants.DateConst;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchValidDate;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchValidDateDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchValidDateMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchValidDateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包批次有效期 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
@Slf4j
public class CouponBatchValidDateServiceImpl extends ServiceImpl<CouponBatchValidDateMapper, CouponBatchValidDate> implements ICouponBatchValidDateService {
    @Resource
    private CouponBatchValidDateMapper couponBatchValidDateMapper;
    @Resource
    private AdminTokenUtils adminTokenUtils;

    /**
     * 保存或更新
     *
     * @param couponBatchDetail       优惠券批处理详细信息
     * @param couponBatchValidDateDto 优惠券批次有效日期dto
     * @return {@link Boolean }
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchDetail couponBatchDetail, CouponBatchValidDateDto couponBatchValidDateDto) {
        LambdaQueryWrapper<CouponBatchValidDate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchValidDate::getCouponBatchDetailId, couponBatchDetail.getId());
        CouponBatchValidDate oldCouponBatchValidDate = couponBatchValidDateMapper.selectOne(lambdaQueryWrapper);
        // 删除
        if (ObjectUtil.isNull(couponBatchValidDateDto)) {
            if (ObjectUtil.isNotNull(oldCouponBatchValidDate)) {
                couponBatchValidDateMapper.deleteByIds(Collections.singletonList(oldCouponBatchValidDate.getId()), adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo());
            } else {
                return true;
            }
        }

        // 恢复默认值
        if (!ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setValidBeginDate(DateConst.DEFAULT_TIME);
            couponBatchValidDateDto.setValidEndDate(DateConst.DEFAULT_TIME);
        }
        if (!ValidDateTypeEnum.SPECIFIED_PERIOD.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setDynamicDurationDay(0);
            couponBatchValidDateDto.setDynamicStartDay(0);
        }
        if (!ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setCountdownHour(0);
        }
        if (!ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setCountdownMinute(0);
        }

        if (ObjectUtil.isNull(oldCouponBatchValidDate)) {
            CouponBatchValidDate couponBatchValidDate = BeanUtil.copyProperties(couponBatchValidDateDto, CouponBatchValidDate.class);
            if (couponBatchValidDate.getValidEndDate() != null) {
                couponBatchValidDate.setValidEndDate(LocalDateTimeUtil.endOfDay(couponBatchValidDate.getValidEndDate(), true));
            }
            couponBatchValidDate.setCouponBatchId(couponBatchDetail.getCouponBatchId());
            couponBatchValidDate.setCouponBatchDetailId(couponBatchDetail.getId());
            couponBatchValidDate.setCreator(adminTokenUtils.getModifier());
            couponBatchValidDate.setCreateTime(LocalDateTime.now());
            couponBatchValidDate.setModifier(adminTokenUtils.getModifier());
            couponBatchValidDate.setModifiedTime(LocalDateTime.now());
            couponBatchValidDate.setDeleteFlag(Boolean.FALSE);
            couponBatchValidDateMapper.insert(couponBatchValidDate);
        } else {
            CouponBatchValidDate couponBatchValidDate = BeanUtil.copyProperties(couponBatchValidDateDto, CouponBatchValidDate.class);
            if (couponBatchValidDate.getValidEndDate() != null) {
                couponBatchValidDate.setValidEndDate(LocalDateTimeUtil.endOfDay(couponBatchValidDate.getValidEndDate(), true));
            }
            couponBatchValidDate.setId(oldCouponBatchValidDate.getId());
            couponBatchValidDate.setModifier(adminTokenUtils.getModifier());
            couponBatchValidDate.setModifiedTime(LocalDateTime.now());
            couponBatchValidDateMapper.updateById(couponBatchValidDate);
        }
        return true;
    }

    /**
     * 根据红包批次id查询
     *
     * @param couponBatchDetailIdList
     * @return
     */
    @Override
    public Map<Long, CouponBatchValidDateDto> selectByCouponBatchDetailIdList(List<Long> couponBatchDetailIdList) {
        if (CollectionUtil.isEmpty(couponBatchDetailIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchValidDate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchValidDate::getCouponBatchDetailId, couponBatchDetailIdList);
        List<CouponBatchValidDate> couponBatchValidDateList = couponBatchValidDateMapper.selectList(lambdaQueryWrapper);
        List<CouponBatchValidDateDto> couponBatchValidDateDtoList = BeanUtil.copyToList(couponBatchValidDateList, CouponBatchValidDateDto.class);
        return couponBatchValidDateDtoList.stream().collect(Collectors.toMap(CouponBatchValidDateDto::getCouponBatchDetailId, Function.identity(), (a, b) -> a));
    }
}
