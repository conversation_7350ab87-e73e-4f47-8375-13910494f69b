package com.ly.localactivity.marketing.common.utils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.*;

/**
 * XmlUtils
 *
 * <AUTHOR>
 * @date 2024/1/12
 */
public class XmlUtils {

    /**
     * xml到bean
     *
     * @param xmlFile   xml文件
     * @param beanClass bean类
     * @return {@link T}
     */
    public static <T> T xmlToBean(File xmlFile, Class<T> beanClass) {
        if (xmlFile.length() == 0) {
            throw new RuntimeException("xml file content length is blank.");
        }
        BufferedReader bufferedReader = null;
        FileReader fileReader = null;
        StringBuffer content = new StringBuffer();
        try {
            fileReader = new FileReader(xmlFile);
            bufferedReader = new BufferedReader(fileReader);
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                content.append(line);
            }
        } catch (Exception e) {
            throw new RuntimeException("xml file read error.", e);
        } finally {
            try {
                if (fileReader != null){
                    fileReader.close();
                }
                if (bufferedReader != null){
                    bufferedReader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return xmlToBean(content.toString(), beanClass);
    }


    /**
     * xml到bean
     *
     * @param xmlContent xml内容
     * @param beanClass  bean类
     * @return {@link T}
     */
    public static <T> T xmlToBean(String xmlContent, Class<?> beanClass) {
        Reader reader = null;
        try {
            JAXBContext context = JAXBContext.newInstance(beanClass);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            reader = new StringReader(xmlContent);
            return (T) unmarshaller.unmarshal(reader);
        } catch (Exception e) {
            throw new RuntimeException("xml parse error.", e);
        } finally {
            try {
                if (reader != null){
                    reader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }
}
