package com.ly.localactivity.marketing.domain.marketing.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.ly.localactivity.marketing.common.constants.DateConst;
import com.ly.localactivity.marketing.common.enums.CouponTypeEnum;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.common.validation.annotation.FutureOrPresentDay;
import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.common.validation.group.PlatformValidation;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.ExtendInfoDto;
import com.ly.localactivity.marketing.external.coupon.dto.ProjectDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description CouponBatchDetailDto
 * @date 2024-04-19
 */
@Data
@ApiModel(value = "CouponBatchDetailDto", description = "红包批次详情DTO")
public class CouponBatchDetailDto {
    private static final long serialVersionUID = 4509260916038777661L;

    @ApiModelProperty("批次详情id")
    private Long id;
    /**
     * 红包批次id
     */
    @ApiModelProperty("coupon_batch_list#ID")
    private Long couponBatchId;
    /**
     * 外部批次编码，鲲鹏批次号
     */
    @ApiModelProperty(value = "外部批次编码，鲲鹏批次号")
    private String batchNo;
    @ApiModelProperty(value = "红包内部名称")
    private String batchName;
    @ApiModelProperty(value = "红包名称")
    private String batchCname;
    @ApiModelProperty("鲲鹏红包状态，1-审批中，2-审批通过，3-已驳回，4-已取消，5-无效")
    @Range(min = 1, max = 4, message = "红包状态值错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer status;
    @ApiModelProperty("营销状态 1-未生效，2-活动中，3-未开始，4-已过期，5-已下架")
    private Integer marketingStatus;
    @ApiModelProperty("卡劵类型 0-满减，1-折扣，2-满减阶梯")
    @Range(min = 0, max = 1, message = "红包类型配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer couponType;
    @ApiModelProperty("门槛")
    @Range(min = 1, message = "红包门槛配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer discountSill;
    @ApiModelProperty("优惠金额")
    private Integer discountAmount;
    @ApiModelProperty("最高优惠额度")
    private Integer discountMaxAmount;
    @ApiModelProperty("折扣率")
    private Integer discountRate;
    @ApiModelProperty("库存")
    @Range(min = 1, max = 10000, message = "库存配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer stock;
    @ApiModelProperty("单日领取上线")
    @Range(min = 1, max = 10000, message = "单日领取上限配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer receiveDayLimit;
    @ApiModelProperty("单用户领取上限 -1表示不限")
    @Range(min = -1, max = 10000, message = "单用户领取上限配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer receiveUserLimit;
    private Integer receiveUserLimitType;
    @ApiModelProperty("是否可退")
    @Range(min = 0, max = 1, message = "是否可退配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer refundFlag;

    @ApiModelProperty("是否可退 鲲鹏枚举 0 可退 1 不可退")
    private Integer isRefund;

    @ApiModelProperty("活动有效期类型 1-固定时间段")
    @NotNull(message = "活动有效期类型不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer eventValidDateType;
    @ApiModelProperty("活动开始时间")
    @FutureOrPresentDay(message = "活动开始时间不能小于现在", groups = {BusinessValidation.class, PlatformValidation.class})
    private LocalDateTime eventValidBeginDate;
    @ApiModelProperty("活动结束时间")
    @FutureOrPresentDay(message = "活动结束时间不能小于现在", groups = {BusinessValidation.class, PlatformValidation.class})
    private LocalDateTime eventValidEndDate;

    private List<String> eventValidDateRange;
    private LocalDateTime currentDate = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
    private String currentDateStr = LocalDateTimeUtil.format(LocalDate.now(), "yyyy-MM-dd");

    @ApiModelProperty("剩余数量")
    private Integer remainStock;
    @ApiModelProperty("已领取数量")
    private Integer receiveStock;
    @ApiModelProperty("已使用数量")
    private Integer usedCount;
    @ApiModelProperty("使用提醒")
    private String notice;

    /**
     * 鲲鹏提交时间
     */
    private LocalDateTime commitTime;

    /**
     * 鲲鹏提交描述
     */
    private String commitMsg;
    /**
     * 鲲鹏提交状态，1-待提交，2-已提交，3-提交失败
     */
    private Integer commitStatus;

    /**
     * 红包使用说明
     */
    @ApiModelProperty("红包使用说明")
    private String useDesc;

    /**
     * 发放场景说明
     */
    @ApiModelProperty("发放场景说明")
    private String sceneDesc;

    /**
     * 同程app链接
     */
    @ApiModelProperty("同程app链接")
    private String linkUrlApp;

    /**
     * 同程小程序链接
     */
    @ApiModelProperty("同程小程序链接")
    private String linkUrlWx;

    /**
     * 所属项目
     */
    @ApiModelProperty("所属项目")
    private String projectCode;

    /**
     * 用户类型：0 不限用户
     * 1 新用户可领
     * 2 老用户可领
     */
    @ApiModelProperty("用户类型")
    private Integer memberType;

    /**
     * 是否付出 0不需要付出，1需要付出
     */
    @ApiModelProperty("是否付出")
    private Integer isUserPay;

    /**
     * 用途
     */
    @ApiModelProperty("用途")
    private String purpose;

    /**
     * 领取入口:
     * 不限制（0）/列表页（1）/详情页（2）/订单填写页（3）/销售系统（4）
     */
    @ApiModelProperty("领取入口")
    private List<Integer> takeEntrance;

    /**
     * 最后同步时间
     */
    @ApiModelProperty("最后同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 场景配置
     */
    @ApiModelProperty("场景配置")
    private String sceneConfig;

    /**
     * 扩展属性列表 满减阶梯规则等
     */
    @ApiModelProperty(value = "扩展属性列表")
    private List<ExtendInfoDto> extendList;

    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private List<ProjectDto> projectList;

    @ApiModelProperty("红包批次有效期")
    private CouponBatchValidDateDto couponBatchValidDate;

    @ApiModelProperty("关联平台信息")
    private List<PlatformVo> platformList;

    private List<Integer> platformCodeList;

    /**
     * 修改人工号
     */
    private LocalDateTime modifiedTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 是否在使用有效期内
     */
    private Boolean inValidDate;


    /**
     * 转换为红包批次详情
     *
     * @param couponBatchList
     * @return
     */
    public static List<CouponBatchDetailDto> detailDtoFromExtDto(List<CouponBatchExtDto> couponBatchList) {
        if (CollectionUtil.isEmpty(couponBatchList)) {
            return Collections.emptyList();
        }

        return couponBatchList.stream().map(CouponBatchDetailDto::detailDtoFromExtDto).collect(Collectors.toList());
    }
    /**
     * 转换为红包批次详情
     *
     * @param couponBatchCacheDto
     * @return
     */
    public static CouponBatchDetailDto detailDtoFromExtDto(CouponBatchExtDto couponBatchCacheDto) {
        CouponBatchDetailDto couponBatchDetailDto = BeanUtil.copyProperties(couponBatchCacheDto, CouponBatchDetailDto.class);
        if (CollectionUtil.isNotEmpty(couponBatchCacheDto.getProjectList())) {
            couponBatchDetailDto.setProjectCode(couponBatchCacheDto.getProjectList().stream().map(project -> String.valueOf(project.getProjectId())).collect(Collectors.joining(",")));
        }
        if (couponBatchCacheDto.getSill() != null) {
            couponBatchDetailDto.setDiscountSill(couponBatchCacheDto.getSill().intValue());
        }
        if (couponBatchCacheDto.getAmount() != null) {
            couponBatchDetailDto.setDiscountAmount(couponBatchCacheDto.getAmount().intValue());
            if (CouponTypeEnum.DISCOUNT.getCode().equals(couponBatchCacheDto.getCouponType())) {
                couponBatchDetailDto.setDiscountMaxAmount(couponBatchCacheDto.getAmount().intValue());
            }
        }
        if (couponBatchCacheDto.getMaxDiscountAmount() != null) {
            couponBatchDetailDto.setDiscountMaxAmount(couponBatchCacheDto.getAmount().intValue());
        }
        if (CollectionUtil.isNotEmpty(couponBatchCacheDto.getProjectList())) {
            couponBatchDetailDto.setProjectCode(couponBatchCacheDto.getProjectList().stream().map(project -> String.valueOf(project.getProjectId())).collect(Collectors.joining(",")));
        }
        // 计算已领取的数量
        if (couponBatchCacheDto.getRemainStock() != null) {
            couponBatchDetailDto.setRemainStock(couponBatchCacheDto.getRemainStock());
            couponBatchDetailDto.setReceiveStock(couponBatchCacheDto.getTotalStock() - couponBatchCacheDto.getRemainStock());
        }
        couponBatchDetailDto.setExtendList(couponBatchCacheDto.getExtendList());
        couponBatchDetailDto.setBatchName(couponBatchCacheDto.getCode());
        couponBatchDetailDto.setBatchCname(couponBatchCacheDto.getBatchName());
        couponBatchDetailDto.setStatus(couponBatchCacheDto.getBatchStatus());
        couponBatchDetailDto.setLinkUrlApp(couponBatchCacheDto.getAppHref());
        couponBatchDetailDto.setLinkUrlWx(couponBatchCacheDto.getMapphref());
        couponBatchDetailDto.setStock(couponBatchCacheDto.getTotalStock());
        couponBatchDetailDto.setEventValidBeginDate(couponBatchCacheDto.getBeginDate());
        couponBatchDetailDto.setEventValidEndDate(couponBatchCacheDto.getEndDate());
        couponBatchDetailDto.setReceiveUserLimit(couponBatchCacheDto.getPersonLimit());
        couponBatchDetailDto.setNotice(couponBatchCacheDto.getBrief());
        couponBatchDetailDto.setUseDesc(couponBatchCacheDto.getDetail());
        // 鲲鹏是否可退和本地是否可退相反
        couponBatchDetailDto.setRefundFlag(couponBatchCacheDto.getIsRefund() != null && couponBatchCacheDto.getIsRefund() == 1 ? 0 : 1);
        if (couponBatchCacheDto.getDayLimit() != null) {
            couponBatchDetailDto.setReceiveDayLimit(couponBatchCacheDto.getDayLimit().intValue());
        }
        couponBatchDetailDto.setDiscountRate(couponBatchCacheDto.getDiscount());
//            couponBatchDetailDto.setDiscountMaxAmount(couponBatchCacheDto.getMaxAmount());
        couponBatchDetailDto.setLastSyncTime(LocalDateTime.now());
        couponBatchDetailDto.setEventValidDateRange(Arrays.asList(LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidBeginDate().toLocalDate(), DateConst.DEFAULT_FORMAT), LocalDateTimeUtil.format(couponBatchDetailDto.getEventValidEndDate().toLocalDate(), DateConst.DEFAULT_FORMAT)));


        // 使用有效期
        CouponBatchValidDateDto couponBatchValidDateDto = new CouponBatchValidDateDto();
        couponBatchValidDateDto.setValidBeginDate(couponBatchCacheDto.getExpBeginDate());
        couponBatchValidDateDto.setValidEndDate(couponBatchCacheDto.getExpEndDate());
        couponBatchValidDateDto.setValidDateType(couponBatchCacheDto.getExpType());
        couponBatchValidDateDto.setDynamicStartDay(couponBatchCacheDto.getExpBeginDays());
        couponBatchValidDateDto.setDynamicDurationDay(couponBatchCacheDto.getExpEndDays());
        if (ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setCountdownHour(couponBatchCacheDto.getExpEndHours());
        } else if (ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDateDto.getValidDateType())) {
            couponBatchValidDateDto.setCountdownMinute(couponBatchCacheDto.getExpEndHours());
        }
        couponBatchValidDateDto.setValidDateRange(Arrays.asList(LocalDateTimeUtil.format(couponBatchValidDateDto.getValidBeginDate(), DateConst.DEFAULT_FORMAT), LocalDateTimeUtil.format(couponBatchValidDateDto.getValidEndDate(), DateConst.DEFAULT_FORMAT)));
        couponBatchDetailDto.setCouponBatchValidDate(couponBatchValidDateDto);

        //使用渠道
        if (StringUtils.isNotBlank(couponBatchCacheDto.getUseChannel())) {
            List<String> channels = JSON.parseArray(couponBatchCacheDto.getUseChannel(), String.class);
            List<PlatformVo> platformList = new ArrayList<>();
            Set<String> platformCodeSet = new HashSet<>();
            for (String channel : channels) {
                PlatformEnum platformEnum = PlatformEnum.codeOf(channel);
                if (platformEnum != null && !platformCodeSet.contains(platformEnum.getRelationChannelCode())) {
                    platformCodeSet.add(platformEnum.getRelationChannelCode());
                }
            }
            for (String platformCode : platformCodeSet) {
                PlatformVo platformVo = new PlatformVo();
                platformVo.setCode(Integer.valueOf(platformCode));
                platformList.add(platformVo);
            }
            couponBatchDetailDto.setPlatformList(platformList);
        }

        // 满减阶梯
        if (CouponTypeEnum.FULL_DISCOUNT_GRADIENT.getCode().equals(couponBatchCacheDto.getCouponType()) && CollectionUtil.isNotEmpty(couponBatchCacheDto.getExtendList())) {
            List<ExtendInfoDto> gradientExtendInfoDtoList = couponBatchCacheDto.getExtendList().stream().filter(extendInfoDto -> extendInfoDto.getAttrId() == 3).collect(Collectors.toList());
            couponBatchDetailDto.setExtendList(gradientExtendInfoDtoList);
        }

        return couponBatchDetailDto;
    }


}
