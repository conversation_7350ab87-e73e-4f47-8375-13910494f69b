package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CouponRecoverRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponRecoverRo", description = "券恢复")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CouponRecoverRequest extends CouponBaseRequest{
    /**
     * 券号
     */
    @ApiModelProperty(value = "券号", required = true)
    @NotBlank(message = "券号不能为空")
    @NotNull(message = "券号不能为空")
    private String couponNo;
}
