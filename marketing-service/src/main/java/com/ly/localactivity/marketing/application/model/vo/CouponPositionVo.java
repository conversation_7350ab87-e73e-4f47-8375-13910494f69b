package com.ly.localactivity.marketing.application.model.vo;

import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CouponPositionVo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CouponPositionVo", description = "红包点位信息")
public class CouponPositionVo implements Serializable {
    private static final long serialVersionUID = 7880057633742679783L;

    @ApiModelProperty(value = "红包点位")
    private OperationPositionVo operationPosition;

}
