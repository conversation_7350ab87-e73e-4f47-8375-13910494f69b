package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * PromotionPlanStatisticsRo
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@ApiModel(value = "PromotionPlanStatisticsRo", description = "推广计划统计请求参数")
@Data
public class PromotionPlanStatisticsRo {
    @ApiModelProperty(value = "运营点位Id", required = true)
    @NotNull
    @Min(value = 1, message = "运营点位Id必须大于0")
    private Long positionId;
}
