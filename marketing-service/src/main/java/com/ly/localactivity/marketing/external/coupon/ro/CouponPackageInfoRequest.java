package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponPackageInfoRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponPackageInfoRo", description = "券包信息")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CouponPackageInfoRequest {

    /**
     * 包装编号
     */
    @ApiModelProperty(value = "礼包号")
    private String packageNo;
    /**
     * 是否返回特殊规则，0：否 1：是
     */
    @ApiModelProperty(value = "是否返回特殊规则")
    private Boolean ruleStatus;

}
