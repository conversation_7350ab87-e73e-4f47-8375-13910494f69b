package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.PlanAutoInvalidTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionLinkTemplate;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContentLinkParam;
import com.ly.localactivity.marketing.external.daytrip.service.DayTripService;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionLinkTemplateMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentLinkParamMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanService;
import com.ly.localactivity.marketing.service.marketing.IPromotionPlanAutoInvalidService;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.build.CachedReturnPlugin;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * OperationPositionJobServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/22
 */
@Slf4j
@Service
public class PromotionPlanAutoInvalidServiceImpl implements IPromotionPlanAutoInvalidService {
    @Resource
    private OperationPromotionLinkTemplateMapper operationPromotionLinkTemplateMapper;
    @Resource
    private OperationPromotionPlanContentMapper operationPromotionPlanContentMapper;
    @Resource
    private OperationPromotionPlanContentLinkParamMapper operationPromotionPlanContentLinkParamMapper;
    @Resource
    private DayTripService dayTripService;
    @Resource
    private IOperationPromotionPlanService operationPromotionPlanService;

    /**
     * 自动无效
     *
     * @param traceId 跟踪id
     */
    @Override
    public void autoInvalid(String traceId) {
        List<OperationPromotionLinkTemplate> templateList = getAutoInvaildPromotionLink();
        SkyLogUtils.infoByMethod(log, "自动下线活动模板：" + JSON.toJSONString(templateList), traceId, "");
        if (CollectionUtil.isEmpty(templateList)) {
            return;
        }
        templateList.stream().forEach(s -> autoInvalid(s, traceId));
    }

    /**
     * 根据模板自动无效
     *
     * @param template 模板
     * @param traceId  跟踪id
     */
    private void autoInvalid(OperationPromotionLinkTemplate template, String traceId) {
        //1、找到使用该模板的所有计划内容
        SkyLogUtils.infoByMethod(log, "自动下线活动模板：" + JSON.toJSONString(template), traceId, template.getId().toString());
        List<OperationPromotionPlanContent> contentList = operationPromotionPlanContentMapper.selectByParamTemplate(template.getId());

        SkyLogUtils.infoByMethod(log, "自动下线活动内容：" + JSON.toJSONString(contentList), traceId, template.getId().toString());
        if (CollectionUtil.isEmpty(contentList)) {
            return;
        }
        //2、按模板的参数找到需要自动无效的计划
        Map<Long, String> invalidPlanIds = new ConcurrentHashMap<>();
        contentList.forEach(s -> {
            Map<PlanAutoInvalidTypeEnum, String> map = new HashMap<>();
            map.put(PlanAutoInvalidTypeEnum.DayTrip, s.getAutoInvalidParamCodes());
            invalidPlanIds.putAll(autoInvalidByParam(s, map, traceId));
        });

        SkyLogUtils.infoByMethod(log, "自动下线-开始下线：" + JSON.toJSONString(invalidPlanIds), traceId, template.getId().toString());

        //3、开始下线计划
        if (CollectionUtil.isEmpty(invalidPlanIds)) {
            return;
        }

        invalidPlanIds.forEach((k, v) -> {
            boolean res = operationPromotionPlanService.invalidByAuto(k, v);
//            boolean res = true;
            SkyLogUtils.infoByMethod(log, "自动下线完成，id：" + k.toString() + "结果：" + (res ? "成功" : "失败"), traceId, k.toString());
        });
    }

    /**
     * 按参数自动无效
     *
     * @param content 内容
     * @param codeMap 代码图
     * @param traceId 跟踪id
     */
    private Map<Long, String> autoInvalidByParam(OperationPromotionPlanContent content, Map<PlanAutoInvalidTypeEnum, String> codeMap, String traceId) {
        SkyLogUtils.infoByMethod(log, "自动下线开始处理content:" + content.getPlanId().toString() + ",params:" + JSON.toJSONString(codeMap), traceId, content.getId().toString());

        //按模板参数去找到参数值，并且根据参数值去调搜索接口判断是否在线
        List<Long> invalidPlanIds = new CopyOnWriteArrayList<>();
        Map<Long, String> invalidPlanMap = new ConcurrentHashMap<>();
        List<OperationPromotionPlanContentLinkParam> contentParams = getContentParam(content.getId());
        List<String> reason = new CopyOnWriteArrayList<>();

        codeMap.forEach((key, value) -> {
            List<String> invalidParamCodes = StrUtil.split(value, ",");
            invalidParamCodes.stream().forEach(code -> {

                Optional<OperationPromotionPlanContentLinkParam> first = contentParams.stream().filter(f -> f.getParamCode().equals(code)).findFirst();
                if (!first.isPresent()) {
                    return;
                }
                //参数值为空，不处理
                if (StringUtils.isBlank(first.get().getParamValue())) {
                    return;
                }

                //检查日游产品是否在线
                if (key.equals(PlanAutoInvalidTypeEnum.DayTrip)) {
                    boolean existBySerialId = dayTripService.existBySerialId(Collections.singletonList(first.get().getParamValue()));
                    if (!existBySerialId) {
                        reason.add(StrUtil.format("产品[{}][{}]不存在", key, first.get().getParamValue()));
                    }
                }

            });
        });

        SkyLogUtils.infoByMethod(log, "自动下线-最终下线计划：" + JSON.toJSONString(invalidPlanIds), traceId, content.getId().toString());

        if (CollectionUtil.isNotEmpty(reason)) {
            invalidPlanMap.put(content.getPlanId(), StrUtil.join(",", reason));
        }
        return invalidPlanMap;
    }

    private List<OperationPromotionLinkTemplate> getAutoInvaildPromotionLink() {
        //获取所有自动下线模板
        LambdaQueryWrapper<OperationPromotionLinkTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationPromotionLinkTemplate::getAutoInvalidFlag, 1);
        queryWrapper.eq(OperationPromotionLinkTemplate::getDeleteFlag, 0);
        return operationPromotionLinkTemplateMapper.selectList(queryWrapper);
    }

    private List<OperationPromotionPlanContentLinkParam> getContentParam(Long contentId) {
        return operationPromotionPlanContentLinkParamMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlanContentLinkParam>()
                .eq(OperationPromotionPlanContentLinkParam::getPlanContentId, contentId)
                .eq(OperationPromotionPlanContentLinkParam::getDeleteFlag, false)
        );
    }
}
