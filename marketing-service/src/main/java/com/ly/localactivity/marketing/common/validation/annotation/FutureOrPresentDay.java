package com.ly.localactivity.marketing.common.validation.annotation;

import com.ly.localactivity.marketing.common.validation.FutureOrPresentDayValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 校验未来或现在(精确到天)
 *
 * <AUTHOR>
 * @date 2024/02/28
 */
@Documented
@Constraint(validatedBy = FutureOrPresentDayValidator.class)
@Target( { ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface FutureOrPresentDay {
    String message() default "The date must be in the present or future day";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
