package com.ly.localactivity.marketing.service.marketing.impl;

import ch.qos.logback.core.pattern.ConverterUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.CommonIgnorePropConst;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.PromotionLinkTypeEnum;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContentLinkParam;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanContentLinkParamDto;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanContentLinkParamService;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionPlanContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 运营推广计划内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Service
@Slf4j
public class OperationPromotionPlanContentServiceImpl extends ServiceImpl<OperationPromotionPlanContentMapper, OperationPromotionPlanContent> implements IOperationPromotionPlanContentService {

    @Resource
    private IOperationPromotionPlanContentLinkParamService operationPromotionPlanContentLinkParamService;
    @Resource
    private OperationPromotionPlanMapper operationPromotionPlanMapper;

    @Resource
    private IOperationPromotionPlanContentService operationPromotionPlanContentService;

    /**
     * 保存
     *
     * @param contentDto 内容dto
     * @param modifier   修改人
     */
    @Override
    public void saveOrUpdate(OperationPromotionPlanContentDto contentDto, String modifier) {
        OperationPromotionPlanContent planContent = getInfoByPlanIdAndPlatform(contentDto.getPlanId(), contentDto.getPlatform());
        boolean isExist = ObjectUtil.isNotNull(planContent);
        LocalDateTime currTime = LocalDateTime.now();
        if (!isExist) {
            planContent = new OperationPromotionPlanContent();
            planContent.setCreateTime(currTime);
            planContent.setCreator(modifier);
        }
        BeanUtil.copyProperties(contentDto, planContent);
        planContent.setModifier(modifier);
        planContent.setModifiedTime(currTime);
        planContent.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
        if (isExist) {
            this.updateById(planContent);
        } else {
            this.baseMapper.insert(planContent);
        }
        // 保存参数
        PromotionLinkTypeEnum typeEnum = EnumUtil.getBy(PromotionLinkTypeEnum::getCode, contentDto.getLinkType());
        // 如果是固定链接则不需要保存链接参数
        if (ObjectUtil.equals(PromotionLinkTypeEnum.FIXED_LINK, typeEnum)) {
            return;
        }
        List<OperationPromotionPlanContentLinkParamDto> linkParamDtos = new ArrayList<>(CollectionUtil.isNotEmpty(contentDto.getLinkParams()) ? contentDto.getLinkParams().size() : 0);
        if (CollectionUtil.isNotEmpty(contentDto.getLinkParams())) {
            for (LinkParamRo linkParam : contentDto.getLinkParams()) {
                OperationPromotionPlanContentLinkParamDto linkParamDto = new OperationPromotionPlanContentLinkParamDto();
                BeanUtil.copyProperties(linkParam, linkParamDto);
                linkParamDto.setPlanContentId(planContent.getId());
                linkParamDto.setLinkTemplateParamId(planContent.getLinkTemplateId());
                linkParamDto.setPlanId(contentDto.getPlanId());
                linkParamDtos.add(linkParamDto);
            }
        }
        // 保存或更新内容 在该方法中维护链接参数
        operationPromotionPlanContentLinkParamService.saveOrUpdateBatch(linkParamDtos, planContent.getId(), modifier);
    }

    @Override
    public OperationPromotionPlanContentDto getByPlanId(Long planId) {
        OperationPromotionPlanContentDto planContentDto = new OperationPromotionPlanContentDto();
        // 获取推广计划内容
        List<OperationPromotionPlanContent> operationPromotionPlanContents = list(new LambdaQueryWrapper<OperationPromotionPlanContent>().eq(OperationPromotionPlanContent::getPlanId, planId));
        if (CollectionUtil.isEmpty(operationPromotionPlanContents)) {
            return planContentDto;
        }
        planContentDto.setLinks(new ArrayList<>(operationPromotionPlanContents.size()));

        for (OperationPromotionPlanContent content : operationPromotionPlanContents) {

            OperationPromotionPlanLinkDto linkDto = BeanUtil.toBean(content, OperationPromotionPlanLinkDto.class);
            BeanUtil.copyProperties(content, planContentDto, "links");

            PromotionLinkTypeEnum linkTypeEnum = EnumUtil.getBy(PromotionLinkTypeEnum::getCode, planContentDto.getLinkType());
            //获取链接参数
            if (ObjectUtil.equal(PromotionLinkTypeEnum.FIXED_LINK, linkTypeEnum) || ObjectUtil.equal(PromotionLinkTypeEnum.EMPTY, linkTypeEnum)) {
                linkDto.setLinkParams(Collections.emptyList());
                planContentDto.getLinks().add(linkDto);
                continue;
            }
            List<OperationPromotionPlanContentLinkParam> params = operationPromotionPlanContentLinkParamService.list(new LambdaQueryWrapper<OperationPromotionPlanContentLinkParam>()
                    .eq(OperationPromotionPlanContentLinkParam::getPlanContentId, content.getId())
                    .eq(OperationPromotionPlanContentLinkParam::getDeleteFlag, Boolean.FALSE)
            );
            if (CollectionUtil.isNotEmpty(params)) {
                List<LinkParamRo> linkParams = params.stream().map(e ->
                        LinkParamRo.builder()
                                .id(e.getId())
                                .paramCode(e.getParamCode())
                                .paramValue(e.getParamValue())
                                .valueType(e.getValueType())
                                .build()
                ).collect(Collectors.toList());
                linkDto.setLinkParams(linkParams);
            } else {
                linkDto.setLinkParams(Collections.emptyList());
            }
            planContentDto.getLinks().add(linkDto);
        }
        return planContentDto;
    }


    private OperationPromotionPlanContent getInfoByPlanIdAndPlatform(Long planId, String platform) {
        return this.getOne(new LambdaQueryWrapper<OperationPromotionPlanContent>()
                .eq(OperationPromotionPlanContent::getPlanId, planId)
                .eq(StrUtil.isNotBlank(platform), OperationPromotionPlanContent::getPlatform, platform)
                .eq(OperationPromotionPlanContent::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .last("limit 1")
        );

    }
    private Boolean updateBatch(List<OperationPromotionPlanContent> operationPromotionPlanContents) {
        operationPromotionPlanContents.forEach(this::updateById);
        return Boolean.TRUE;
    }


    /**
     * 同步字段
     *
     * @return {@link String }
     */
    @Override
    public String syncField() {
        List<OperationPromotionPlan> operationPromotionPlans = operationPromotionPlanMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlan>().ne(OperationPromotionPlan::getPlatform, PlatformEnum.ALL_PLATFORM.getCode()));
        Map<Long, OperationPromotionPlan> idOperationPromotionPlanMap = operationPromotionPlans.stream().collect(Collectors.toMap(OperationPromotionPlan::getId, Function.identity()));
        List<OperationPromotionPlanContent> operationPromotionPlanContents = list(
                new LambdaQueryWrapper<OperationPromotionPlanContent>().eq(OperationPromotionPlanContent::getPlatform,StrUtil.EMPTY)
        );
        operationPromotionPlanContents.forEach(content -> {
            OperationPromotionPlan operationPromotionPlan = idOperationPromotionPlanMap.get(content.getPlanId());
            if (ObjectUtil.isNull(operationPromotionPlan)) {
                return;
            }
            content.setPlatform(operationPromotionPlan.getPlatform());
        });

        List<List<OperationPromotionPlanContent>> listList = CollectionUtil.split(operationPromotionPlanContents, 100);
        if (CollectionUtil.isEmpty(listList)) {

            return "无数据可同步";
        }

        List<CompletableFuture<Boolean>> completableFutureList = new ArrayList<>(listList.size());
        listList.forEach(list -> {
            SkyLogUtils.infoByMethod(log, StrUtil.format("开始同步:,size{}", list.size()), "", "");
            completableFutureList.add(CompletableFuture.supplyAsync(() -> updateBatch(list)));
        });
        completableFutureList.forEach(CompletableFuture::join);

        return "";
    }


}
