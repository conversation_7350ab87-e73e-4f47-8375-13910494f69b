package com.ly.localactivity.marketing.external.coupon.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.adapter.CheckListLog;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.constants.CouponRequestHeaderConst;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.HttpSender;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.external.coupon.dto.*;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CouponCoreService
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Service
@Slf4j
public class CouponCoreService {
    @Value("${coupon.kunPeng.couponAccount}")
    private String COUPON_ACCOUNT;
    @Value("${coupon.kunPeng.couponPassword}")
    private String COUPON_PASSWORD;
    @Value("${coupon.kunPeng.couponRechargeUrl}")
    private String COUPON_RECHARGE_URL;
    @Value("${coupon.kunPeng.couponGetListUrl}")
    private String COUPON_GET_LIST_URL;
    @Value("${coupon.kunPeng.couponGetDetailUrl}")
    private String COUPON_GET_DETAIL_URL;
    @Value("${coupon.kunPeng.couponConsumeUrl}")
    private String COUPON_CONSUME_URL;
    @Value("${coupon.kunPeng.couponDiscardUrl}")
    private String COUPON_DISCARD_URL;
    @Value("${coupon.kunPeng.couponReturnUrl}")
    private String COUPON_RETURN_URL;
    @Value("${coupon.kunPeng.couponGetOrderListUrl}")
    private String COUPON_GET_ORDER_LIST_URL;
    @Value("${coupon.kunPeng.couponCheckUrl}")
    private String COUPON_CHECK_URL;
    @Value("${coupon.kunPeng.couponOccupyUrl}")
    private String COUPON_OCCUPY_URL;

    /**
     * 获取通用标头
     *
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public Map<String, String> getCommonRequestHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put(CouponRequestHeaderConst.TIMESTAMP, String.valueOf(Instant.now().getEpochSecond()));
        headers.put(CouponRequestHeaderConst.ACCOUNT, COUPON_ACCOUNT);
        headers.put(CouponRequestHeaderConst.PASSWORD, COUPON_PASSWORD);
        headers.put(CouponRequestHeaderConst.CONTENT_TYPE, CouponRequestHeaderConst.DEFAULT_CONTENT_TYPE);
        return headers;
    }

    /**
     * 发券
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponInfoExtDto}>>
     */
    public CouponCommonResult<List<CouponInfoExtDto>> recharge(CouponRechargeRequest request) {
        CouponCommonResult<List<CouponInfoExtDto>> response = HttpSender.create()
                .typeCode("couponCoreService_recharge")
                .requestId(request.getRequestId())
                .url(COUPON_RECHARGE_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<List<CouponInfoExtDto>>>() {
                });
        return response;
    }

    /**
     * 查询列表
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponInfoDetailExtDto}>>
     */
    @CheckListLog
//    @ElapsedTime(indexName = IndexCollectConst.TCZB_MARKETING_COUPON_LIST_API_TIME)
    public CouponCommonResult<List<CouponInfoDetailExtDto>> getCouponList(CouponListRequest request) {
        String version = ConfigUtils.get(ConfigConst.COUPON_API_VERSION);
        if (StringUtils.isNotBlank(version) && "2".equals(version)) {
            return getCouponList_ConnectPool(request);
        } else {
            return getCouponList_Http(request);
        }
    }

    /**
     * 查询列表
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponInfoDetailExtDto}>>
     */
    @CheckListLog
//    @ElapsedTime(indexName = IndexCollectConst.TCZB_MARKETING_COUPON_LIST_API_TIME)
    public CouponCommonResult<List<CouponInfoDetailExtDto>> getCouponList_Http(CouponListRequest request) {
        long start = System.currentTimeMillis();
        CouponCommonResult<List<CouponInfoDetailExtDto>> response = HttpSender.create()
                .typeCode("couponCoreService_getCouponList")
                .requestId(request.getRequestId())
                .url(COUPON_GET_LIST_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
                });
        SkyLogUtils.infoByMethod(log, "getCouponList ->>>>>>>>>>>getCouponList_back = " + (System.currentTimeMillis() - start), "", "");
        return response;
    }
    private static <T> T doPost(String url, String params, Map<String, String> headers, TypeReference<T> typeReference)  {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);// 创建httpPost
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpPost.setHeader(entry.getKey(), entry.getValue());
        }

        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;

        try {
            response = httpclient.execute(httpPost);

            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (HttpStatus.SC_OK == state) {
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity);
                return JSON.parseObject(jsonString, typeReference);

            } else {
                //logger.error("请求返回:"+state+"("+url+")");
            }
        } catch (ClientProtocolException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 查询列表
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponInfoDetailExtDto}>>
     */
    @CheckListLog
//    @ElapsedTime(indexName = IndexCollectConst.TCZB_MARKETING_COUPON_LIST_API_TIME)
    public CouponCommonResult<List<CouponInfoDetailExtDto>> getCouponList_ConnectPool(CouponListRequest request) {
        long start = System.currentTimeMillis();
        String timeOutJsonStr = ConfigUtils.get(ConfigConst.HTTP_CLIENT_RETRY_TIME_OUT);
        int retryTimeOut = 55;
        if (StringUtils.isNotEmpty(timeOutJsonStr)) {
            JSONObject jsonObject = JSON.parseObject(timeOutJsonStr);
            Integer timeOut = jsonObject.getInteger("COUPON_GET_LIST_URL");
            if (timeOut != null) {
                retryTimeOut = timeOut;
            }
        }
        String response = HttpClientUtil.postAndRetry(COUPON_GET_LIST_URL, JSON.toJSONString(request), getCommonRequestHeader(), 1, retryTimeOut, request.getTimeOut());
        SkyLogUtils.infoByMethod(log, "getCouponList ->>>>>>>>>>>getCouponList = cost" + (System.currentTimeMillis() - start), "", "");
        CouponCommonResult<List<CouponInfoDetailExtDto>> listCouponCommonResult = JSON.parseObject(response, new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
        });
        return listCouponCommonResult;
    }

    public CouponCommonResult<List<CouponInfoDetailExtDto>> getCouponOrderList_ConnectPool(CouponOrderListRequest request) {
        long start = System.currentTimeMillis();
        String timeOutJsonStr = ConfigUtils.get(ConfigConst.HTTP_CLIENT_RETRY_TIME_OUT);
        int retryTimeOut = 55;
        if (StringUtils.isNotEmpty(timeOutJsonStr)) {
            JSONObject jsonObject = JSON.parseObject(timeOutJsonStr);
            Integer timeOut = jsonObject.getInteger("COUPON_GET_ORDER_LIST_URL");
            if (timeOut != null) {
                retryTimeOut = timeOut;
            }
        }
        String response = HttpClientUtil.postAndRetry(COUPON_GET_ORDER_LIST_URL, JSON.toJSONString(request), getCommonRequestHeader(), 1, retryTimeOut, null);
        SkyLogUtils.infoByMethod(log, "getCouponOrderList_ConnectPool ->>>>>>>>>>> = cost" + (System.currentTimeMillis() - start)  + " res: " + JSON.toJSONString(response), "", "");
        CouponCommonResult<List<CouponInfoDetailExtDto>> listCouponCommonResult = JSON.parseObject(response, new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
        });
        return listCouponCommonResult;
    }

    /**
     * 查询券详情
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponInfoDetailExtDto}>
     */
    @CheckListLog
    public CouponCommonResult<CouponInfoDetailExtDto> getCouponDetail(CouponDetailRequest request) {
        CouponCommonResult<CouponInfoDetailExtDto> response = HttpSender.create()
                .typeCode("couponCoreService_getCouponDetail")
                .requestId(request.getRequestId())
                .url(COUPON_GET_DETAIL_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<CouponInfoDetailExtDto>>() {
                });
        return response;

//        try {
//            SkyLogUtils.infoByMethod(log, "开始调用查询券详情接口：" + JSONObject.toJSONString(request), "", "");
//            String result = HttpUtil.createPost(COUPON_GET_DETAIL_URL)
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();
//            SkyLogUtils.infoByMethod(log, "调用查询券详情接口结束：" + result, "", "");
//            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponInfoDetailExtDto>>() {
//            });
//        } catch (Exception e) {
//            SkyLogUtils.errorByMethod(log, "调用查询券详情接口失败", "", "", e);
//            return null;
//        }
    }

    /**
     * 券使用
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponSimpleInfoExtDto}>
     */
    public CouponCommonResult<CouponSimpleInfoExtDto> couponConsume(CouponConsumeRequest request) {

        try {
            long start = System.currentTimeMillis();
            String result = HttpSender.create()
                    .typeCode("couponCoreService_couponConsume")
                    .requestId(request.getRequestId())
                    .url(COUPON_CONSUME_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();

            SkyLogUtils.infoByMethod(log, StringUtils.format("调用券使用接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , request.getCouponNo()
                    , request.getRequestId());

            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponSimpleInfoExtDto>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用券使用接口失败", request.getCouponNo()
                    , request.getRequestId(), e);
            return null;
        }
    }

    /**
     * 券作废
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link Object}>
     */
    public CouponCommonResult<Object> couponDiscard(CouponDiscardRequest request) {
        try {
            long start = System.currentTimeMillis();
            String result = HttpSender.create()
                    .typeCode("couponCoreService_couponDiscard")
                    .requestId(request.getRequestId())
                    .url(COUPON_DISCARD_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();
            SkyLogUtils.infoByMethod(log, StringUtils.format("调用券作废接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , request.getCouponNo()
                    , request.getRequestId());
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<Object>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用券作废接口失败", request.getCouponNo()
                    , request.getRequestId(), e);
            return null;
        }
    }

    /**
     * 优惠券退还
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link Object}>
     */
    public CouponCommonResult<Object> couponReturn(CouponReturnRequest request) {
        try {
            long start = System.currentTimeMillis();
            SkyLogUtils.infoByMethod(log, "开始调用券退券接口：" + JSONObject.toJSONString(request), "", "");

            String result = HttpSender.create()
                    .typeCode("couponCoreService_couponReturn")
                    .requestId(request.getRequestId())
                    .url(COUPON_RETURN_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();
            SkyLogUtils.infoByMethod(log, StringUtils.format("调用退还接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , request.getCouponNo()
                    , request.getRequestId());
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<Object>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用券退券接口失败", request.getCouponNo()
                    , request.getRequestId(), e);
            return null;
        }
    }

    /**
     * 获取优惠券订单列表
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponInfoDetailExtDto}>>
     */
    public CouponCommonResult<List<CouponInfoDetailExtDto>> getCouponOrderList(CouponOrderListRequest request) {

        CouponCommonResult<List<CouponInfoDetailExtDto>> response = HttpSender.create()
                .typeCode("couponCoreService_getCouponOrderList")
                .requestId(request.getRequestId())
                .url(COUPON_GET_ORDER_LIST_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
                });
        return response;

//        try {
//            long start = System.currentTimeMillis();
//            String result = HttpUtil.createPost(ConfigUtils.get(ConfigConst.COUPON_GET_ORDER_LIST_URL))
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();
//            SkyLogUtils.infoByMethod(log, StringUtils.format("调用下单查询接口，请求：{}，响应：{}，耗时：{}"
//                            , JSONObject.toJSONString(request)
//                            , result
//                            , System.currentTimeMillis() - start)
//                    , ""
//                    , request.getRequestId());
//            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
//            });
//        } catch (Exception e) {
//            SkyLogUtils.errorByMethod(log, "调用查询订单列表接口失败", ""
//                    , request.getRequestId(), e);
//            return null;
//        }
    }

    /**
     * 校验接口
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponResultInfoExtDto}>
     */
    public CouponCommonResult<CouponResultInfoExtDto> checkCoupon(CouponCheckRequest request) {
        try {
            long start = System.currentTimeMillis();
//            String result = HttpUtil.createPost(COUPON_CHECK_URL)
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();
            String result = HttpSender.create()
                    .typeCode("couponCoreService_checkCoupon")
                    .requestId(request.getRequestId())
                    .url(COUPON_CHECK_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();

            SkyLogUtils.infoByMethod(log, StringUtils.format("调用校验接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , ""
                    , request.getRequestId());
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponResultInfoExtDto>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用券校验接口失败", "", request.getRequestId(), e);
            return null;
        }
    }

    /**
     * 占用优惠券
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponOccupyExtDto}>
     */
    public CouponCommonResult<CouponOccupyExtDto> occupyCoupon(CouponOccupyRequest request) {
        try {
            long start = System.currentTimeMillis();
//            String result = HttpUtil.createPost(COUPON_OCCUPY_URL)
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();
            String result = HttpSender.create()
                    .typeCode("couponCoreService_occupyCoupon")
                    .requestId(request.getRequestId())
                    .url(COUPON_OCCUPY_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();
            SkyLogUtils.infoByMethod(log, StringUtils.format("调用占用接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , request.getCouponNo()
                    , request.getRequestId());
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponOccupyExtDto>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用券占用接口失败", request.getCouponNo()
                    , request.getRequestId(), e);
            return null;
        }
    }
}
