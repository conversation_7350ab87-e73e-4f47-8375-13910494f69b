package com.ly.localactivity.marketing.external.coupon.service;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoDetailExtDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.UnknownHostException;
import java.nio.charset.UnsupportedCharsetException;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * HttpClient工具类
 *
 * @return
 * <AUTHOR>
 * @create 2015年12月18日
 */
@Slf4j
public class HttpClientUtil {

    static final int TIME_OUT = 1 * 1000; // 超时时间

    private static CloseableHttpClient httpClient = null;

    private static PoolingHttpClientConnectionManager phccm = null;
    private final static Object syncLock = new Object();

    private final static ResponseHandler<String> RESPONSE_HANDLER = new BasicResponseHandler();

    private static IdleConnectionMonitorThread monitorThread = null;

    public static class IdleConnectionMonitorThread extends Thread {
        private volatile HttpClientConnectionManager connMgr;
        private volatile boolean shutdown;

        public IdleConnectionMonitorThread(
                PoolingHttpClientConnectionManager connMgr) {
            super();
            this.connMgr = connMgr;
        }
        @Override
        public void run() {
            try {
                log.info(StringUtils.format("开启httpClient监视,线程id{}", Thread.currentThread().getId()));
                while (!shutdown) {
                    Thread.sleep(5000);

//                    todo 去除日志
//                    Class<PoolingHttpClientConnectionManager> poolingHttpClientConnectionManagerClass = PoolingHttpClientConnectionManager.class;
//                    Field pool = poolingHttpClientConnectionManagerClass.getDeclaredField("pool");
//                    pool.setAccessible(true);
//                    Object o = pool.get(phccm);
//                    AbstractConnPool abstractConnPool = (AbstractConnPool) o;
//                    Class<AbstractConnPool> abstractConnPoolClass = AbstractConnPool.class;
//                    Field available = abstractConnPoolClass.getDeclaredField("available");
//                    available.setAccessible(true);
//                    Object o1 = available.get(abstractConnPool);
//                    LinkedList availableList = (LinkedList) o1;  // 目前（2024-07-23） 10并发 8 连接 100并发 连接
//                    log.info(MessageFormat.format("HttpClient connection pool available size{0}，coreSize{1}",  availableList == null ? 0 : availableList.size(), Runtime.getRuntime().availableProcessors()));
                    connMgr.closeExpiredConnections(); // 关闭过期连接 连接寿命 默认策略是 永久长连接
                    connMgr.closeIdleConnections(20, TimeUnit.SECONDS); // 关闭120秒内不活动的连接 空闲连接
                }
            } catch (Exception ex) {
                log.error("httpClient监视失败 ", ex);
                shutdown();
            }
        }
        public void shutdown() {
            shutdown = true;
        }
    }

    private static void config(HttpRequestBase httpRequestBase, Integer timeOut) {
        if (timeOut == null){
            timeOut = HttpClientUtil.TIME_OUT;
        }

        // 配置请求的超时设置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(20) // 从链接池获取连接的超时时间
                .setConnectTimeout(10) // 与服务器连接超时时间，创建socket连接的超时时间  -- 修改此值需同步修改重试里超时时间设置
                .setSocketTimeout(timeOut) //socket读取数据的超时时间，从服务器获取数据的超时时间
                .build();
        httpRequestBase.setConfig(requestConfig);
    }

    /**
     * 获取HttpClient对象
     *
     * @return
     * <AUTHOR>
     * @create 2015年12月18日
     */
    public static CloseableHttpClient getHttpClient() {
        if (httpClient == null) {
            synchronized (syncLock) {
                if (httpClient == null) {
                    synchronized (syncLock){
                        httpClient = createHttpClient(400, 200);
                        // 开启监视
                        if (phccm != null){
                            if (monitorThread == null) {
                                monitorThread = new IdleConnectionMonitorThread(phccm);
                                monitorThread.start();
                            }
                        }
                    }
                }
            }
        }
        return httpClient;
    }

    /**
     * 创建HttpClient对象
     *
     * @return
     * <AUTHOR>
     * @create 2015年12月18日
     */
    public static CloseableHttpClient createHttpClient(int maxTotal,
                                                       int maxPerRoute) {
        ConnectionSocketFactory plainsf = PlainConnectionSocketFactory
                .getSocketFactory();
        LayeredConnectionSocketFactory sslsf = SSLConnectionSocketFactory
                .getSocketFactory();
        Registry<ConnectionSocketFactory> registry = RegistryBuilder
                .<ConnectionSocketFactory> create().register("http", plainsf)
                .register("https", sslsf).build();
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(
                registry);
        // 将最大连接数增加
        cm.setMaxTotal(maxTotal);
        // 将每个路由基础的连接增加
        cm.setDefaultMaxPerRoute(maxPerRoute);
        //官方推荐使用这个来检查永久链接的可用性，而不推荐每次请求的时候才去检查
        cm.setValidateAfterInactivity(2000);

        phccm = cm;

        //设置KeepAlive
        ConnectionKeepAliveStrategy myStrategy = new ConnectionKeepAliveStrategy() {

            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                // Honor 'keep-alive' header
                HeaderElementIterator it = new BasicHeaderElementIterator(
                        response.headerIterator(HTTP.CONN_KEEP_ALIVE));
                while (it.hasNext()) {
                    HeaderElement he = it.nextElement();
                    String param = he.getName();
                    String value = he.getValue();
                    if (value != null && param.equalsIgnoreCase("timeout")) {
                        try {
                            return Long.parseLong(value) * 1000;
                        } catch(NumberFormatException ignore) {
                        }
                    }
                }
                // 没设置 则60s
                return 30 * 1000L;
            }

        };

        // 请求重试处理
        HttpRequestRetryHandler httpRequestRetryHandler = new HttpRequestRetryHandler() {
            public boolean retryRequest(IOException exception,
                                        int executionCount, HttpContext context) {
                log.warn("------------------------------------->httpRequestRetryHandler.exception" + exception.getMessage(), executionCount);

                if (executionCount >= 2) {// 重试最多一次 重试时会请求新的连接
                    return false;
                }

                if (exception instanceof ConnectTimeoutException){
                    // 首次超时时间10ms 第二次重试修改获取连接超时时间为1s
                    Object requestConfig = context.getAttribute("http.request-config");// connectTimeout
                    ReflectUtil.setFieldValue(requestConfig, "connectTimeout", TIME_OUT);
                    return true;
                }

                if (exception instanceof NoHttpResponseException) {// 如果服务器丢掉了连接，那么就重试
                    log.error("------------------------------------->服务器丢掉了连接");
                    return true;
                }
                if (exception instanceof SSLHandshakeException) {// 不要重试SSL握手异常
                    return false;
                }
                if (exception instanceof InterruptedIOException) {// 超时
                    return false;
                }
                if (exception instanceof UnknownHostException) {// 目标服务器不可达
                    return false;
                }
                if (exception instanceof SSLException) {// SSL握手异常
                    return false;
                }

                HttpClientContext clientContext = HttpClientContext
                        .adapt(context);
                HttpRequest request = clientContext.getRequest();
                // 如果请求是幂等的，就再次尝试
                if (!(request instanceof HttpEntityEnclosingRequest)) {
                    return true;
                }
                return false;
            }
        };

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(cm)
//                .disableAutomaticRetries() //高并发不重试
                .setKeepAliveStrategy(myStrategy)
                .setRetryHandler(httpRequestRetryHandler).build();

        return httpClient;
    }

    private static void setPostParams(HttpPost httpost,
                                      Map<String, Object> params) {
        if (params == null){
            return;
        }
        try {
            StringEntity stringEntity = new StringEntity(JSON.toJSONString(params), "UTF-8");
            httpost.setEntity(stringEntity);
        } catch (UnsupportedCharsetException e) {
            log.error("设置post参数错误", e);
//            e.printStackTrace();
        }
    }

    private static void setPostParams(HttpPost httpost,
                                      String jsonParam) {
        if (jsonParam == null){
            return;
        }
        try {
            StringEntity stringEntity = new StringEntity(jsonParam, "UTF-8");
            httpost.setEntity(stringEntity);
        } catch (UnsupportedCharsetException e) {
            log.error("设置post参数错误", e);
        }
    }

    private static void setPostHeaders(HttpPost httpost,
                                       Map<String, String> headers) {

        if (headers == null){
            return;
        }
        Header[] nvps = new Header[headers.size()];
        Set<String> keySet = headers.keySet();
        int i = 0;
        for (String key : keySet) {
            nvps[i++] = new BasicHeader(key, headers.get(key));
        }
        httpost.setHeaders(nvps);

    }

    /**
     * post请求URL获取内容
     *
     * @param url
     * @return
     * <AUTHOR>
     * @throws IOException
     * @create 2015年12月18日
     */
    public static String post(String url, Map<String, Object> params, Map<String, String> headers, Integer timeOut) {
        long start = System.currentTimeMillis();
        log.info(MessageFormat.format("httpClient===> start: url:{0}, params{1}", url, params));
        HttpPost httppost = new HttpPost(url);
        config(httppost, timeOut);
        setPostParams(httppost, params);
        setPostHeaders(httppost, headers);
        try {
            ResponseHandler<String> handler = new BasicResponseHandler();
            String result = getHttpClient().execute(httppost, handler);
            return result;
        } catch (Exception e) {
//          e.printStackTrace();
            log.error("httpClient post error! ", e);
            throw new RuntimeException(e);
        } finally {
            log.info(MessageFormat.format("httpClient===> cost:{0}, url:{1}, params{2}", (System.currentTimeMillis() - start), url, params));
        }
    }

    /**
     * post请求URL获取内容
     *
     * @param url
     * @return
     * <AUTHOR>
     * @throws IOException
     * @create 2015年12月18日
     */
    public static String post(String url, String jsonParam, Map<String, String> headers, Integer timeOut) {
        long start = System.currentTimeMillis();
        log.info(MessageFormat.format("httpClient===> start: url:{0}, params{1}", url, jsonParam));
        HttpPost httppost = new HttpPost(url);
        config(httppost, timeOut);
        setPostParams(httppost, jsonParam);
        setPostHeaders(httppost, headers);
        try {
            String result = getHttpClient().execute(httppost, RESPONSE_HANDLER);
            return result;
        } catch (Exception e) {
//          e.printStackTrace();
            log.error("httpClient post error! ", e);
            throw new RuntimeException(e);
        } finally {
            log.info(MessageFormat.format("httpClient===> cost:{0}, url:{1}, params{2}", (System.currentTimeMillis() - start), url, jsonParam));
        }
    }

    /**
     * post请求URL获取内容
     *
     * @param url
     * @return
     * <AUTHOR>
     * @throws IOException
     * @create 2015年12月18日
     */
    public static String postAndRetry(String url, String jsonParam, Map<String, String> headers, Integer retry, Integer retryTimeOut, Integer finalTimeOut) {
        if(retry == null || retry < 0){
            retry = 1;
        }
        retry = Math.min(5, retry);
        if (retryTimeOut == null || retryTimeOut < 0){
            retryTimeOut = TIME_OUT;
        }
        if (finalTimeOut == null || finalTimeOut < 0){
            finalTimeOut = TIME_OUT;
        }
        retryTimeOut = Math.min(retryTimeOut, finalTimeOut);

        long startHttp = System.currentTimeMillis();
        log.info(MessageFormat.format("httpClient===> start: url:{0}, params{1}", url, jsonParam));

        for (int i = 0; i <= retry; i++) {
            int remainTime = (int) (finalTimeOut - (System.currentTimeMillis() - startHttp));
            if (remainTime <= 0){
                break;
            }

            int currentRetryTimeOut = Math.min(remainTime, retryTimeOut);
            if (i == retry){
                currentRetryTimeOut = remainTime;
            }

            long start = System.currentTimeMillis();
            HttpPost httppost = new HttpPost(url);
            config(httppost, currentRetryTimeOut);
            setPostParams(httppost, jsonParam);
            setPostHeaders(httppost, headers);
            try {
                String result = getHttpClient().execute(httppost, RESPONSE_HANDLER);
                log.info(MessageFormat.format("httpClient===> cost:{0}, retryCount{1}, url:{2}, params{3}", (System.currentTimeMillis() - startHttp), i + 1, url, jsonParam));
                return result;
            } catch (Exception e) {
//          e.printStackTrace();
//                log.warn("httpClient post error! ", e);
                log.info(MessageFormat.format("httpClient===>timeOut:{0}, cost:{1}, retryCount{2}, url:{3}, params{4}", retryTimeOut ,(System.currentTimeMillis() - start), i + 1, url, jsonParam));
                // throw new RuntimeException(e);
            }
        }

        return null;
    }


    /**
     * GET请求URL获取内容
     *
     * @param url
     * @return
     * <AUTHOR>
     * @create 2015年12月18日
     */
    public static String get(String url, Map<String, String> headers, Integer timeOut) {
        long start = System.currentTimeMillis();
        log.info(MessageFormat.format("httpClient===> start: url:{0}, params{1}", url));
        HttpGet httpget = new HttpGet(url);
        config(httpget, timeOut);
        setGetHeaders(httpget, headers);
        try {
            ResponseHandler<String> handler = new BasicResponseHandler();
            String result = getHttpClient().execute(httpget, handler);
            return result;
        } catch (IOException e) {
            log.error("httpClient get error! ", e);
            throw new RuntimeException(e);
        } finally {
            log.info(MessageFormat.format("httpClient===> cost:{0}, url:{1}", (System.currentTimeMillis() - start), url));
        }
    }

    private static void setGetHeaders(HttpGet httpget, Map<String, String> headers) {
        if (headers == null){
            return;
        }
        Header[] nvps = new Header[headers.size()];
        Set<String> keySet = headers.keySet();
        int i = 0;
        for (String key : keySet) {
            nvps[i++] = new BasicHeader(key, headers.get(key));
        }
        httpget.setHeaders(nvps);
    }

    public static void main(String[] args) throws IOException {


        HashMap<String, String> headers = new HashMap<>();
        headers.put("Account", "57fea3b08f1d4068ad0a7c377fa6a8d6");
        headers.put("Password", "23183fbd6c0348598ba84cdd65f65fb2");
        headers.put("Timestamp", java.lang.String.valueOf(Instant.now().getEpochSecond()));
        headers.put("Content-Type", "application/json");

//        JSONObject params = JSON.parseObject("{\"channel\":433,\"projectId\":271,\"projectIds\":[271],\"requestId\":\"ab0d73ea-7eef-466b-9ba0-18f00c7ecf78\",\"source\":2,\"status\":0,\"userkey\":\"**********\"}");
        JSONObject params = new JSONObject();
//        params.put("batchNo", "AP_AC3L54SECEB");
//        params.put("env", "dev");
        params.put("channel", 433);
        params.put("projectId", 271);
        params.put("projectIds", Arrays.asList(271));
        params.put("requestId", "ab0d73ea-7eef-466b-9ba0-18f00c7ecf78");
        params.put("source", 2);
        params.put("status", 0);
        params.put("userkey", "**********");

        long start = System.currentTimeMillis();
        String post = postAndRetry("http://mkcloud.17usoft.com/virtualcouponapi/account/getcouponlist", JSON.toJSONString(params), headers, 1, 10, 1000);
//        String post = post("http://mkcloud.t.17usoft.com/virtualcouponapi/account/getcouponlist", JSON.toJSONString(params), headers, 1000);
        System.out.println(post);
//        String post = post("http://localhost:8083/localactivity-marketingapi/test/coupon/test", params, header);
//        String post = post("https://resource.17usoft.com/localactivity-marketing/api/job/coupon/refreshWaitingAuditCouponBatchDetail", new HashMap<>(), header);
//        System.out.println(post);

//        String post1 = post("http://mkcloud.qa.17usoft.com/virtualcouponapi/account/getcouponlist", params, header);
        System.out.println(MessageFormat.format("threadId:{0}, cost:{1}", Thread.currentThread().getId(), (System.currentTimeMillis() - start)));

//        if (true){
//            return;
//        }

//        for (int i = 0; i < 10; i++) {
//            int finalI = i + 1;
//            new Thread((() -> {
//                long tStart = System.currentTimeMillis();
//                String tPost = post("http://mkcloud.t.17usoft.com/virtualcouponapi/account/getcouponlist", params, headers, 100);
////                CouponCommonResult<List<CouponInfoDetailExtDto>> response = HttpSender.create()
////                        .typeCode("couponCoreService_getCouponList")
//////                        .requestId(request.getRequestId())
////                        .url("http://mkcloud.qa.17usoft.com/virtualcouponapi/account/getcouponlist")
////                        .headers(headers)
////                        .body(params)
////                        .doPost(new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
////                        });
////                System.out.println(MessageFormat.format("num:{0}, threadId:{1}, cost:{2}", finalI, Thread.currentThread().getId(), (System.currentTimeMillis() - tStart)));
//            })).start();
//        }


        TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>> typeReference = new TypeReference<CouponCommonResult<List<CouponInfoDetailExtDto>>>() {
        };


        // URL列表数组
//        String[] urisToGet = {
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********",
//                "http://blog.csdn.net/catoop/article/details/********" };
//
//        long start = System.currentTimeMillis();
//        try {
//            int pagecount = urisToGet.length;
//            ExecutorService executors = Executors.newFixedThreadPool(pagecount);
//            CountDownLatch countDownLatch = new CountDownLatch(pagecount);
//            for (int i = 0; i < pagecount; i++) {
//                HttpGet httpget = new HttpGet(urisToGet[i]);
//                config(httpget);
//                // 启动线程抓取
//                executors
//                        .execute(new GetRunnable(urisToGet[i], countDownLatch));
//            }
//            countDownLatch.await();
//            executors.shutdown();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } finally {
//            System.out.println("线程" + Thread.currentThread().getName() + ","
//                    + System.currentTimeMillis() + ", 所有线程已完成，开始进入下一步！");
//        }
//
//        long end = System.currentTimeMillis();
//        System.out.println("consume -> " + (end - start));
    }
}