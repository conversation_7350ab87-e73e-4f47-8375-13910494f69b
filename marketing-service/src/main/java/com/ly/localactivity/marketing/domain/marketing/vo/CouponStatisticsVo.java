package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponStatisticsVo
 *
 * <AUTHOR>
 * @date 2024/4/16
 */
@ApiModel(value = "CouponStatisticsVo", description = "红包统计信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponStatisticsVo {

    private Integer type;
    @ApiModelProperty(value = "活动中数量")
    private Integer invalidCount;
    @ApiModelProperty(value = "活动中数量")
    private Integer activeCount;
    @ApiModelProperty(value = "未开始数量")
    private Integer notStartCount;
    @ApiModelProperty(value = "已过期数量")
    private Integer expiredCount;
    @ApiModelProperty(value = "已下架数量")
    private Integer deListedCount;
    @ApiModelProperty(value = "未提交数量")
    private Integer unCommitCount;
    @ApiModelProperty(value = "待审核数量")
    private Integer waitAuditCount;
    @ApiModelProperty(value = "审核中数量")
    private Integer auditingCount;
    @ApiModelProperty(value = "审核通过数量")
    private Integer passCount;
    @ApiModelProperty(value = "审核驳回数量")
    private Integer rejectCount;


    @ApiModelProperty(value = "所有活动中数量")
    private Integer allActiveCount;
    @ApiModelProperty(value = "所有待审核数量")
    private Integer allWaitAuditCount;
}
