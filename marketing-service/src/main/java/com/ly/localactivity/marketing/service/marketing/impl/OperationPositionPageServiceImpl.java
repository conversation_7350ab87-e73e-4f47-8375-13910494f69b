package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.DictTypeEnum;
import com.ly.localactivity.marketing.common.enums.StatusFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPositionPage;
import com.ly.localactivity.marketing.domain.marketing.SysDictDetail;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionPageStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageSelectVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionPageVo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPositionPageMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionPageService;
import com.ly.localactivity.marketing.service.marketing.ISysDictDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 营销运营位页面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
public class OperationPositionPageServiceImpl extends ServiceImpl<OperationPositionPageMapper, OperationPositionPage> implements IOperationPositionPageService {
    @Resource
    private BizLogUtils bizLogUtils;
    @Resource
    private OperationPositionPageMapper operationPositionPageMapper;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ISysDictDetailService dictDetailService;


    /**
     * 查询频道列表
     *
     * @return {@link List}<{@link OperationPositionPageVo}>
     */
    @Override
    public List<OperationPositionPageVo> listOperationPositionPage() {
        LambdaQueryWrapper<OperationPositionPage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(OperationPositionPage::getSortNo);
        List<OperationPositionPage> operationPositionPages = list(queryWrapper);
        // domain 转 vo
        List<OperationPositionPageVo> operationPositionPageVos = BeanUtil.copyToList(operationPositionPages, OperationPositionPageVo.class);
        List<SysDictDetail> dictDetails = dictDetailService.list(new LambdaQueryWrapper<SysDictDetail>().eq(SysDictDetail::getDictType, DictTypeEnum.PROJECT.getType()).eq(SysDictDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        Map<Integer, String> dictMap = dictDetails.stream().collect(Collectors.toMap(d -> Integer.valueOf(d.getDictValue()), SysDictDetail::getDictLabel, (a, b) -> a));
        operationPositionPageVos.parallelStream().forEach(e -> {
            Optional.ofNullable(e.getProject()).map(dictMap::get).ifPresent(e::setProjectName);
        });
        return operationPositionPageVos;
    }


    /**
     * 新增频道页(频道页名称+版本不能重复)
     *
     * @param request
     * @return {@link Boolean}
     */
    @Override
    public Boolean insertOperationPositionPage(OperationPositionPageRo request) {
        // 频道页名称 + 版本唯一性判断
        LambdaQueryWrapper<OperationPositionPage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationPositionPage::getName, request.getName())
                .ne(ObjectUtil.isNotNull(request.getId()), OperationPositionPage::getId, request.getId())
                .eq(OperationPositionPage::getVersion, request.getVersion());
        Integer exist = operationPositionPageMapper.selectCount(wrapper);
        if (exist > 0) {
            throw new ApiException("名称+版本已存在, 请重新输入");
        }
        // 频道页Code唯一性判断
        if (StringUtils.length(request.getCode()) > 20) {
            throw new ApiException("自定义Code不能超过20位");
        }

        if (count(new LambdaQueryWrapper<OperationPositionPage>()
                .ne(ObjectUtil.isNotNull(request.getId()), OperationPositionPage::getId, request.getId())
                .eq(OperationPositionPage::getCode, request.getCode())) > 0) {
            throw new ApiException("自定义Code重复");
        }

        List<SysDictDetail> dictDetails = dictDetailService.list(new LambdaQueryWrapper<SysDictDetail>().eq(SysDictDetail::getDictType, DictTypeEnum.PROJECT.getType()).eq(SysDictDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        Map<Integer, String> dictMap = dictDetails.stream().collect(Collectors.toMap(d -> Integer.valueOf(d.getDictValue()), SysDictDetail::getDictLabel, (a, b) -> a));
        Optional.of(request.getProject()).map(dictMap::get).orElseThrow(() -> new ApiException("项目不存在"));

        // vo 转 domain
        OperationPositionPage operationPositionPage = new OperationPositionPage();
        BeanUtil.copyProperties(request, operationPositionPage);
        operationPositionPage.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
        operationPositionPage.setStatus(StatusFlagEnum.NORMAL.getCode());
        String operateName = adminTokenUtils.getEmployeeNo();
        String operateNum = adminTokenUtils.getEmployeeName();
        operationPositionPage.setCreator(operateName + "-" + operateNum);
        operationPositionPage.setCreateTime(LocalDateTime.now());
        operationPositionPage.setModifier(operateName + "-" + operateNum);
        operationPositionPage.setModifiedTime(LocalDateTime.now());
        operationPositionPageMapper.insert(operationPositionPage);
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.New)
                .logType1("运营点位")
                .logType2("新增频道页")
                .content(BeanCompareUtils.compareStr(new OperationPositionPage(), operationPositionPage))
                .operateTime(LocalDateTime.now())
                .operator(operateName)
                .operatorNum(operateNum)
                .build());
        return true;
    }


    /**
     * 修改频道页(频道页名称+版本不能重复)
     *
     * @param request
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateOperationPositionPage(OperationPositionPageRo request) {
        OperationPositionPage oldOperationPositionPage = getById(request.getId());
        if (oldOperationPositionPage == null) {
            throw new ApiException("修改失败，未查询到有效数据！");
        }
        // 检查是否修改过
        OperationPositionPageRo oldOperationPositionPageRo = BeanUtil.copyProperties(oldOperationPositionPage, OperationPositionPageRo.class);
        if (oldOperationPositionPageRo.equals(request)) {
            return true;
        }

        List<SysDictDetail> dictDetails = dictDetailService.list(new LambdaQueryWrapper<SysDictDetail>().eq(SysDictDetail::getDictType, DictTypeEnum.PROJECT.getType()).eq(SysDictDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        Map<Integer, String> dictMap = dictDetails.stream().collect(Collectors.toMap(d -> Integer.valueOf(d.getDictValue()), SysDictDetail::getDictLabel, (a, b) -> a));
        Optional.of(request.getProject()).map(dictMap::get).orElseThrow(() -> new ApiException("项目不存在"));

        // 检查修改后的频道页名称 + 版本是否已存在
        LambdaQueryWrapper<OperationPositionPage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationPositionPage::getName, request.getName())
                .ne(ObjectUtil.isNotNull(request.getId()), OperationPositionPage::getId, request.getId())
                .eq(OperationPositionPage::getVersion, request.getVersion());
        int exist = operationPositionPageMapper.selectCount(wrapper);
        if (exist > 0) {
            throw new ApiException("频道页名称+版本，不能重复！");
        }
        // 频道页Code唯一性判断
        LambdaQueryWrapper<OperationPositionPage> wrapperCode = new LambdaQueryWrapper<>();
        wrapperCode.eq(OperationPositionPage::getCode, request.getCode());
        wrapperCode.ne(ObjectUtil.isNotNull(request.getId()), OperationPositionPage::getId, request.getId());
        exist = operationPositionPageMapper.selectCount(wrapperCode);
        if (exist > 0) {
            throw new ApiException("频道页Code不能重复！");
        }
        // vo 转 domain
        OperationPositionPage operationPositionPage = new OperationPositionPage();
        BeanUtil.copyProperties(request, operationPositionPage);
        String operateName = adminTokenUtils.getEmployeeNo();
        String operateNum = adminTokenUtils.getEmployeeName();
        operationPositionPage.setModifier(operateName + "-" + operateNum);
        operationPositionPage.setModifiedTime(LocalDateTime.now());
        LambdaUpdateWrapper<OperationPositionPage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OperationPositionPage::getId, operationPositionPage.getId());
        operationPositionPageMapper.update(operationPositionPage, updateWrapper);
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("运营点位")
                .logType2("修改频道页")
                .content(BeanCompareUtils.compareStr(oldOperationPositionPage, operationPositionPage))
                .operateTime(LocalDateTime.now())
                .operator(operateName)
                .operatorNum(operateNum)
                .build());
        return true;
    }

    /**
     * 频道页设为有效|无效
     *
     * @param request
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateOperationPositionPageStatus(OperationPositionPageStatusRo request) {
        OperationPositionPage oldOperationPositionPage = getById(request.getId());
        if (oldOperationPositionPage == null) {
            throw new ApiException("设置失败，未查询到有效数据！");
        }
        String operateName = adminTokenUtils.getEmployeeNo();
        String operateNum = adminTokenUtils.getEmployeeName();
        OperationPositionPage operationPositionPage = new OperationPositionPage();
        operationPositionPage.setId(request.getId());
        operationPositionPage.setStatus(request.getStatus());
        operationPositionPage.setModifiedTime(LocalDateTime.now());
        operationPositionPage.setModifier(operateName + "-" + operateNum);
        updateById(operationPositionPage);
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("运营点位")
                .logType2("频道页设为" + (Objects.equals(request.getStatus(), StatusFlagEnum.NORMAL.getCode()) ? "有效" : "无效"))
                .content(BeanCompareUtils.compareStr(oldOperationPositionPage, operationPositionPage))
                .operateTime(LocalDateTime.now())
                .operator(operateName)
                .operatorNum(operateNum)
                .build());
        return true;
    }


    /**
     * 根据 id 查询频道详情
     *
     * @param id
     * @return {@link OperationPositionPageVo}
     */
    @Override
    public OperationPositionPageVo detailOperationPositionPage(Long id) {
        OperationPositionPage operationPositionPage = operationPositionPageMapper.selectById(id);
        if (operationPositionPage == null) {
            throw new ApiException("频道页不存在");
        }
        return BeanUtil.copyProperties(operationPositionPage, OperationPositionPageVo.class);
    }

    /**
     * 搜索频道页页面
     *
     * @param keyword 关键词
     * @return {@link List}<{@link OperationPositionPageSelectVo}>
     */
    @Override
    public List<OperationPositionPageSelectVo> searchOperationPositionPage(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return null;
        }
        List<OperationPositionPage> list = baseMapper.selectList(new LambdaQueryWrapper<OperationPositionPage>()
                .like(OperationPositionPage::getName, keyword)
                .eq(OperationPositionPage::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(OperationPositionPage::getSortNo));
        return list.stream().map(e -> OperationPositionPageSelectVo.builder()
                .label(e.getName() + "V" + e.getVersion())
                .value(e.getId())
                .build()
        ).collect(Collectors.toList());
    }
}
