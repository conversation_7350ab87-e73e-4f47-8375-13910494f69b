package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.LinkUrlTypeEnum;
import com.ly.localactivity.marketing.common.enums.StatusFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionLinkTemplate;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent;
import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplatePageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.LinkParamVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplatePageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplateParamsVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplateVo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionLinkTemplateMapper;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanContentMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionLinkTemplateParamService;
import com.ly.localactivity.marketing.service.marketing.IOperationPromotionLinkTemplateService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 运营推广链接模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
@Service
public class OperationPromotionLinkTemplateServiceImpl extends ServiceImpl<OperationPromotionLinkTemplateMapper, OperationPromotionLinkTemplate> implements IOperationPromotionLinkTemplateService {
    @Resource
    private BizLogUtils bizLogUtils;
    @Resource
    private OperationPromotionPlanContentMapper operationPromotionPlanContentMapper;
    @Resource
    private IOperationPromotionLinkTemplateParamService operationPromotionLinkTemplateParamService;
    @Resource
    private OperationPromotionLinkTemplateServiceImpl operationPromotionLinkTemplateService;
    @Resource
    private AdminTokenUtils adminTokenUtils;


    /**
     * 查询链接模板列表
     *
     * @return {@link List}<{@link OperationPromotionLinkTemplateVo}>
     */
    @Override
    public OperationPromotionLinkTemplatePageVo listOperationPromotionLinkTemplate(OperationPromotionLinkTemplatePageRo request) {
        LambdaQueryWrapper<OperationPromotionLinkTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationPromotionLinkTemplate::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        queryWrapper.eq(OperationPromotionLinkTemplate::getPlatform, request.getPlatform());
        if (!StringUtils.isBlank(request.getCustomerName())) {
            queryWrapper.like(OperationPromotionLinkTemplate::getCustomerName, request.getCustomerName());
        }
        queryWrapper.orderByDesc(OperationPromotionLinkTemplate::getStatus);
        queryWrapper.orderByDesc(OperationPromotionLinkTemplate::getModifiedTime);
        Page<OperationPromotionLinkTemplate> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<OperationPromotionLinkTemplate> linkTemplatePages = this.page(page, queryWrapper);
        List<OperationPromotionLinkTemplate> linkTemplates = linkTemplatePages.getRecords();
        // 封装分页结果
        OperationPromotionLinkTemplatePageVo operationPromotionLinkTemplatePageVo = new OperationPromotionLinkTemplatePageVo();
        operationPromotionLinkTemplatePageVo.setTotal(linkTemplatePages.getTotal());
        if (CollectionUtils.isEmpty(linkTemplates)) {
            operationPromotionLinkTemplatePageVo.setOperationPromotionLinkTemplateVos(new ArrayList<>());
            return operationPromotionLinkTemplatePageVo;
        }
        List<OperationPromotionLinkTemplateVo> operationPromotionLinkTemplateVos
                = BeanUtil.copyToList(linkTemplates, OperationPromotionLinkTemplateVo.class);
        // 自动验证下架参数String转数组
        for (int i = 0; i < linkTemplates.size(); i++) {
            OperationPromotionLinkTemplate obj = linkTemplates.get(i);
            if (obj.getAutoInvalidParamCodes() != null) {
                String[] autoInvalidParamCodes = obj.getAutoInvalidParamCodes().split(",");
                operationPromotionLinkTemplateVos.get(i).setAutoInvalidParamCodes(autoInvalidParamCodes);
            }
        }
        // 给 list 中每个对象添加引用次数
        operationPromotionLinkTemplateVos.forEach(linkTemplateVo ->
                linkTemplateVo.setCitationCount(operationPromotionPlanContentMapper.selectCount(
                        Wrappers.<OperationPromotionPlanContent>lambdaQuery()
                                .eq(OperationPromotionPlanContent::getLinkTemplateId, linkTemplateVo.getId()))));
        operationPromotionLinkTemplatePageVo.setOperationPromotionLinkTemplateVos(operationPromotionLinkTemplateVos);
        return operationPromotionLinkTemplatePageVo;
    }

    /**
     * 根据 id 查询链接模板详情
     *
     * @param id
     * @return {@link OperationPromotionLinkTemplateParamsVo}
     */
    @Override
    public OperationPromotionLinkTemplateParamsVo getOperationPromotionLinkTemplateById(Long id) {
        // 获取链接模板
        OperationPromotionLinkTemplate operationPromotionLinkTemplate = this.getById(id);
        if (operationPromotionLinkTemplate == null) {
            return null;
        }
        OperationPromotionLinkTemplateParamsVo operationPromotionLinkTemplateParamVo =
                BeanUtil.copyProperties(operationPromotionLinkTemplate, OperationPromotionLinkTemplateParamsVo.class);
        if (operationPromotionLinkTemplate.getAutoInvalidParamCodes() != null) {
            operationPromotionLinkTemplateParamVo.setAutoInvalidParamCodes(operationPromotionLinkTemplate.getAutoInvalidParamCodes().split(","));
        }
        // 获取链接参数
        List<LinkParamVo> linkParamVos = operationPromotionLinkTemplateParamService.getLinkTemplateParamsByLinkTemplateId(id);
        operationPromotionLinkTemplateParamVo.setLinkParams(linkParamVos);
        return operationPromotionLinkTemplateParamVo;
    }

    /**
     * 新增|修改链接模板
     *
     * @param request
     * @return boolean
     */
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    @Override
    public Boolean saveOrUpdateOperationPromotionLinkTemplate(OperationPromotionLinkTemplateRo request) {
        // 无 id 做新增, 有 id 做编辑
        boolean isEdit = false, result = false;
        OperationPromotionLinkTemplate oldOperationPromotionLinkTemplate = new OperationPromotionLinkTemplate(); // 记录日志用
        OperationPromotionLinkTemplate operationPromotionLinkTemplate = new OperationPromotionLinkTemplate();
        String modifier = adminTokenUtils.getModifier();
        if (ObjectUtils.isEmpty(request.getId())) {
            operationPromotionLinkTemplate
                    = BeanUtil.copyProperties(request, OperationPromotionLinkTemplate.class);
            operationPromotionLinkTemplate.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            operationPromotionLinkTemplate.setStatus(StatusFlagEnum.NORMAL.getCode());
            operationPromotionLinkTemplate.setCreateTime(LocalDateTime.now());
            operationPromotionLinkTemplate.setCreator(modifier);
            operationPromotionLinkTemplate.setModifiedTime(LocalDateTime.now());
            operationPromotionLinkTemplate.setModifier(modifier);
            operationPromotionLinkTemplate.setLinkUrlType(LinkUrlTypeEnum.NATIVE.getCode()); // 原生链接|同程APP
            if (request.getAutoInvalidParamCodes() != null) {
                operationPromotionLinkTemplate.setAutoInvalidParamCodes(String.join(",", request.getAutoInvalidParamCodes()));
            }
            // webview 链接
            if (LinkUrlTypeEnum.WEBVIEW.getCode().equals(request.getLinkUrlType())) {
                if (StringUtils.isBlank(request.getLinkUrlPrefix())) {
                    throw new ApiException("链接前缀不能为空！");
                }
                operationPromotionLinkTemplate.setLinkUrlType(LinkUrlTypeEnum.WEBVIEW.getCode());
                operationPromotionLinkTemplate.setLinkUrlPrefix(request.getLinkUrlPrefix());
            }
            // 链接模板持久化
            operationPromotionLinkTemplateService.save(operationPromotionLinkTemplate);
            List<LinkParamRo> linkParamRos = request.getLinkParams();
            // 链接参数持久化
            result = operationPromotionLinkTemplateParamService.saveOperationPromotionLinkTemplateParams(linkParamRos, operationPromotionLinkTemplate.getId());
        } else {
            isEdit = true;
            oldOperationPromotionLinkTemplate = operationPromotionLinkTemplateService.getById(request.getId());
            if (oldOperationPromotionLinkTemplate == null) {
                throw new ApiException("修改失败，未查询到有效数据！");
            }
            result = operationPromotionLinkTemplateService.updateOperationPromotionLinkTemplate(request, oldOperationPromotionLinkTemplate);
        }
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(isEdit ? LogActionEnum.Update : LogActionEnum.New)
                .logType1("推广计划")
                .logType2(isEdit ? "修改" : "新增" + "链接模板")
                .content(BeanCompareUtils.compareStr(oldOperationPromotionLinkTemplate, operationPromotionLinkTemplate))
                .operateTime(LocalDateTime.now())
                .operator(modifier.substring(0, modifier.indexOf("[")))
                .operatorNum(modifier.substring(modifier.indexOf("[") + 1, modifier.indexOf("]")))
                .build());
        return result;
    }

    /**
     * 链接模板设为有效|无效
     *
     * @param request
     * @return boolean
     */
    @Override
    public Boolean updateOperationPromotionLinkTemplateStatus(OperationPromotionLinkTemplateStatusRo request) {
        OperationPromotionLinkTemplate oldOperationPromotionLinkTemplate = this.getById(request.getId());
        if (oldOperationPromotionLinkTemplate == null) {
            throw new ApiException("设置失败，未查询到有效数据！");
        }
        String modifier = adminTokenUtils.getModifier();
        OperationPromotionLinkTemplate operationPromotionLinkTemplate = new OperationPromotionLinkTemplate();
        operationPromotionLinkTemplate.setId(request.getId());
        operationPromotionLinkTemplate.setStatus(request.getStatus());
        operationPromotionLinkTemplate.setModifier(modifier);
        operationPromotionLinkTemplate.setModifiedTime(LocalDateTime.now());
        this.updateById(operationPromotionLinkTemplate);
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("推广计划")
                .logType2("链接模板设为有效|无效")
                .content(BeanCompareUtils.compareStr(oldOperationPromotionLinkTemplate, oldOperationPromotionLinkTemplate))
                .operateTime(LocalDateTime.now())
                .operator(modifier.substring(0, modifier.indexOf("[")))
                .operatorNum(modifier.substring(modifier.indexOf("[") + 1, modifier.indexOf("]")))
                .build());
        return true;
    }


    /**
     * 编辑链接模板
     *
     * @param request
     * @return boolean
     */
    public Boolean updateOperationPromotionLinkTemplate(OperationPromotionLinkTemplateRo request, OperationPromotionLinkTemplate oldOperationPromotionLinkTemplate) {
        oldOperationPromotionLinkTemplate
                = BeanUtil.copyProperties(request, OperationPromotionLinkTemplate.class);
        oldOperationPromotionLinkTemplate.setModifiedTime(LocalDateTime.now());
        oldOperationPromotionLinkTemplate.setModifier(adminTokenUtils.getModifier());
        if (request.getAutoInvalidParamCodes() != null) {
            oldOperationPromotionLinkTemplate.setAutoInvalidParamCodes(String.join(",", request.getAutoInvalidParamCodes()));
        }
        // 同程APP链接 | 原生链接
        oldOperationPromotionLinkTemplate.setLinkUrlPrefix("");
        oldOperationPromotionLinkTemplate.setLinkUrlType(LinkUrlTypeEnum.NATIVE.getCode());
        // 微信小程序链接
        if (LinkUrlTypeEnum.WEBVIEW.getCode().equals(request.getLinkUrlType())) {
            if (StringUtils.isBlank(request.getLinkUrlPrefix())) {
                throw new ApiException("链接前缀不能为空！");
            }
            oldOperationPromotionLinkTemplate.setLinkUrlPrefix(request.getLinkUrlPrefix());
            oldOperationPromotionLinkTemplate.setLinkUrlType(LinkUrlTypeEnum.WEBVIEW.getCode());
        }
        // 链接模板持久化
        operationPromotionLinkTemplateService.updateById(oldOperationPromotionLinkTemplate);
        // 链接参数持久化
        if (!CollectionUtils.isEmpty(request.getLinkParams()) && !ObjectUtil.isNull(request.getId())) {
            operationPromotionLinkTemplateParamService.updateLinkTemplateParams(request.getLinkParams(), request.getId());
        }
        return true;
    }
}
