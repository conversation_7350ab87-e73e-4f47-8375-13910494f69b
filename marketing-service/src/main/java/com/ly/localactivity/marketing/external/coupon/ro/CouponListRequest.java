package com.ly.localactivity.marketing.external.coupon.ro;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CouponListRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CouponListRo", description = "获取券列表请求参数")
public class CouponListRequest extends CouponBaseRequest {
    /**
     * 红包状态
     * 0： 全部
     * 1-未使用；2-已占用；3-已使用；4-已作废；5_已过期
     */
    @ApiModelProperty(value = "红包状态", required = true)
    @NotNull(message = "红包状态不能为空")
    private Integer status;

    /**
     * 单红包批次号
     */
    @ApiModelProperty(value = "单红包批次号")
    private List<String> batchNoList;
    /**
     * 子渠道
     */
    @ApiModelProperty(value = "子渠道")
    private Integer subChannel;
    /**
     * 1可用,2未生效
     */
    @ApiModelProperty(value = "1可用,2未生效")
    private Integer subStatus;
    /**
     * 根据不同的项目传参不同
     */
    @ApiModelProperty(value = "根据不同的项目传参不同")
    private JSONObject extRuleInfo;
    /**
     * 是否返回券的使用规则
     * 0-不返回(默认),1-返回
     */
    @ApiModelProperty(value = "是否返回券的使用规则")
    private Integer ruleStatus;

    /**
     * 0:不包含酒店(默认) 1：包含酒店 - 查询酒店营销接口；订单相关流程请勿调用
     */
    @ApiModelProperty(value = "是否包含酒店")
    private Integer containHotel;
    /**
     * 站外平台id-会员thirdpartyId（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外平台id")
    private Integer outsidePartyId;
    /**
     * 站外会员id（支付宝小程序登陆后必传）
     */
    @ApiModelProperty(value = "站外会员id")
    private String outsideUnionId;

    /**
     * 超时时间
     */
    private Integer timeOut;

    private Integer seconds;

    private Integer threadCount;

    private Integer isDefault;

}
