package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRule;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchRuleDetailMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchRuleDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包使用规则明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class CouponBatchRuleDetailServiceImpl extends ServiceImpl<CouponBatchRuleDetailMapper, CouponBatchRuleDetail> implements ICouponBatchRuleDetailService {
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ICouponBatchRuleDetailService couponBatchRuleDetailService;
    @Resource
    private CouponBatchRuleDetailMapper couponBatchRuleDetailMapper;


    @Override
    public Boolean saveOrUpdate(CouponBatchRule couponBatchRule, List<String> dataIdList) {
        LambdaQueryWrapper<CouponBatchRuleDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchRuleDetail::getCouponBatchRuleId, couponBatchRule.getId());
        List<CouponBatchRuleDetail> oldCouponBatchRuleDetailList = couponBatchRuleDetailMapper.selectList(lambdaQueryWrapper);
        Map<String, CouponBatchRuleDetail> dataIdMap = oldCouponBatchRuleDetailList.stream().collect(Collectors.toMap(CouponBatchRuleDetail::getDataId, Function.identity(), (a, b) -> a));

        List<Long> validIdList = new ArrayList<>();
        List<CouponBatchRuleDetail> insertList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtil.isNotEmpty(dataIdList)) {
            for (String dataId : dataIdList) {
                if (dataIdMap.containsKey(dataId)) {
                    validIdList.add(dataIdMap.get(dataId).getId());
                    continue;
                }

                CouponBatchRuleDetail couponBatchRuleDetail = new CouponBatchRuleDetail();
                couponBatchRuleDetail.setCouponBatchId(couponBatchRule.getCouponBatchId());
                couponBatchRuleDetail.setCouponBatchRuleId(couponBatchRule.getId());
                couponBatchRuleDetail.setLimitType(couponBatchRule.getLimitType());
                couponBatchRuleDetail.setDataId(String.valueOf(dataId));
                couponBatchRuleDetail.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
                couponBatchRuleDetail.setModifier(adminTokenUtils.getModifier());
                couponBatchRuleDetail.setCreator(adminTokenUtils.getModifier());
                couponBatchRuleDetail.setCreateTime(now);
                couponBatchRuleDetail.setModifiedTime(now);
                insertList.add(couponBatchRuleDetail);
            }
        }

        // 批量新增
        if (CollectionUtil.isNotEmpty(insertList)) {
            couponBatchRuleDetailMapper.insertBatchSomeColumn(insertList);
        }
        // 无效数据
        List<Long> invalidIdList = oldCouponBatchRuleDetailList.stream().map(CouponBatchRuleDetail::getId).filter(id -> !validIdList.contains(id)).collect(Collectors.toList());
        couponBatchRuleDetailService.deleteByIds(invalidIdList);

        return true;
    }

    /**
     * 按ids删除
     *
     * @param invalidIdList 无效id列表
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteByIds(List<Long> invalidIdList) {
        if (CollectionUtil.isEmpty(invalidIdList)) {
            return true;
        }
        couponBatchRuleDetailMapper.deleteByIds(invalidIdList, adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo());
        return true;
    }

    /**
     * 按优惠券批量规则id选择
     *
     * @param ruleIdList 规则id列表
     * @return {@link Map}<{@link Long}, {@link List}<{@link CouponBatchRuleDetail}>>
     */
    @Override
    public Map<Long, List<CouponBatchRuleDetail>> selectByCouponBatchRuleId(List<Long> ruleIdList) {
        if (CollectionUtil.isEmpty(ruleIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchRuleDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchRuleDetail::getCouponBatchRuleId, ruleIdList);
        List<CouponBatchRuleDetail> couponBatchRuleDetailList = couponBatchRuleDetailService.list(lambdaQueryWrapper);
        return couponBatchRuleDetailList.stream().collect(Collectors.groupingBy(CouponBatchRuleDetail::getCouponBatchRuleId));
    }
}
