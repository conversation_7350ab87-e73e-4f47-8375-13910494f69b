package com.ly.localactivity.marketing.common.utils;

import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * BeanCopyUtils
 *
 * <AUTHOR>
 * @date 2024/5/27
 */
public class BeanCopyUtils {

    private BeanCopyUtils(){

    }

    public static <O,V> List<V> copyBeanList(List<O> list, Class<V> clazz){
        return list.stream().map(o-> copyBean(o,clazz)).collect(Collectors.toList());
    }

    public static <V>  V copyBean(Object source,Class<V> clazz){
        V result=null;
        //创建目标对象
        try {
            result= clazz.newInstance();
            //实现属性拷贝
            BeanUtils.copyProperties(source,result);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
        //返回结果
        return result;
    }
}
