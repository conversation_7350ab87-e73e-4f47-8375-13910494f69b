package com.ly.localactivity.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanDto
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@ApiModel("推广计划对象")
@Data
public class OperationPromotionPlanDto {
    @ApiModelProperty(value = "id", required = true)
    private Long id;
    @ApiModelProperty(value = "推广计划名称", required = true)
    private String name;
    @ApiModelProperty(value = "推广计划id", required = true)
    private Long positionId;
    @ApiModelProperty(value = "平台", required = true)
    private String platform;
    @ApiModelProperty(value = "推广方式", required = true)
    private Integer promoteType;
    @ApiModelProperty(value = "有效期类型，1-长期有效，2-固定日期段", required = true)
    private Integer validDateType;
    @ApiModelProperty(value = "有效开始日期")
    private LocalDateTime validStartDate;
    @ApiModelProperty(value = "有效结束日期")
    private LocalDateTime validEndDate;
    @ApiModelProperty(value = "顺位", required = true)
    private Integer indexValue;
    @ApiModelProperty(value = "是否置顶/锁定")
    private Boolean topFlag;
    @ApiModelProperty(value = "是否兜底")
    private Boolean defaultFlag;
    @ApiModelProperty(value = "默认展示展示方式，1-最后顺位，2-兜底展示")
    private Integer defaultShowType;
    @ApiModelProperty(value = "推广原因")
    private String reasonType;
    @ApiModelProperty(value = "推广原因备注")
    private String reasonRemark;
    @ApiModelProperty(value = "有效标记")
    private Boolean validFlag;
    @ApiModelProperty(value = "地区id")
    private List<Long> regionIds;
    @ApiModelProperty(value = "图片地址")
    private String imageUrl;
    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "推广链接")
    private String linkUrl;
}
