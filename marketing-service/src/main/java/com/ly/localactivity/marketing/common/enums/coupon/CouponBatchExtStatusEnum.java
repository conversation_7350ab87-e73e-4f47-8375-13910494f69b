package com.ly.localactivity.marketing.common.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 鲲鹏券批次状态
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
@AllArgsConstructor
@Getter
public enum CouponBatchExtStatusEnum {
    Draft(0, "草稿"),
    InApproval(1, "审批中"),
    Normal(2, "正常"),
    Reject(3, "驳回"),
    Cancel(4, "取消"),
    Invalid(5, "失效"),
    Cancellation(6, "作废"),
    Generating(7, "兑换码生成中"),
    Pending_Generate(8, "兑换码待生成"),
    USED_EXPIRED(9, "使用有效期已过期"),
    ;
    private Integer code;
    private String name;
}
