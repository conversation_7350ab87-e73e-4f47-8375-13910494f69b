package com.ly.localactivity.marketing.external.coupon.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.constants.DateConst;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.common.utils.TemplateUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchValidDateDto;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.HttpCookie;
import java.time.LocalDateTime;
import java.util.*;

/**
 * CouponAdminService
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Service
@Slf4j
public class CouponAdminService {
    private static final String ACCESS_TOKEN = "access_token";
    private static final String TOKEN = "token";
    @Resource
    private AdminTokenUtils adminTokenUtils;

    /**
     * 批量创建鲲鹏红包
     *
     * @param couponBatchDetailDtoList 优惠券批次详细信息dto列表
     * @return {@link Map }<{@link String }, {@link CouponCommonResult }<{@link String }>>
     */
    public Map<Long, CouponCommonResult<String>> batchCreateOrUpdateActivity(List<CouponBatchDetailDto> couponBatchDetailDtoList) {
        Map<Long, CouponCommonResult<String>> result = new HashMap<>(16);
        couponBatchDetailDtoList.forEach(couponBatchDetailDto -> {
            CouponCommonResult<String> couponCommonResult = createOrUpdateActivity(couponBatchDetailDto);
            if (ObjectUtil.isNotNull(couponCommonResult)) {
                result.put(couponBatchDetailDto.getId(), couponCommonResult);
            }
        });
        return result;
    }

    /**
     * 批量提交审核
     *
     * @param batchNoList 批号列表
     * @return {@link Map }<{@link String }, {@link CouponCommonResult }<{@link String }>>
     */
    public Map<String, CouponCommonResult<String>> batchApproveActivity(List<String> batchNoList) {
        Map<String, CouponCommonResult<String>> result = new HashMap<>(16);
        batchNoList.forEach(batchNo -> {
            CouponCommonResult<String> couponCommonResult = approveActivity(batchNo);
            if (ObjectUtil.isNotNull(couponCommonResult)) {
                result.put(batchNo, couponCommonResult);
            }
        });
        return result;
    }

    /**
     * 创建鲲鹏红包
     *
     * @param couponBatchDetailDto
     * @return
     */
    public CouponCommonResult<String> createOrUpdateActivity(CouponBatchDetailDto couponBatchDetailDto) {
        String requestTemplate = ConfigUtils.get(ConfigConst.COUPON_CREATE_REQUEST_TEMPLATE);
        if (StringUtils.isEmpty(requestTemplate)) {
            SkyLogUtils.warnByMethod(log, "请求模版未配置", "", "");
            return null;
        }
        JSONObject requestTemplateMap = JSON.parseObject(requestTemplate);
        Object template = requestTemplateMap.get(String.valueOf(couponBatchDetailDto.getCouponType()));
        if (template == null) {
            SkyLogUtils.warnByMethod(log, "请求模版未配置", "", "");
            return null;
        }

        if (ObjectUtil.isNull(couponBatchDetailDto.getCouponBatchValidDate())) {
            SkyLogUtils.warnByMethod(log, "红包有效期未配置", "", "");
            return null;
        }

        // 替换模板中的参数
        Map<String, String> parameters = getParameters(couponBatchDetailDto);
        String request = TemplateUtils.getContent(String.valueOf(template), parameters);
        // 替换换行符
        request = request.replaceAll("\n", "\\\\n");
        try {
            SkyLogUtils.infoByMethod(log, "开始调用鲲鹏创建红包活动接口：" + JSONObject.toJSONString(request), "", "");
            String result = HttpUtil.createPost(ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_URL))
                    .cookie(new HttpCookie(ACCESS_TOKEN, ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_TOKEN)))
                    .header(TOKEN, ConfigUtils.get(ConfigConst.COUPON_CLOSE_DEFAULT_TOKEN))
                    .body(request)
                    .execute()
                    .body();
            SkyLogUtils.infoByMethod(log, "调用鲲鹏创建红包活动接口：" + result, "", "");
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<String>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用鲲鹏创建红包活动接口失败", "", "", e);
            return null;
        }
    }

    /**
     * 鲲鹏红包提交审核
     *
     * @param batchNo
     * @return
     */
    public CouponCommonResult<String> approveActivity(String batchNo) {
        try {
            SkyLogUtils.infoByMethod(log, "开始调用鲲鹏红包提交审核接口：" + JSONObject.toJSONString("request"), "", "");
            String admin = StringUtils.isBlank(adminTokenUtils.getEmployeeNo()) ? ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_ADMIN) : adminTokenUtils.getEmployeeNo();
            String userId = org.apache.commons.lang3.StringUtils.getDigits(admin);
            String result = HttpUtil.createGet(ConfigUtils.get(ConfigConst.COUPON_START_APPROVE_URL) + "?batchId=" + batchNo + "&userId=" + userId)
                    .cookie(new HttpCookie(ACCESS_TOKEN, ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_TOKEN)))
                    .header(TOKEN, ConfigUtils.get(ConfigConst.COUPON_CLOSE_DEFAULT_TOKEN))
                    .execute()
                    .body();
            SkyLogUtils.infoByMethod(log, "调用鲲鹏红包提交审核接口：" + result, "", "");
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<String>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用鲲鹏鲲鹏红包提交审核失败", "", "", e);
            return null;
        }
    }

    /**
     * 鲲鹏红包关闭
     */
    public CouponCommonResult<String> closeActivity(String batchNo) {
        if (StringUtils.isEmpty(batchNo)){
            return CouponCommonResult.success(null);
        }
        try {
            SkyLogUtils.infoByMethod(log, "开始调用鲲鹏红包关闭接口：" + JSONObject.toJSONString("request"), "", "");
            String admin = StringUtils.isBlank(adminTokenUtils.getEmployeeNo()) ? ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_ADMIN) : adminTokenUtils.getEmployeeNo();
            String userId = org.apache.commons.lang3.StringUtils.getDigits(admin);

            Map<String, String> parameters = new HashMap<>();
            parameters.put("batchId", batchNo);
            parameters.put("workId", userId);
            parameters.put("userName", adminTokenUtils.getEmployeeName());
            String result = HttpUtil.createPost(ConfigUtils.get(ConfigConst.COUPON_CLOSE_URL))
                    .cookie(new HttpCookie(ACCESS_TOKEN, ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_TOKEN)))
                    .header(TOKEN, ConfigUtils.get(ConfigConst.COUPON_CLOSE_DEFAULT_TOKEN))
                    .body(JSON.toJSONString(parameters))
                    .execute()
                    .body();
            SkyLogUtils.infoByMethod(log, "调用鲲鹏红包关闭接口：" + result, "", "");
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<String>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用鲲鹏鲲鹏红包关闭失败", "", "", e);
            return null;
        }
    }

    /**
     * 转换参数
     *
     * @param couponBatchDetailDto
     * @return
     */
    private Map<String, String> getParameters(CouponBatchDetailDto couponBatchDetailDto) {
        CouponBatchValidDateDto couponBatchValidDate = couponBatchDetailDto.getCouponBatchValidDate();
        Integer countdownMinute = couponBatchValidDate.getCountdownMinute();
        if (ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDate.getValidDateType())) {
            // 鲲鹏红包小时和分钟共用一个字段
            couponBatchValidDate.setCountdownHour(countdownMinute);
        }
        // 鲲鹏是否可退和本地相反
        couponBatchDetailDto.setIsRefund(couponBatchDetailDto.getRefundFlag() != null && couponBatchDetailDto.getRefundFlag() == 0 ? 1 : 0);
        // 创建人
        if (StringUtils.isNotBlank(adminTokenUtils.getEmployeeNo())){
            couponBatchDetailDto.setModifier(adminTokenUtils.getEmployeeName() + adminTokenUtils.getEmployeeNo());
        }else {
            couponBatchDetailDto.setModifier(ConfigUtils.get(ConfigConst.COUPON_CREATE_ACTIVITY_ADMIN));
        }
        couponBatchDetailDto.setModifiedTime(LocalDateTime.now());
        Map<String, Object> couponBatchDetailMap = BeanUtil.beanToMap(couponBatchDetailDto);
        Map<String, Object> validDateMap = BeanUtil.beanToMap(couponBatchValidDate);
        // 字段转换完成后，将分驻字段设置回去
        couponBatchValidDate.setCountdownMinute(countdownMinute);

        for (Map.Entry<String, Object> entry : validDateMap.entrySet()) {
            couponBatchDetailMap.putIfAbsent(entry.getKey(), entry.getValue());
        }

        // 转换渠道
        List<String> channelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(couponBatchDetailDto.getPlatformList())){
            for (PlatformVo platformVo : couponBatchDetailDto.getPlatformList()) {
                if (PlatformEnum.App_IOS_Android.getCode().equals(String.valueOf(platformVo.getCode()))){
                    channelList.add(PlatformEnum.App_Android.getCode());
                    channelList.add(PlatformEnum.App_IOS.getCode());
                    channelList.add(PlatformEnum.App_All.getCode());
                }else if (PlatformEnum.Wx_App.getCode().equals(String.valueOf(platformVo.getCode()))){
                    channelList.add(PlatformEnum.Wx_App.getCode());
                    channelList.add(PlatformEnum.Wx_App_H5.getCode());
                }else {
                    channelList.add(String.valueOf(platformVo.getCode()));
                }
            }
        }

        couponBatchDetailMap.put("channelList", StringUtils.join(",", channelList));
        Map<String, String> parameters = new HashMap<>();
        for (Map.Entry<String, Object> entry : couponBatchDetailMap.entrySet()) {
            // 本地-1代表全部 鲲鹏0代表全部
            if (Objects.equals(entry.getValue(), -1)) {
                entry.setValue(0);
            }
            if (entry.getValue() != null) {
                if (entry.getValue() instanceof LocalDateTime) {
                    parameters.put(entry.getKey(), LocalDateTimeUtil.format((LocalDateTime) entry.getValue(), DateConst.DEFAULT_FULL_FORMAT));
                } else {
                    parameters.put(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
        }


        return parameters;
    }
}
