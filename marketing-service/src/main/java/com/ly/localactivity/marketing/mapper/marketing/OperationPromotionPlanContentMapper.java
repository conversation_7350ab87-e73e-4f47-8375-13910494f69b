package com.ly.localactivity.marketing.mapper.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlanContent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.localactivity.marketing.mapper.MyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运营推广计划内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface OperationPromotionPlanContentMapper extends MyBaseMapper<OperationPromotionPlanContent> {

    /**
     * 按参数选择模板
     *
     * @param templateId 模板id
     * @return {@link List}<{@link OperationPromotionPlanContent}>
     */
    List<OperationPromotionPlanContent> selectByParamTemplate(@Param("templateId") Long templateId);
}
