package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * OperationPositionDetailRo
 *
 * <AUTHOR>
 * @date 2024/2/4
 */
@ApiModel(value = "OperationPositionDetailRo", description = "运营点位详情查询请求参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationPositionDetailRo {
    /**
     * 运营点位id
     */
    @ApiModelProperty(value = "运营点位id", required = true)
    @NotNull(message = "运营点位id不能为空")
    private Long id;
}
