package com.ly.localactivity.marketing.service.marketing;

import com.ly.localactivity.marketing.domain.marketing.OperationPositionPlatform;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.mapper.marketing.OperationPositionPlatformMapper;

import java.util.List;

/**
 * <p>
 * 营销运营位平台 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
public interface IOperationPositionPlatformService extends IService<OperationPositionPlatform> {
    public OperationPositionPlatformMapper getMapper();
    /**
     * 批量插入
     */
    void insertBatch(List<OperationPositionPlatform> operationPositionPlatform);

}
