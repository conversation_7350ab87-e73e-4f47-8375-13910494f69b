package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.ly.localactivity.marketing.domain.base.OperationLogDto;
import com.ly.localactivity.marketing.domain.marketing.SysOperationLog;
import com.ly.localactivity.marketing.mapper.marketing.SysOperationLogMapper;
import com.ly.localactivity.marketing.service.marketing.ISysOperationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
public class SysOperationLogServiceImpl extends ServiceImpl<SysOperationLogMapper, SysOperationLog> implements ISysOperationLogService {

    /**
     * 保存日志
     *
     * @param logDto 日志dto
     */
    @Override
    public void saveLog(OperationLogDto logDto) {
        ThreadUtil.execAsync(new Runnable() {
            @Override
            public void run() {
                SysOperationLog log = new SysOperationLog();
                log.setType(logDto.getType().getCode());
                log.setAction(logDto.getAction().getCode());
                log.setRelationId(logDto.getRelationId());
                log.setContent(logDto.getContent());
                log.setOperatorNo(logDto.getOperatorNo());
                log.setOperatorName(logDto.getOperatorName());
                log.setDeleteFlag(false);
                log.setCreateTime(LocalDateTime.now());
                log.setCreator(logDto.getOperatorNo() + "-" + logDto.getOperatorName());
                log.setModifiedTime(LocalDateTime.now());
                log.setModifier("");
                save(log);
            }
        });

    }
}
