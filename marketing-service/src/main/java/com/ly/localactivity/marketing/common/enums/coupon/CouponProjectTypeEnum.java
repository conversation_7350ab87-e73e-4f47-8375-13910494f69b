package com.ly.localactivity.marketing.common.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * CouponProjectTypeEnum
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Getter
@AllArgsConstructor
public enum CouponProjectTypeEnum {
    /**
     * 一日游，14-景区，271-周边玩乐
     */
    DayTrip(1, "日游类型",271),
    ;

    private Integer code;
    private String name;
    /**
     * 项目id，对应红包的projectId
     */
    private Integer projectId;
}
