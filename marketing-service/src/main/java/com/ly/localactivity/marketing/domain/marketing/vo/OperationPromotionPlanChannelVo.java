package com.ly.localactivity.marketing.domain.marketing.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description OperationPromotionPlanChannelVo
 * @date 2024-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperationPromotionPlanChannelVo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 位置id
     */
    @ApiModelProperty(value = "点位ID")
    private Long positionId;

    /**
     * 运营计划id，operation_plan.id
     */
    @ApiModelProperty(value = "运营计划id")
    private Long planId;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * refid
     */
    @ApiModelProperty(value = "refid")
    private String refid;
}
