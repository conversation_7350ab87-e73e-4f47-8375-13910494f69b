package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 鲲鹏提交状态，1-待提交，2-已提交，3-提交失败
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponDetailCommitStatusEnum {

    WAIT_COMMIT(1, "待提交"),
    COMMIT_SUCCESS(2, "提交成功"),
    COMMIT_ERROR(3, "提交失败"),
    ;
    private final Integer code;
    private final String desc;
}
