package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.enums.CouponRuleCommissionTypeEnum;
import com.ly.localactivity.marketing.common.enums.CouponRuleLimitTypeEnum;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRule;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchRuleDetail;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchRuleDto;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchListMapper;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchRuleMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchOperationService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchRuleDetailService;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包使用规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class CouponBatchRuleServiceImpl extends ServiceImpl<CouponBatchRuleMapper, CouponBatchRule> implements ICouponBatchRuleService {

    @Resource
    private CouponBatchListMapper couponBatchListMapper;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ICouponBatchRuleDetailService couponBatchRuleDetailService;
    @Resource
    private CouponBatchRuleMapper couponBatchRuleMapper;
    @Resource
    private ICouponBatchOperationService couponBatchOperationService;

    /**
     * 保存或更新
     *
     * @param couponBatchList
     * @param couponBatchRuleDto
     * @return
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchList couponBatchList, CouponBatchRuleDto couponBatchRuleDto) {
        if (couponBatchRuleDto == null) {
            return true;
        }
        // 全部 则去除id关联
        CouponRuleLimitTypeEnum limitTypeEnum = EnumUtil.getBy(CouponRuleLimitTypeEnum::getCode, couponBatchRuleDto.getLimitType());
        if (CouponRuleLimitTypeEnum.ALL.equals(limitTypeEnum)) {
            couponBatchRuleDto.setDataIdList(null);
        } else if (CollectionUtil.isEmpty(couponBatchRuleDto.getDataIdList())) {
            if (ObjectUtil.isNotNull(limitTypeEnum)) {
                throw new ApiException(StrUtil.format("请选择{}", limitTypeEnum.getDesc()));
            } else {
                throw new ApiException("请选择限制类型");
            }
        }
        LambdaQueryWrapper<CouponBatchRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CouponBatchRule::getCouponBatchId, couponBatchList.getId());
        CouponBatchRule oldCouponBatchRule = couponBatchRuleMapper.selectOne(lambdaQueryWrapper);

        CouponBatchRule couponBatchRule = BeanUtil.copyProperties(couponBatchRuleDto, CouponBatchRule.class);
        couponBatchRule.setCouponBatchId(couponBatchList.getId());
        couponBatchRule.setModifier(adminTokenUtils.getModifier());
        couponBatchRule.setModifiedTime(LocalDateTime.now());
        if (CouponBatchListTypeEnum.SUPPLIER.getCode().equals(couponBatchList.getType())) {
            couponBatchRule.setCommissionType(CouponRuleCommissionTypeEnum.GE_ZERO.getCode());
        }
        if (oldCouponBatchRule == null) {
            couponBatchRule.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            couponBatchRule.setCreator(adminTokenUtils.getModifier());
            couponBatchRule.setCreateTime(LocalDateTime.now());
            couponBatchRuleMapper.insert(couponBatchRule);
        } else {
            couponBatchRule.setId(oldCouponBatchRule.getId());
            couponBatchRuleMapper.updateById(couponBatchRule);
        }
        // 保存规则明细
        couponBatchRuleDetailService.saveOrUpdate(couponBatchRule, couponBatchRuleDto.getDataIdList());
        // 和渠道联动
        if(CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponBatchRuleDto.getLimitType())){
            String positionCodeListStr = ConfigUtils.get(ConfigConst.COUPON_DETAIL_POSITION_CODE);
            List<String> positionCodeList = null;
            if (StringUtils.isNotBlank(positionCodeListStr)) {
                positionCodeList = JSON.parseArray(positionCodeListStr, String.class);
            }
            couponBatchOperationService.checkPositionCode(couponBatchList.getId(), positionCodeList);
        }

        return true;
    }

    /**
     * 根据红包id查询
     *
     * @param couponBatchIdList
     * @return
     */
    @Override
    public Map<Long, CouponBatchRuleDto> selectByCouponBatchId(List<Long> couponBatchIdList) {
        if (CollectionUtil.isEmpty(couponBatchIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchRule::getCouponBatchId, couponBatchIdList);
        List<CouponBatchRule> couponBatchRuleList = couponBatchRuleMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(couponBatchRuleList)) {
            return new HashMap<>();
        }
        List<CouponBatchRuleDto> couponBatchRuleDtoList = BeanUtil.copyToList(couponBatchRuleList, CouponBatchRuleDto.class);

        List<Long> batchRuleIdList = couponBatchRuleDtoList.stream().map(CouponBatchRuleDto::getId).collect(Collectors.toList());

        Map<Long, List<CouponBatchRuleDetail>> couponBatchRuleIdMap = couponBatchRuleDetailService.selectByCouponBatchRuleId(batchRuleIdList);
        for (CouponBatchRuleDto couponBatchRuleDto : couponBatchRuleDtoList) {
            List<CouponBatchRuleDetail> couponBatchRuleDetailList = couponBatchRuleIdMap.get(couponBatchRuleDto.getId());
            if (CollectionUtil.isNotEmpty(couponBatchRuleDetailList)) {
                couponBatchRuleDto.setDataIdList(couponBatchRuleDetailList.stream().map(CouponBatchRuleDetail::getDataId).collect(Collectors.toList()));
            }
        }
        return couponBatchRuleDtoList.stream().collect(Collectors.toMap(CouponBatchRuleDto::getCouponBatchId, Function.identity(), (a, b) -> a));
    }
}
