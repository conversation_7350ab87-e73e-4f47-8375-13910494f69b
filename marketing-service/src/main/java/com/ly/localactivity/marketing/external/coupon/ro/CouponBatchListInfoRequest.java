package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponBatchListInfoRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponBatchListInfoRo", description = "批量获取券信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CouponBatchListInfoRequest {
    private String requestId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", required = true)
    private List<String> batchNos;

}
