package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponStatusEnum {
    /**
     * 1-未提交
     */
    UN_COMMIT(1, "未提交"),
    /**
     * 2-未生效（待审核）
     */
    INVALID(2, "未生效(待审核)"),
    /**
     * 3-有效
     */
    VALID(3, "有效"),
    /**
     * 4-下架
     */
    TC_OUT(4, "同程下架"),
    B_OUT(5, "供应商下架"),
    EXPIRED (6, "已过期"),
    REJECT(7, "审核驳回"),
    ;
    private final Integer code;
    private final String desc;
}
