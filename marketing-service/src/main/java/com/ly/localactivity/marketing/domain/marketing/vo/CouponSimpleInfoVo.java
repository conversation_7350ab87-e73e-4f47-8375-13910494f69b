package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description CouponSimpleInfoVo
 * @date 2024-04-23
 */
@ApiModel("优惠券简单信息")
@Data
public class CouponSimpleInfoVo {
    @ApiModelProperty("batch_list#id")
    private Long couponBatchId;
    @ApiModelProperty("code")
    private String couponCode;
    @ApiModelProperty("优惠券审核状态")
    private Integer auditStatus;
    @ApiModelProperty("优惠券状态")
    private Integer status;
    @ApiModelProperty("批次号")
    private String batchNo;//批次号
    @ApiModelProperty("批次名称ToC")
    private String batchCname;//批次名称
    @ApiModelProperty("活动有效开始时间")
    private String eventValidBeginDate;//活动有效开始时间
    @ApiModelProperty("活动有效结束时间")
    private String eventValidEndDate;//活动有效结束时间
    private Integer marketingStatus;
    private String invalidReason;
}
