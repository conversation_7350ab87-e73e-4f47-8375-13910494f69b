package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description CouponPositionCodeTypeEnum
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponPositionCodeTypeEnum {
    /**
     * 1-日游详情页营销位
     */
    DAY_TRIP_DETAIL_MARKETING(1, "日游详情页营销位"),
    /**
     * 2-IM咨询
     */
    IM(2, "IM(咨询)商家发放红包"),
    ;
    private final Integer code;
    private final String desc;
}
