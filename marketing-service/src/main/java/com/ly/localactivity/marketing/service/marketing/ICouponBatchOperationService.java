package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchList;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchOperation;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchOperationDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包批次运营配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchOperationService extends IService<CouponBatchOperation> {

    /**
     * 保存或更新
     * @param couponBatchList
     * @param couponBatchOperationDto
     * @return
     */
    Boolean saveOrUpdate(CouponBatchList couponBatchList, List<CouponBatchOperationDto> couponBatchOperationDto);

    /**
     * 根据红包id查询
     * @param couponBatchIdList
     * @return
     */
    Map<Long, List<CouponBatchOperationDto>> selectByCouponBatchId(List<Long> couponBatchIdList);

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    Boolean deleteByIds(List<Long> invalidIdList);

    /**
     * 检查点位code是否可用
     * @param couponBatchId
     * @param validPositionCodeList
     * @return
     */
    Boolean checkPositionCode(Long couponBatchId, List<String> validPositionCodeList);
}
