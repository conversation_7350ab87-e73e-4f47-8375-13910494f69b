package com.ly.localactivity.marketing.application.model.vo;

import com.ly.localactivity.marketing.common.bean.CommonResultDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CouponRechargeResponse
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRechargeResponse {
    @ApiModelProperty(value = "交易号")
    private String tradeNo;
    @ApiModelProperty(value = "领券结果")
    private CommonResultDto rechargeResult;
}
