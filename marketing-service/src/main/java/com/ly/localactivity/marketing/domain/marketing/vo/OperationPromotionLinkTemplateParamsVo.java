package com.ly.localactivity.marketing.domain.marketing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * OperationPromotionLinkParamVo
 *
 * <AUTHOR>
 * @date 2024-2-7
 */
@Data
@ApiModel(value = "OperationPromotionLinkTemplateParamVo",description = "链接模板参数返回参数")
public class OperationPromotionLinkTemplateParamsVo {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "链接模板id")
    private Long id;

    /**
     * 链接模板名称
     */
    @ApiModelProperty(value = "链接模板名称")
    private String name;

    /**
     * 自定义名称
     */
    @ApiModelProperty(value = "自定义名称")
    private String customerName;

    /**
     * 页面，数据字典
     */
    @ApiModelProperty(value = "页面, 数据字典")
    private String pageCode;

    /**
     * 平台code，数据字典
     */
    @ApiModelProperty(value = "平台code, 数据字典")
    private String platform;

    /**
     * 推广链接
     */
    @ApiModelProperty(value = "推广链接")
    private String linkUrl;

    /**
     * 是否自动验证下架，1-是，0-否
     */
    @ApiModelProperty(value = "是否自动验证下架")
    private Integer autoInvalidFlag;

    /**
     * 自动验证下架的参数code
     */
    @ApiModelProperty(value = "自动验证下架的参数code")
    private String[] autoInvalidParamCodes;
    /**
     * 状态，1-有效，0-无效
     */
    @ApiModelProperty(value = "是否有效")
    private Integer status;

    /**
     * 链接 param_code, param_value, value_type
     */
    @ApiModelProperty(value = "链接参数相关信息")
    private List<LinkParamVo> linkParams;

    /**
     * 链接类型，1-原生链接，2-webview链接
     */
    @ApiModelProperty(value = "链接类型")
    private Integer linkUrlType;

    /**
     * 链接前缀
     */
    @ApiModelProperty(value = "链接前缀")
    private String linkUrlPrefix;
}
