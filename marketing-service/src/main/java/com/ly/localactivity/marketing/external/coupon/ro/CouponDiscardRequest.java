package com.ly.localactivity.marketing.external.coupon.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * CouponDiscardRo
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CouponDiscardRo", description = "作废优惠券")
@AllArgsConstructor
@NoArgsConstructor
@Data
@SuperBuilder
public class CouponDiscardRequest extends CouponBaseRequest{
    /**
     * 券号
     */
    @ApiModelProperty(value = "券号",required = true)
    private String couponNo;
    /**
     * 作废说明
     */
    @ApiModelProperty(value = "作废说明",required = true)
    private String instruction;
}
