package com.ly.localactivity.marketing.domain.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description CouponBatchRelationResourceRuleDto
 * @date 2024-09-13
 */
@AllArgsConstructor
@Data
public class CouponBatchRelationResourceRuleDto {
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商--资源一级品类
     */
    private  List<Integer> firstCategoryList;
    /**
     * 资源二级品类
     */
    private  List<String> secondCategoryList;
    /**
     * 出发地
     */
    private List<String> departureList;

    /*
     * 指定资源集合
     */
    private List<String> resourceSerialIdList;
    private Boolean all;

    public CouponBatchRelationResourceRuleDto() {
        this.firstCategoryList = new ArrayList<>();
        this.secondCategoryList = new ArrayList<>();
        this.departureList = new ArrayList<>();
        this.resourceSerialIdList = new ArrayList<>();
        this.all = Boolean.FALSE;
    }
}
