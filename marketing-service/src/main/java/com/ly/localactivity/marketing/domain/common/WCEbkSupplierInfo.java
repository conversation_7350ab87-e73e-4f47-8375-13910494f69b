package com.ly.localactivity.marketing.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 供应商信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("WCEbkSupplierInfo")
public class WCEbkSupplierInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "EICId", type = IdType.AUTO)
    private Long EICId;

    /**
     * 供应商id
     */
    @TableField("EICSupplierId")
    private Long EICSupplierId;

    /**
     * 供应商名称
     */
    @TableField("EICSupplierName")
    private String EICSupplierName;

    /**
     * 昵称
     */
    @TableField("EICNick")
    private String EICNick;

    /**
     * 头像
     */
    @TableField("EICLogo")
    private String EICLogo;

    /**
     * 角色id,逗号隔开
     */
    @TableField("EICRoleIds")
    private String EICRoleIds;

    /**
     * 合同类型 ","分割
     */
    @TableField("EICCompactTypes")
    private String EICCompactTypes;

    /**
     * 类型，0合作系统同步，1三方对接
     */
    @TableField("EICType")
    private Integer EICType;

    /**
     * 商家等级 1优秀 2良好 3一般 4差 5淘汰
     */
    @TableField("EICLevel")
    private Integer EICLevel;

    /**
     * 是否是测试商家，1是0否
     */
    @TableField("EICIsTest")
    private Integer EICIsTest;

    /**
     * 有效性
     */
    @TableField("EICDataFlag")
    private Integer EICDataFlag;

    /**
     * 创建人姓名
     */
    @TableField("EICCreateName")
    private String EICCreateName;

    /**
     * 创建人工号
     */
    @TableField("EICCreateJobNum")
    private String EICCreateJobNum;

    /**
     * 创建时间
     */
    @TableField("EICCreateDate")
    private LocalDateTime EICCreateDate;

    /**
     * 更新人姓名
     */
    @TableField("EICUpdateName")
    private String EICUpdateName;

    /**
     * 更新人工号
     */
    @TableField("EICUpdateJobNum")
    private String EICUpdateJobNum;

    /**
     * 更新时间
     */
    @TableField("EICUpdateDate")
    private LocalDateTime EICUpdateDate;


}
