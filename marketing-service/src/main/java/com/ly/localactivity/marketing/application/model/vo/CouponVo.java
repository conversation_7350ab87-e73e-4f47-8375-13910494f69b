package com.ly.localactivity.marketing.application.model.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsIndexDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoDetailExtDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CouponVo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponVo {
    @ApiModelProperty(value = "红包批次类型，1-平台红包，2-商家红包")
    private Integer batchType;
    @ApiModelProperty(value = "商家id")
    private Long supplierId;
    @ApiModelProperty(value = "使用渠道")
    private List<String> platforms;
    @ApiModelProperty(value = "优惠券code")
    private String couponCode;
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;
    @ApiModelProperty(value = "有效期开始时间")
    private LocalDateTime beginDate;
    @ApiModelProperty(value = "有效期结束时间")
    private LocalDateTime endDate;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    @ApiModelProperty(value = "项目id")
    private Integer projectId;
    @ApiModelProperty(value = "券状态，1-未使用；2-已占用；3-已使用；4-已作废; 5-已过期")
    private Integer status;
    @ApiModelProperty(value = "红包类型，0 立减 1折扣；2次卡；3满返；5满减满返")
    private Integer couponType;
    @ApiModelProperty(value = "券面值立减券：立减金额，折扣：最高折扣金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "门槛")
    private BigDecimal sill;
    @ApiModelProperty(value = "折扣率，70代表7折")
    private Integer discount;
    @ApiModelProperty(value = "最高立减金额")
    private BigDecimal maxDiscountAmount;
    @ApiModelProperty(value = "是否可退")
    private Boolean isRefund;
    @ApiModelProperty(value = "是否付出")
    private Boolean isUserPay;
    @ApiModelProperty(value = "券简述")
    private String brief;
    @ApiModelProperty(value = "券详述")
    private String detail;
    @ApiModelProperty(value = "领取时间")
    private LocalDateTime receiveTime;
    @ApiModelProperty(value = "使用时间")
    private LocalDateTime consumeTime;
    @ApiModelProperty(value = "使用订单号")
    private String consumeOrderNo;
    @ApiModelProperty(value = "附加属性")
    private List<CouponBatchExtendInfo> extendList;
    @ApiModelProperty(value = "使用规则")
    private CouponResourceRuleInfo rule;
    @ApiModelProperty(value = "备注，发放场景")
    private String remark;
    @ApiModelProperty(value = "批次状态，0 新建 1 审批中 2 审批通过 3 已驳回 4 已取消 5 无效")
    private Integer batchStatus;
    @ApiModelProperty(value = "0:普通券 1：商家券 2：私域券")
    private Integer couponsType;

    @ApiModelProperty(value = "点位code")
    private List<String> operationPositionCodes;
    @ApiModelProperty(value = "refid")
    private List<String> refIdList;

    public static CouponVo fromExt(CouponInfoDetailExtDto s, CouponBatchCacheDto cacheDto, boolean simpleMode) {

        CouponVo couponVo = new CouponVo();
        couponVo.setBatchNo(s.getBatchNo());
        couponVo.setStatus(s.getCouponStatus());
        couponVo.setBeginDate(s.getBeginDate());
        couponVo.setEndDate(s.getEndDate());
        couponVo.setAmount(s.getAmount());
        couponVo.setSill(s.getSill());
        couponVo.setCouponsType(s.getCouponsType());

        couponVo.setCouponType(s.getCouponType());
        couponVo.setCouponCode(s.getCouponCode());
        couponVo.setDiscount(s.getDiscount());
        couponVo.setMaxDiscountAmount(s.getMaxDiscountAmount());
        couponVo.setReceiveTime(s.getReceiveTime());
        if (couponVo.getReceiveTime() == null) {
            couponVo.setReceiveTime(LocalDateTime.MIN);
        }
        if (cacheDto != null && cacheDto.getListInfo() != null) {
            couponVo.setBatchType(cacheDto.getListInfo().getType());
            if (cacheDto.getListInfo().getType().equals(CouponBatchListTypeEnum.SUPPLIER.getCode())) {
                couponVo.setSupplierId(cacheDto.getListInfo().getSupplierId());
            }
        }
        if (cacheDto != null && cacheDto.getRule() != null) {
            CouponResourceRuleInfo ruleInfo = new CouponResourceRuleInfo();
            ruleInfo.setLimitType(cacheDto.getRule().getLimitType());
            ruleInfo.setCommissionType(cacheDto.getRule().getCommissionType());
            ruleInfo.setLimitData(cacheDto.getLimitDataIds());
            couponVo.setRule(ruleInfo);
        }

        //附加属性
        if (CollectionUtil.isNotEmpty(s.getExtendList())) {
            couponVo.setExtendList(
                    s.getExtendList().stream().map(m -> {
                        return CouponBatchExtendInfo.builder()
                                .attrId(m.getAttrId())
                                .attrValue(m.getAttrValue())
                                .leftValue(m.getLeftValue())
                                .rightValue(m.getRightValue())
                                .ext1(m.getExt1())
                                .ext2(m.getExt2())
                                .build();
                    }).collect(Collectors.toList())
            );
        }

        if (StringUtils.isNotBlank(s.getUseChannel())) {
            List<String> channels = JSON.parseArray(s.getUseChannel(), String.class);
            couponVo.setPlatforms(channels);
        }

        // 点位code
        if (CollectionUtil.isNotEmpty(cacheDto.getOperationCodes())){
            couponVo.setOperationPositionCodes(cacheDto.getOperationCodes());
        }
        // refId
        if (CollectionUtil.isNotEmpty(cacheDto.getRefIds())){
            couponVo.setRefIdList(cacheDto.getRefIds());
        }

        if (simpleMode) {
            //简化模式只返回固定几个字段
            return couponVo;
        }

        if (StringUtils.isBlank(s.getBatchName()) && cacheDto.getDetailInfo() != null) {
            // batchCname 为对外名称
            couponVo.setBatchName(cacheDto.getDetailInfo().getBatchCname());
        } else {
            couponVo.setBatchName(s.getBatchName());
        }

        couponVo.setCouponName(s.getCouponName());
        //是否可退， 0 可退， 1 不可退
        couponVo.setIsRefund(s.getIsRefund() != null && s.getIsRefund() == 0);
        couponVo.setIsUserPay(s.getIsUserPay() != null && s.getIsUserPay() == 1);
        couponVo.setBrief(s.getBrief());
        couponVo.setDetail(s.getDetail());
        couponVo.setConsumeTime(s.getConsumeTime());
        couponVo.setConsumeOrderNo(s.getConsumeOrderNo());
        couponVo.setRemark(s.getRemark());
        couponVo.setBatchStatus(s.getBatchStatus());

        return couponVo;
    }

    public static CouponVo from(CouponInfoDetailExtDto s, CouponBatchCacheDto couponBatchCacheDto, boolean simpleMode) {
        CouponVo couponVo = new CouponVo();
        couponVo.setBatchNo(s.getBatchNo());
        couponVo.setStatus(s.getCouponStatus());
        couponVo.setBeginDate(s.getBeginDate());
        couponVo.setEndDate(s.getEndDate());
        couponVo.setAmount(s.getAmount());
        couponVo.setSill(s.getSill());
        couponVo.setCouponsType(s.getCouponsType());

        couponVo.setCouponType(s.getCouponType());
        couponVo.setCouponCode(s.getCouponCode());
        couponVo.setDiscount(s.getDiscount());
        couponVo.setMaxDiscountAmount(s.getMaxDiscountAmount());
        couponVo.setReceiveTime(s.getReceiveTime());
        if (couponVo.getReceiveTime() == null) {
            couponVo.setReceiveTime(LocalDateTime.MIN);
        }
        if (couponBatchCacheDto != null && couponBatchCacheDto.getListInfo() != null) {
            couponVo.setBatchType(couponBatchCacheDto.getListInfo().getType());
            if (couponBatchCacheDto.getListInfo().getType().equals(CouponBatchListTypeEnum.SUPPLIER.getCode())) {
                couponVo.setSupplierId(couponBatchCacheDto.getListInfo().getSupplierId());
            }
        }
        if (couponBatchCacheDto != null && couponBatchCacheDto.getRule() != null) {
            CouponResourceRuleInfo ruleInfo = new CouponResourceRuleInfo();
            ruleInfo.setLimitType( couponBatchCacheDto.getRule().getLimitType());
            ruleInfo.setCommissionType(couponBatchCacheDto.getRule().getCommissionType());
            ruleInfo.setLimitData(couponBatchCacheDto.getLimitDataIds());
            couponVo.setRule(ruleInfo);
        }

        //附加属性
        if (CollectionUtil.isNotEmpty(s.getExtendList())) {
            couponVo.setExtendList(
                    s.getExtendList().stream().map(m -> {
                        return CouponBatchExtendInfo.builder()
                                .attrId(m.getAttrId())
                                .attrValue(m.getAttrValue())
                                .leftValue(m.getLeftValue())
                                .rightValue(m.getRightValue())
                                .ext1(m.getExt1())
                                .ext2(m.getExt2())
                                .build();
                    }).collect(Collectors.toList())
            );
        }

        if (StringUtils.isNotBlank(s.getUseChannel())) {
            List<String> channels = JSON.parseArray(s.getUseChannel(), String.class);
            couponVo.setPlatforms(channels);
        }

        if (couponBatchCacheDto != null){
            // 点位code
            if (CollectionUtil.isNotEmpty(couponBatchCacheDto.getOperationCodes())){
                couponVo.setOperationPositionCodes(couponBatchCacheDto.getOperationCodes());
            }
            // refId
            if (CollectionUtil.isNotEmpty(couponBatchCacheDto.getRefIds())){
                couponVo.setRefIdList(couponBatchCacheDto.getRefIds());
            }
        }

        if (simpleMode) {
            //简化模式只返回固定几个字段
            return couponVo;
        }

        if (StringUtils.isBlank(s.getBatchName()) && couponBatchCacheDto != null && couponBatchCacheDto.getDetailInfo() != null && StringUtils.isNotBlank(couponBatchCacheDto.getDetailInfo().getBatchCname())) {
            // batchCname 为对外名称
            couponVo.setBatchName(couponBatchCacheDto.getDetailInfo().getBatchCname());
        } else {
            couponVo.setBatchName(s.getBatchName());
        }

        couponVo.setCouponName(s.getCouponName());
        //是否可退， 0 可退， 1 不可退
        couponVo.setIsRefund(s.getIsRefund() != null && s.getIsRefund() == 0);
        couponVo.setIsUserPay(s.getIsUserPay() != null && s.getIsUserPay() == 1);
        couponVo.setBrief(s.getBrief());
        couponVo.setDetail(s.getDetail());
        couponVo.setConsumeTime(s.getConsumeTime());
        couponVo.setConsumeOrderNo(s.getConsumeOrderNo());
        couponVo.setRemark(s.getRemark());
        couponVo.setBatchStatus(s.getBatchStatus());

        return couponVo;
    }

    public static CouponVo from(CouponInfoDetailExtDto s, CouponEsIndexDto indexDto, boolean simpleMode) {
        CouponVo couponVo = new CouponVo();
        couponVo.setBatchNo(s.getBatchNo());
        couponVo.setStatus(s.getCouponStatus());
        couponVo.setBeginDate(s.getBeginDate());
        couponVo.setEndDate(s.getEndDate());
        couponVo.setAmount(s.getAmount());
        couponVo.setSill(s.getSill());
        couponVo.setCouponsType(s.getCouponsType());

        couponVo.setCouponType(s.getCouponType());
        couponVo.setCouponCode(s.getCouponCode());
        couponVo.setDiscount(s.getDiscount());
        couponVo.setMaxDiscountAmount(s.getMaxDiscountAmount());
        couponVo.setReceiveTime(s.getReceiveTime());
        if (couponVo.getReceiveTime() == null) {
            couponVo.setReceiveTime(LocalDateTime.MIN);
        }
        if (indexDto != null && indexDto.getBatchType() != null) {
            couponVo.setBatchType(indexDto.getBatchType());
            if (indexDto.getBatchType().equals(CouponBatchListTypeEnum.SUPPLIER.getCode())) {
                couponVo.setSupplierId(indexDto.getSupplierId());
            }
        }
        if (indexDto != null) {
            CouponResourceRuleInfo ruleInfo = new CouponResourceRuleInfo();
            ruleInfo.setLimitType(indexDto.getLimitType());
            ruleInfo.setCommissionType(indexDto.getCommissionType());
            ruleInfo.setLimitData(indexDto.getLimitData());
            ruleInfo.setFirstCategoryId(indexDto.getFirstCategoryId());
            couponVo.setRule(ruleInfo);
            // 点位code
            if (CollectionUtil.isNotEmpty(indexDto.getOperationPositionCodes())){
                couponVo.setOperationPositionCodes(indexDto.getOperationPositionCodes());
            }
            // refId
            if (CollectionUtil.isNotEmpty(indexDto.getRefIdList())){
                couponVo.setRefIdList(indexDto.getRefIdList());
            }
        }

        //附加属性
        if (CollectionUtil.isNotEmpty(s.getExtendList())) {
            couponVo.setExtendList(
                    s.getExtendList().stream().map(m -> {
                        return CouponBatchExtendInfo.builder()
                                .attrId(m.getAttrId())
                                .attrValue(m.getAttrValue())
                                .leftValue(m.getLeftValue())
                                .rightValue(m.getRightValue())
                                .ext1(m.getExt1())
                                .ext2(m.getExt2())
                                .build();
                    }).collect(Collectors.toList())
            );
        }

        if (StringUtils.isNotBlank(s.getUseChannel())) {
            List<String> channels = JSON.parseArray(s.getUseChannel(), String.class);
            couponVo.setPlatforms(channels);
        }

        if (simpleMode) {
            //简化模式只返回固定几个字段
            return couponVo;
        }

        if (StringUtils.isBlank(s.getBatchName()) && indexDto != null && StringUtils.isNotBlank(indexDto.getBatchName())) {
            // batchCname 为对外名称
            couponVo.setBatchName(indexDto.getBatchName());
        } else {
            couponVo.setBatchName(s.getBatchName());
        }

        couponVo.setCouponName(s.getCouponName());
        //是否可退， 0 可退， 1 不可退
        couponVo.setIsRefund(s.getIsRefund() != null && s.getIsRefund() == 0);
        couponVo.setIsUserPay(s.getIsUserPay() != null && s.getIsUserPay() == 1);
        couponVo.setBrief(s.getBrief());
        couponVo.setDetail(s.getDetail());
        couponVo.setConsumeTime(s.getConsumeTime());
        couponVo.setConsumeOrderNo(s.getConsumeOrderNo());
        couponVo.setRemark(s.getRemark());
        couponVo.setBatchStatus(s.getBatchStatus());
        if (StringUtils.isNotBlank(s.getUseChannel())) {
            List<String> channels = JSON.parseArray(s.getUseChannel(), String.class);
            couponVo.setPlatforms(channels);
        }

        return couponVo;
    }
}
