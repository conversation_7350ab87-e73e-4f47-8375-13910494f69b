package com.ly.localactivity.marketing.external.coupon.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * CostDeptDto
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@ApiModel(value = "CostDeptDto", description = "成本分摊")
public class CostDeptDto {
    /**
     * 成本分摊部门ID/项目id
     */
    @ApiModelProperty(value = "成本分摊部门ID/项目id")
    private Integer costDept;
    /**
     * 承担比例
     */
    @ApiModelProperty(value = "承担比例")
    private BigDecimal percent;
}
