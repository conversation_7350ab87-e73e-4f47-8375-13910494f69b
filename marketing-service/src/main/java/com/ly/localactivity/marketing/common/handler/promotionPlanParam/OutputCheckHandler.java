package com.ly.localactivity.marketing.common.handler.promotionPlanParam;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.OperationPositionOutputTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionPlanSingleEditRo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @description OutputCheckHandler
 * @date 2024-04-08
 */
@Component
@Slf4j
public class OutputCheckHandler extends AbstractCheckHandler {

    @Resource
    private OutputCheckHandler outputCheckHandler;

    @Autowired
    public void init(DateAndLockedCheckHandler dateAndLockedCheckHandler) {
        //从容器中获取下一个处理器
        nextHandler = dateAndLockedCheckHandler;
    }

    /**
     * 处理器执行方法
     *
     * @param param param
     */
    @Override
    public void handle(OperationPromotionPlanSingleEditRo param, Set<String> accumulationSet) {
        if (!accumulationSet.add("OutputCheckHandler")) {
            return;
        }
        SkyLogUtils.infoByMethod(log, StringUtils.format("OutputCheckHandler[start],param:{}", JSON.toJSONString(param)), "", "");
        Integer outputType = param.getOutputType();
        OperationPositionOutputTypeEnum outputTypeEnum = EnumUtil.getBy(OperationPositionOutputTypeEnum::getCode, outputType);
        //占位不需要校验
        if (param.getPlaceFlag()) {
            super.next("OutputCheckHandler", param, accumulationSet);
            return;
        }
        //兜底不做校验
        //根据输出类型校验
        switch (outputTypeEnum) {
            case SLIDE:
            case ICON:
            case SINGLE_IMAGE:
                if (StringUtils.isBlank(param.getImageUrl()) || StringUtils.isBlank(param.getImageName()) || StringUtils.isBlank(param.getImageInfo())) {
                    throw new ApiException("图片相关信息不能为空");
                }
                if (!param.getDefaultFlag()) {
//                    //图片重复校验
//                    if (operationPromotionPlanMapper.imageCount(param.getId(), param.getPlatform(), param.getPositionId(), param.getImageName()) > 0) {
//                        throw new ApiException("图片重复");
//                    }
                }
                break;
            case ICON_TITLE_SUBTITLE:
                if (StringUtils.isBlank(param.getImageUrl()) || StringUtils.isBlank(param.getImageName()) || StringUtils.isBlank(param.getImageInfo())) {
                    throw new ApiException("图片相关信息不能为空");
                }
            case TITLE_SUBTITLE:
                if (StringUtils.isBlank(param.getSubTitle())) {
                    throw new ApiException("副标题不能为空");
                }
                //字数校验
                if (param.getSubTitle().length() > param.getSubTitleMaxLength()) {
                    throw new ApiException("副标题字数不符合要求");
                }
            case KEYWORD:
                if (StringUtils.isBlank(param.getKeyword())) {
                    throw new ApiException(OperationPositionOutputTypeEnum.KEYWORD.equals(outputTypeEnum) ? "关键词不能为空" : "标题不能为空");
                }
                //字数校验
                if (param.getKeyword().length() > param.getKeyWordMaxLength()) {
                    throw new ApiException(OperationPositionOutputTypeEnum.KEYWORD.equals(outputTypeEnum) ? "关键词不符合要求" : "标题字数不符合要求");
                }
                break;
            case ICON_AND_TITLE:
                if (StringUtils.isBlank(param.getImageUrl()) || StringUtils.isBlank(param.getImageName())) {
                    throw new ApiException("图片相关信息不能为空");
                }
                if (!param.getDefaultFlag()) {
                    //图片重复校验
//                    if (operationPromotionPlanMapper.imageCount(param.getId(), param.getPlatform(), param.getPositionId(), param.getImageName()) > 0) {
//                        throw new ApiException("图片重复");
//                    }
                }
                if (StringUtils.isBlank(param.getKeyword())) {
                    throw new ApiException("标题不能为空");
                }
                //字数校验
                if (param.getKeyword().length() > param.getKeyWordMaxLength()) {
                    throw new ApiException("标题字数不符合要求");
                }
                break;
            default:
                break;
        }
        outputCheckHandler.next("OutputCheckHandler", param, accumulationSet);
    }
}
