package com.ly.localactivity.marketing.domain.marketing.dto;

import com.ly.localactivity.marketing.domain.marketing.*;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 红包批次缓存信息
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponBatchCacheDto {
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 基本信息，鲲鹏接口返回
     */
    private CouponBatchExtDto baseInfo;
    /**
     * 规则，数据表
     */
    private CouponBatchRule rule;
    /**
     * 详细信息，数据表
     */
    private CouponBatchDetail detailInfo;
    /**
     * 列表信息，数据表
     */
    private CouponBatchList listInfo;
    /**
     * 平台
     */
    private List<String> platforms;
    /**
     * refids
     */
    private List<String> refIds;
    /**
     * 运营点位code
     */
    private List<String> operationCodes;
    /**
     * 限制数据id
     */
    private List<String> limitDataIds;
    /**
     * 当日领取限制
     */
    private Boolean dayLimit;
    /**
     * 库存总限制
     */
    private Boolean stockLimit;

    /**
     * 最晚使用有效时间
     */
    private LocalDateTime lastUseTime;
}
