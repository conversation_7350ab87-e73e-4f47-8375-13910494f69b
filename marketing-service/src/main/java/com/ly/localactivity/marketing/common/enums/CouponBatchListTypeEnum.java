package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡劵类型 0-满减，1-折扣
 * @date 2024-04-19
 */
@AllArgsConstructor
@Getter
public enum CouponBatchListTypeEnum {
    /**
     * 0-满减
     */
    PLATFORM(1, "平台红包"),
    /**
     * 1-折扣
     */
    SUPPLIER(2, "商家红包"),

    ;
    private final Integer code;
    private final String desc;

    public static CouponBatchListTypeEnum getByCode(Integer code){
        if (code == null){
            return null;
        }
        for (CouponBatchListTypeEnum value : CouponBatchListTypeEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }

        return null;
    }
}
