package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OperationPositionListRo
 *
 * <AUTHOR>
 * @date 2024/1/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperationPositionListRo", description = "运营位列表请求参数")
@Builder
public class OperationPositionListRo {
    @ApiModelProperty(value = "运营点位id")
    private Long id;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageIndex;
    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小", required = true)
    private Integer pageSize;

    /**
     * 运营位编码
     */
    @ApiModelProperty(value = "运营位编码/名称")
    private String codeOrName;

    /**
     * 所属频道页名称
     */
    @ApiModelProperty(value = "所属频道页名称")
    private String pageName;
    /**
     * 所属频道页ID
     */
    @ApiModelProperty(value = "所属频道页ID")
    private Long positionPageId;
    /**
     * 所属运营平台
     */
    @ApiModelProperty(value = "所属运营平台")
    private List<String> platforms;
    /**
     * 运营位状态
     */
    @ApiModelProperty(value = "运营位状态")
    private Integer status;
    /**
     * 是否必填已维护
     */
    @ApiModelProperty(value = "是否必填已维护")
    private Boolean requiredMaintainedFlag;

    /**
     * 输出形式
     */
    @ApiModelProperty(value = "输出形式")
    private Integer outputType;

    /**
     * 点位使用方
     */
    @ApiModelProperty(value = "点位使用方")
    private Integer positionUser;

}
