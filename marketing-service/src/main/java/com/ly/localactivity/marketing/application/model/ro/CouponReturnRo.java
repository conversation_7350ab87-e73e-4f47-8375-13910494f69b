package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * 红包退还使用
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponReturnRo extends BaseCouponRo {
    @NotBlank(message = "优惠券码不能为空")
    @ApiModelProperty(value = "优惠券码")
    private String couponCode;
    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号")
    private String orderNo;
}
