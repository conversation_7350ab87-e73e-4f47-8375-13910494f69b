package com.ly.localactivity.marketing.common.utils;

import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;

/**
 * ConfigUtils
 *
 * <AUTHOR>
 * @date 2024/1/10
 */
@Slf4j
public class ConfigUtils {
    public static String get(String key) {
        try {
            return ConfigCenterClient.get(key);
        } catch (Exception e) {
            log.error("获取配置异常", e);
        }
        return "";
    }
}
