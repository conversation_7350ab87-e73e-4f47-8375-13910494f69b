package com.ly.localactivity.marketing.application.model.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.marketing.common.enums.CouponBatchListTypeEnum;
import com.ly.localactivity.marketing.common.enums.ValidDateTypeEnum;
import com.ly.localactivity.marketing.common.enums.coupon.CouponBatchApiStatusEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchCacheDto;
import com.ly.localactivity.marketing.external.coupon.dto.ProjectDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CouponBatchNo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponBatchVo {
    @ApiModelProperty(value = "红包批次类型，1-平台红包，2-商家红包")
    private Integer batchType;
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
    @ApiModelProperty(value = "适用平台")
    private List<String> platforms;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "批次名称")
    private String batchName;
    @ApiModelProperty(value = "批次内部名称")
    private String batchInnerName;
    @ApiModelProperty(value = "红包类型，0 立减 1折扣；2次卡；3满返；5满减满返")
    private Integer couponType;
    @ApiModelProperty(value = "券面值立减券：立减金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "门槛")
    private BigDecimal sill;
    @ApiModelProperty(value = "折扣率，70代表7折")
    private Integer discount;
    @ApiModelProperty(value = "最高立减金额")
    private BigDecimal maxDiscountAmount;
    @ApiModelProperty(value = "个人领取限制")
    private Integer personLimit;
    @ApiModelProperty(value = "每日领取限制")
    private Long dayLimit;
    @ApiModelProperty(value = "是否可退")
    private Boolean isRefund;

    @ApiModelProperty(value = "批次有效期开始时间")
    private LocalDateTime beginDate;
    @ApiModelProperty(value = "批次有效期结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "项目ids")
    private List<Integer> projectIds;
    @ApiModelProperty(value = "批次状态，1-可领取，2-不可领取，3-已过期")
    private Integer status;

    @ApiModelProperty(value = "券简述")
    private String brief;
    @ApiModelProperty(value = "券详述")
    private String detail;
    @ApiModelProperty(value = "附加属性")
    private List<CouponBatchExtendInfo> extendList;
    @ApiModelProperty(value = "使用规则列表")
    private CouponResourceRuleInfo rule;
    @ApiModelProperty(value = "总库存")
    private Integer totalStock;
    @ApiModelProperty(value = "剩余库存")
    private Integer remainStock;
    @ApiModelProperty(value = "点位code")
    private List<String> operationPositionCodes;
    @ApiModelProperty(value = "refid")
    private List<String> refIdList;

    @ApiModelProperty(value = "1 固定有效期，2 动态有效期，3.倒计时（按小时）4.倒计时（按分钟）")
    private Integer expType;
    @ApiModelProperty(value = "动态有效期开始天")
    private Integer expBeginDays;
    @ApiModelProperty(value = "动态有效期持续天数")
    private Integer expEndDays;
    @ApiModelProperty(value = "有效开始时间yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expBeginDate;
    @ApiModelProperty(value = "有效截止时间yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expEndDate;
    @ApiModelProperty(value = "红包已领数量")
    private Long receivedCount;

    public static CouponBatchVo fromExt(CouponBatchCacheDto catchDto, boolean simpleMode) {
        CouponBatchVo batchVo = new CouponBatchVo();
        batchVo.setBatchNo(catchDto.getBaseInfo().getBatchNo());
        batchVo.setCouponType(catchDto.getBaseInfo().getCouponType());
        //状态需要转换
        batchVo.setStatus(CouponBatchApiStatusEnum.getByExtStatus(catchDto.getBaseInfo().getBatchStatus()).getCode());
        batchVo.setAmount(catchDto.getBaseInfo().getAmount());
        batchVo.setSill(catchDto.getBaseInfo().getSill());
        batchVo.setDiscount(catchDto.getBaseInfo().getDiscount());
        batchVo.setMaxDiscountAmount(catchDto.getBaseInfo().getMaxDiscountAmount());
        batchVo.setBeginDate(catchDto.getBaseInfo().getBeginDate());
        batchVo.setEndDate(catchDto.getBaseInfo().getEndDate());

        if (catchDto.getListInfo() != null) {
            batchVo.setBatchType(catchDto.getListInfo().getType());
            if (catchDto.getListInfo().getType().equals(CouponBatchListTypeEnum.SUPPLIER.getCode())){
                batchVo.setSupplierId(catchDto.getListInfo().getSupplierId());
            }
        }

        //使用渠道
        if (StringUtils.isNotBlank(catchDto.getBaseInfo().getUseChannel())) {
            List<String> channels = JSON.parseArray(catchDto.getBaseInfo().getUseChannel(), String.class);
            batchVo.setPlatforms(channels);
        }

        // 使用规则
        if (catchDto.getRule() != null){
            CouponResourceRuleInfo ruleInfo = new CouponResourceRuleInfo();
            ruleInfo.setLimitType(catchDto.getRule().getLimitType());
            ruleInfo.setCommissionType(catchDto.getRule().getCommissionType());
            ruleInfo.setLimitData(catchDto.getLimitDataIds());

            if(catchDto.getListInfo() != null){
                ruleInfo.setFirstCategoryId(catchDto.getListInfo().getProductCategoryId());
            }

            batchVo.setRule(ruleInfo);
        }

        //附加属性
        if (CollectionUtil.isNotEmpty(catchDto.getBaseInfo().getExtendList())) {
            batchVo.setExtendList(
                    catchDto.getBaseInfo().getExtendList().stream().map(s -> {
                        return CouponBatchExtendInfo.builder()
                                .attrId(s.getAttrId())
                                .attrValue(s.getAttrValue())
                                .leftValue(s.getLeftValue())
                                .rightValue(s.getRightValue())
                                .ext1(s.getExt1())
                                .ext2(s.getExt2())
                                .build();
                    }).collect(Collectors.toList())
            );
        }

        if (ValidDateTypeEnum.LONG_TERM.getCode().equals(catchDto.getBaseInfo().getExpType())){
            batchVo.setExpBeginDate(catchDto.getBaseInfo().getExpBeginDate());
            batchVo.setExpEndDate(catchDto.getBaseInfo().getExpEndDate());
        }else if (ValidDateTypeEnum.SPECIFIED_PERIOD.getCode().equals(catchDto.getBaseInfo().getExpType())) {
            batchVo.setExpBeginDate(LocalDateTime.now().plusDays(catchDto.getBaseInfo().getExpBeginDays()));
            batchVo.setExpEndDate(LocalDateTimeUtil.endOfDay(batchVo.getExpBeginDate().plusDays(catchDto.getBaseInfo().getExpEndDays())));
        }else if (ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(catchDto.getBaseInfo().getExpType())) {
            batchVo.setExpBeginDate(LocalDateTime.now());
            batchVo.setExpEndDate(LocalDateTime.now().plusHours(catchDto.getBaseInfo().getExpEndHours()));
        }else if (ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(catchDto.getBaseInfo().getExpType())) {
            batchVo.setExpBeginDate(LocalDateTime.now());
            batchVo.setExpEndDate(LocalDateTime.now().plusMinutes(catchDto.getBaseInfo().getExpEndHours()));
        }

        batchVo.setPersonLimit(catchDto.getBaseInfo().getPersonLimit());

        // refId
        if (CollectionUtil.isNotEmpty(catchDto.getRefIds())){
            batchVo.setRefIdList(catchDto.getRefIds());
        }

        // 简易模式
        if (simpleMode){
            return batchVo;
        }

        batchVo.setBatchName(catchDto.getBaseInfo().getBatchName());
        batchVo.setBatchInnerName(catchDto.getBaseInfo().getCode());
        batchVo.setDayLimit(catchDto.getBaseInfo().getDayLimit());
        batchVo.setIsRefund(catchDto.getBaseInfo().getIsRefund() != null && catchDto.getBaseInfo().getIsRefund() == 0);

        batchVo.setTotalStock(catchDto.getBaseInfo().getTotalStock());
        batchVo.setRemainStock(catchDto.getBaseInfo().getRemainStock());
        if (CollectionUtil.isNotEmpty(catchDto.getBaseInfo().getProjectList())) {
            batchVo.setProjectIds(catchDto.getBaseInfo().getProjectList().stream().map(ProjectDto::getProjectId).collect(Collectors.toList()));
        }

        batchVo.setBrief(catchDto.getBaseInfo().getBrief());
        batchVo.setDetail(catchDto.getBaseInfo().getDetail());


        //使用规则
//        if (catchDto.getRule() != null) {
//            CouponResourceRuleInfo ruleInfo = new CouponResourceRuleInfo();
//            ruleInfo.setCommissionType(catchDto.getRule().getCommissionType());
//            ruleInfo.setLimitType(catchDto.getRule().getLimitType());
//            if (CollectionUtil.isNotEmpty(catchDto.getRuleDetails())){
//                ruleInfo.setLimitData(catchDto.getRuleDetails().stream().map(CouponBatchRuleDetail::getDataId).collect(Collectors.toList()));
//            }
//            batchVo.setRule(ruleInfo);
//        }


        // 点位code
        if (CollectionUtil.isNotEmpty(catchDto.getOperationCodes())){
            batchVo.setOperationPositionCodes(catchDto.getOperationCodes());
        }


        return batchVo;
    }
}
