package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.domain.marketing.dto.CouponBatchDetailDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * AuditCouponBatchRo
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
@Data
@ApiModel(value = "AuditCouponBatchRo", description = "红包批次审核请求")
public class AuditCouponBatchRo implements Serializable {
    private static final long serialVersionUID = 4367481750253772309L;

    @NotNull(message = "红包id不能为空")
    private Long couponBatchId;

    @NotNull(message = "审核结果不能为空")
    private Integer auditResult;

    private String auditComment;

    private CouponBatchDetailDto kunPengCouponApply;
}