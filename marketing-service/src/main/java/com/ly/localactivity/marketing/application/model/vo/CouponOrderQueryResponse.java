package com.ly.localactivity.marketing.application.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CouponOrderQueryResponse
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponOrderQueryResponse {
    @ApiModelProperty(value = "优惠券列表")
    public List<CouponVo> couponList;
//    @ApiModelProperty(value = "优惠券总数")
//    private Integer couponCount;
}
