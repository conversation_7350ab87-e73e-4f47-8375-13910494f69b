package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionLinkTemplate;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplatePageRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionLinkTemplateStatusRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplatePageVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplateParamsVo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionLinkTemplateVo;

import java.util.List;

/**
 * <p>
 * 运营推广链接模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
public interface  IOperationPromotionLinkTemplateService extends IService<OperationPromotionLinkTemplate> {
    /**
     * 查询链接模板列表
     * @return {@link List}<{@link OperationPromotionLinkTemplateVo}>
     */
    OperationPromotionLinkTemplatePageVo listOperationPromotionLinkTemplate(OperationPromotionLinkTemplatePageRo request);

    /**
     * 根据 id 查询链接模板详情
     * @param id
     * @return {@link OperationPromotionLinkTemplateParamsVo}
     */
    OperationPromotionLinkTemplateParamsVo getOperationPromotionLinkTemplateById(Long id);

    /**
     * 新增|修改链接模板
     * @param request
     * @return boolean
     */
    Boolean saveOrUpdateOperationPromotionLinkTemplate(OperationPromotionLinkTemplateRo request);


    /**
     * 链接模板设为有效|无效
     * @param request
     * @return boolean
     */
    Boolean updateOperationPromotionLinkTemplateStatus(OperationPromotionLinkTemplateStatusRo request);
}
