package com.ly.localactivity.marketing.domain.marketing;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 红包批次明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupon_batch_detail")
public class CouponBatchDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 红包批次id
     */
    @TableField("coupon_batch_id")
    private Long couponBatchId;

    /**
     * 外部批次编码，鲲鹏批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 红包批次名称
     */
    @TableField("batch_name")
    private String batchName;

    /**
     * 红包批次名称ToC
     */
    @TableField("batch_cname")
    private String batchCname;

    /**
     * 红包使用提醒
     */
    @TableField("notice")
    private String notice;

    /**
     * 红包使用说明
     */
    @TableField("use_desc")
    private String useDesc;

    /**
     * 发放场景说明
     */
    @TableField("scene_desc")
    private String sceneDesc;

    /**
     * 同程app链接
     */
    @TableField("link_url_app")
    private String linkUrlApp;

    /**
     * 同程小程序链接
     */
    @TableField("link_url_wx")
    private String linkUrlWx;

    /**
     * 所属项目
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 卡券类型，0-满减，1-折扣
     */
    @TableField("coupon_type")
    private Integer couponType;

    /**
     * 折扣，70代表7折
     */
    @TableField("discount_rate")
    private Integer discountRate;

    /**
     * 优惠最大金额
     */
    @TableField("discount_max_amount")
    private BigDecimal discountMaxAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 优惠门槛，0表示无门槛
     */
    @TableField("discount_sill")
    private BigDecimal discountSill;

    /**
     * 活动有效期类型，1-固定日期段
     */
    @TableField("event_valid_date_type")
    private Integer eventValidDateType;

    /**
     * 活动有效期开始
     */
    @TableField("event_valid_begin_date")
    private LocalDateTime eventValidBeginDate;

    /**
     * 活动有效期结束
     */
    @TableField("event_valid_end_date")
    private LocalDateTime eventValidEndDate;

    /**
     * 是否可退，0-不可退，1-可退
     */
    @TableField("refund_flag")
    private Integer refundFlag;

    /**
     * 单日领取上限,-1代表不限
     */
    @TableField("receive_day_limit")
    private Integer receiveDayLimit;

    /**
     * 单用户领取上限，-1代表不限
     */
    @TableField("receive_user_limit")
    private Integer receiveUserLimit;

    /**
     * 库存数量，-1代表无限
     */
    @TableField("stock")
    private Integer stock;

    /**
     * 剩余库存
     */
    @TableField("remain_stock")
    private Integer remainStock;

    /**
     * 已使用数量
     */
    @TableField("used_count")
    private Integer usedCount;

    /**
     * 鲲鹏红包状态，1-审批中，2-审批通过，3-已驳回，4-已取消，5-无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 鲲鹏提交时间
     */
    @TableField("commit_time")
    private LocalDateTime commitTime;

    /**
     * 鲲鹏提交描述
     */
    @TableField("commit_msg")
    private String commitMsg;
    /**
     * 鲲鹏提交状态，1-待提交，2-已提交，3-提交失败
     */
    @TableField("commit_status")
    private Integer commitStatus;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 删除标记
     */
    @TableField(value = "delete_flag", fill = FieldFill.INSERT)
//    @TableLogic
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;


}
