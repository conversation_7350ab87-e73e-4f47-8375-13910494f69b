package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.marketing.common.bean.DictDetailInfo;
import com.ly.localactivity.marketing.common.bean.DictInfo;
import com.ly.localactivity.marketing.common.enums.DictTypeEnum;
import com.ly.localactivity.marketing.common.service.DictService;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchPlatform;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchPlatformMapper;
import com.ly.localactivity.marketing.service.marketing.ICouponBatchPlatformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 红包批次适用平台 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
@Slf4j
public class CouponBatchPlatformServiceImpl extends ServiceImpl<CouponBatchPlatformMapper, CouponBatchPlatform> implements ICouponBatchPlatformService {
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private CouponBatchPlatformMapper couponBatchPlatformMapper;
    @Resource
    private ICouponBatchPlatformService couponBatchPlatformService;
    @Resource
    private DictService dictService;

    /**
     * 保存或更新
     * @param couponBatchDetail
     * @param platformVoList
     * @return
     */
    @Override
    public Boolean saveOrUpdate(CouponBatchDetail couponBatchDetail, List<PlatformVo> platformVoList) {

        LambdaQueryWrapper<CouponBatchPlatform> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponBatchPlatform::getCouponBatchDetailId, couponBatchDetail.getId());
        List<CouponBatchPlatform> oldCouponBatchPlatformList = couponBatchPlatformMapper.selectList(queryWrapper);
        Map<String, CouponBatchPlatform> platformValueMap = oldCouponBatchPlatformList.stream().collect(Collectors.toMap(CouponBatchPlatform::getPlatform, Function.identity()));

        List<Long> validIdList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(platformVoList)){
            for (PlatformVo platformVo : platformVoList) {
                CouponBatchPlatform oldCouponBatchPlatform = platformValueMap.get(String.valueOf(platformVo.getCode()));
                if (oldCouponBatchPlatform != null){
                    validIdList.add(oldCouponBatchPlatform.getId());
                    continue;
                }
                CouponBatchPlatform couponBatchPlatform = new CouponBatchPlatform();
                couponBatchPlatform.setCouponBatchId(couponBatchDetail.getCouponBatchId());
                couponBatchPlatform.setCouponBatchDetailId(couponBatchDetail.getId());
                couponBatchPlatform.setPlatform(String.valueOf(platformVo.getCode()));
                couponBatchPlatform.setCreator(adminTokenUtils.getModifier());
                couponBatchPlatform.setCreateTime(LocalDateTime.now());
                couponBatchPlatform.setModifier(adminTokenUtils.getModifier());
                couponBatchPlatform.setModifiedTime(LocalDateTime.now());
                couponBatchPlatform.setDeleteFlag(Boolean.FALSE);
                couponBatchPlatformMapper.insert(couponBatchPlatform);
            }
        }

        List<Long> invalidIdList = oldCouponBatchPlatformList.stream().map(CouponBatchPlatform::getId).filter(id -> !validIdList.contains(id)).collect(Collectors.toList());

        couponBatchPlatformService.deleteByIds(invalidIdList);

        return true;
    }

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    @Override
    public Boolean deleteByIds(List<Long> invalidIdList) {
        if (CollectionUtil.isEmpty(invalidIdList)) {
            return true;
        }
        return couponBatchPlatformMapper.deleteByIds(invalidIdList, adminTokenUtils.getEmployeeName(), adminTokenUtils.getEmployeeNo());
    }

    /**
     * 根据红包批次明细id查询
     * @param couponBatchDetailIdList
     * @return
     */
    @Override
    public Map<Long, List<PlatformVo>> selectByCouponBatchDetailIdList(List<Long> couponBatchDetailIdList) {
        if (CollectionUtil.isEmpty(couponBatchDetailIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CouponBatchPlatform> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CouponBatchPlatform::getCouponBatchDetailId, couponBatchDetailIdList);
        List<CouponBatchPlatform> couponBatchPlatformList = couponBatchPlatformMapper.selectList(lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(couponBatchPlatformList)){
            return new HashMap<>();
        }
        List<PlatformVo> platformVoList = new ArrayList<>(couponBatchPlatformList.size());

        DictInfo dicInfo = dictService.getDicInfo(DictTypeEnum.PLATFORM.getType());
        Map<String, String> dictValueMap = new HashMap<>();
        if (dicInfo != null && CollectionUtils.isNotEmpty(dicInfo.getDetail())){
            dictValueMap = dicInfo.getDetail().stream().collect(Collectors.toMap(DictDetailInfo::getValue, DictDetailInfo::getLabel, (a, b) -> a));
        }
        for (CouponBatchPlatform platformVo : couponBatchPlatformList) {
            PlatformVo build = PlatformVo.builder()
                    .couponBatchDetailId(platformVo.getCouponBatchDetailId())
                    .code(Integer.valueOf(platformVo.getPlatform()))
                    .name(dictValueMap.get(platformVo.getPlatform()))
                    .build();
            platformVoList.add(build);
        }

        return platformVoList.stream().collect(Collectors.groupingBy(PlatformVo::getCouponBatchDetailId));
    }
}
