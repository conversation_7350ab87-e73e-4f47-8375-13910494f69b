package com.ly.localactivity.marketing.application.model.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * CouponUserQueryRo
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@ApiModel(value = "查询对象",description = "couponCodes、batchNos、limitData三个条件三选一，优先级按顺序")
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CouponUserQueryRo extends BaseCouponRo{
    @ApiModelProperty(value = "状态0-全部；1-未使用；2-已占用；3-已使用；4-已作废；5_已过期，默认1-未使用")
    private Integer status;
    @ApiModelProperty(value = "红包code，不传返回所有，传了之后无视其它条件，只查这些红包，优先级最高")
    private List<String> couponCodes;
    @ApiModelProperty(value = "批次号列表，不传返回所有，传了之后无视其它条件，只查这些批次号的红包，优先级中等")
    private List<String> batchNos;
    @ApiModelProperty(value = "限制条件，优先级最低，resourceIds:资源id,cityIds:出发城市id,secondCategorys:二级品类，多个逗号分隔，不传返回该平台下所有的已领红包")
    private CouponLimitRo limitData;
    @ApiModelProperty(value = "精简模式")
    private Boolean simple;
    private Integer timeOut;
}
