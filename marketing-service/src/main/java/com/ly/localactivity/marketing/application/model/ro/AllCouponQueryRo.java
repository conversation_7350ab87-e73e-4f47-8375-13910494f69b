package com.ly.localactivity.marketing.application.model.ro;

import com.ly.localactivity.marketing.application.model.BaseSignRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * UserQueryRo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
@ApiModel(value = "查询对象",description = "positionCode、batchNos、limitData三个条件三选一，优先级按顺序")
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class AllCouponQueryRo extends BaseSignRequest {

    @ApiModelProperty(value = "限制条件，resourceIds:资源id,cityIds:出发城市id,secondCategorys:二级品类，多个逗号分隔")
    private CouponLimitRo limitData;
    @ApiModelProperty(value = "批次号列表，传了就只查这些批次号的红包，不传会查询本地所有可使用的红包")
    private List<String> batchNos;
    @ApiModelProperty(value = "运营点位code列表，传了就只查这些红包，不传会查询本地所有可使用的红包")
    private String positionCode;


    @ApiModelProperty(value = "是否是简单查询")
    private Boolean simple;

    @NotNull(message = "平台不能为空")
    @Size(min = 1)
    @ApiModelProperty(value = "平台")
    private List<Integer> platforms;
}
