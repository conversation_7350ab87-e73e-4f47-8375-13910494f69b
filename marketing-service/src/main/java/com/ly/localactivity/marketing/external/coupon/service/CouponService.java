package com.ly.localactivity.marketing.external.coupon.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.adapter.CheckListLog;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.constants.CouponRequestHeaderConst;
import com.ly.localactivity.marketing.common.utils.HttpSender;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchListExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponInfoDetailExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.CouponPackageInfoExtDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.CouponBatchInfoRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponBatchListInfoRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponPackageInfoRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponRecoverRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CouponService
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Service
@Slf4j
public class CouponService {
    @Value("${coupon.kunPeng.couponAccount}")
    private String COUPON_ACCOUNT;
    @Value("${coupon.kunPeng.couponPassword}")
    private String COUPON_PASSWORD;
    @Value("${coupon.kunPeng.couponGetBatchInfoUrl}")
    private String COUPON_GET_BATCH_INFO_URL;
    @Value("${coupon.kunPeng.couponGetBatchListUrl}")
    private String COUPON_GET_BATCH_LIST_URL;
    @Value("${coupon.kunPeng.couponGetPackageInfoUrl}")
    private String COUPON_GET_PACKAGE_INFO_URL;
    @Value("${coupon.kunPeng.couponRecoverUrl}")
    private String COUPON_RECOVER_URL;
    /**
     * 获取通用请求头
     *
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public Map<String, String> getCommonRequestHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put(CouponRequestHeaderConst.TIMESTAMP, String.valueOf(Instant.now().getEpochSecond()));
        headers.put(CouponRequestHeaderConst.ACCOUNT, COUPON_ACCOUNT);
        headers.put(CouponRequestHeaderConst.PASSWORD, COUPON_PASSWORD);
        headers.put(CouponRequestHeaderConst.CONTENT_TYPE, CouponRequestHeaderConst.DEFAULT_CONTENT_TYPE);
        return headers;
    }

    /**
     * 恢复接口(恢复已占用的红包为未使用状态)
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponCommonResult}<{@link Object}>>
     */
    public CouponCommonResult<CouponCommonResult<Object>> couponRecover(@Validated CouponRecoverRequest request) {
        try {
            long start = System.currentTimeMillis();
//            String result = HttpUtil.createPost(COUPON_RECOVER_URL)
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();

            String result = HttpSender.create()
                    .typeCode("couponCoreService_couponRecover")
                    .requestId(request.getRequestId())
                    .url(COUPON_RECOVER_URL)
                    .headers(getCommonRequestHeader())
                    .body(request)
                    .doPost();
            SkyLogUtils.infoByMethod(log, StringUtils.format("调用恢复接口，请求：{}，响应：{}，耗时：{}"
                            , JSONObject.toJSONString(request)
                            , result
                            , System.currentTimeMillis() - start)
                    , request.getCouponNo()
                    , request.getRequestId());
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponCommonResult<Object>>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用恢复接口失败", request.getCouponNo(), request.getRequestId(), e);
            return null;
        }
    }

    /**
     * 获取优惠券批次信息
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponInfoDetailExtDto}>
     */
    @CheckListLog
    public CouponCommonResult<CouponBatchExtDto> getCouponBatchInfo(@Validated CouponBatchInfoRequest request) {

        CouponCommonResult<CouponBatchExtDto> response = HttpSender.create()
                .typeCode("couponCoreService_getCouponBatchInfo")
                .requestId(request.getRequestId())
                .url(COUPON_GET_BATCH_INFO_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<CouponBatchExtDto>>() {
                });
        return response;

//        try {
//            long start = System.currentTimeMillis();
//            String result = HttpUtil.createPost(COUPON_GET_BATCH_INFO_URL)
//                    .addHeaders(getCommonRequestHeader())
//                    .body(JSONObject.toJSONString(request))
//                    .execute()
//                    .body();
//
//            SkyLogUtils.infoByMethod(log, StringUtils.format("调用红包批次信息接口，请求：{}，响应：{}，耗时：{}"
//                            , JSONObject.toJSONString(request)
//                            , result
//                            , System.currentTimeMillis() - start)
//                    , ""
//                    , request.getRequestId());
//            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponBatchExtDto>>() {
//            });
//        } catch (Exception e) {
//            SkyLogUtils.errorByMethod(log, "调用查询批次信息失败", "", request.getRequestId(), e);
//            return null;
//        }
    }

    /**
     * 获取优惠券批次列表信息
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link List}<{@link CouponBatchExtDto}>>
     */
    @CheckListLog
    public CouponCommonResult<CouponBatchListExtDto> getCouponBatchListInfo(@Validated CouponBatchListInfoRequest request) {

        CouponCommonResult<CouponBatchListExtDto> response = HttpSender.create()
                .typeCode("couponCoreService_getCouponBatchListInfo")
                .requestId(request.getRequestId())
                .url(COUPON_GET_BATCH_LIST_URL)
                .headers(getCommonRequestHeader())
                .body(request)
                .doPost(new TypeReference<CouponCommonResult<CouponBatchListExtDto>>() {
                });
        return response;

    }



    @CheckListLog
    public CouponCommonResult<CouponBatchListExtDto> getCouponBatchListInfo_connectPool(@Validated CouponBatchListInfoRequest request) {
        String response = HttpClientUtil.post(COUPON_GET_BATCH_LIST_URL, request.toString(), getCommonRequestHeader(), 100);
        return JSON.parseObject(response, new TypeReference<CouponCommonResult<CouponBatchListExtDto>>() {
        });
    }

    /**
     * 查询礼包信息
     *
     * @param request 请求
     * @return {@link CouponCommonResult}<{@link CouponPackageInfoExtDto}>
     */
    public CouponCommonResult<CouponPackageInfoExtDto> getCouponPackageInfo(@Validated CouponPackageInfoRequest request) {
        try {
            SkyLogUtils.infoByMethod(log, "开始调用查询礼包信息：" + JSONObject.toJSONString(request), "", "");
            String result = HttpUtil.createPost(COUPON_GET_PACKAGE_INFO_URL)
                    .addHeaders(getCommonRequestHeader())
                    .body(JSONObject.toJSONString(request))
                    .execute()
                    .body();
            SkyLogUtils.infoByMethod(log, "调用查询礼包信息结束：" + result, "", "");
            return JSONObject.parseObject(result, new TypeReference<CouponCommonResult<CouponPackageInfoExtDto>>() {
            });
        } catch (Exception e) {
            SkyLogUtils.errorByMethod(log, "调用查询礼包信息失败", "", "", e);
            return null;
        }
    }

}
