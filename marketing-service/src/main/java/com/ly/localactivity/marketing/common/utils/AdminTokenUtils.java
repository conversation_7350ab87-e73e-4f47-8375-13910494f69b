package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.util.URLUtil;
import com.ly.localactivity.marketing.common.constants.AdminConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * AdminTokenUtils
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@Component
public class AdminTokenUtils {
    private static final String MQ_THREAD_PREFIX = "ConsumeMessage";
    @Autowired
    private HttpServletRequest request;

    /**
     * 获取当前操作人工号
     *
     * @return {@link String}
     */
    public String getEmployeeNo() {
        if (Thread.currentThread().getName().startsWith(MQ_THREAD_PREFIX)){
            return null;
        }
        Object employeeNo = request.getHeader(AdminConst.Admin_Employee_No_Header);
        return employeeNo != null ? employeeNo.toString() : "";
    }

    /**
     * 获取当前操作人姓名
     *
     * @return {@link String}
     */
    public String getEmployeeName() {
        if (Thread.currentThread().getName().startsWith(MQ_THREAD_PREFIX)){
            return null;
        }
        Object employeeName = request.getHeader(AdminConst.Admin_Employee_Name_Header);
        return employeeName != null ? URLUtil.decode(employeeName.toString()) : "";
    }

    /**
     * 根据规则生成修改人
     *
     * @return {@link String}
     */
    public String getModifier() {
        if (Thread.currentThread().getName().startsWith(MQ_THREAD_PREFIX)){
            return null;
        }
        return getEmployeeName()+"["+getEmployeeNo()+"]";
    }
}
