package com.ly.localactivity.marketing.common.handler.promotionPlanParam;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.common.enums.PlanDefaultFlagEnum;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.common.enums.PromoteTypeEnum;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPromotionPlan;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPositionListRo;
import com.ly.localactivity.marketing.domain.marketing.ro.OperationPromotionPlanSingleEditRo;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPositionVo;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;
import com.ly.localactivity.marketing.mapper.marketing.OperationPromotionPlanMapper;
import com.ly.localactivity.marketing.service.marketing.IOperationPositionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description OutputCheckHandler
 * @date 2024-04-08
 */
@Component
@Slf4j
public class DefaultPlanCheckHandler extends AbstractCheckHandler {

    @Resource
    private OperationPromotionPlanMapper operationPromotionPlanMapper;
    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private DefaultPlanCheckHandler defaultPlanCheckHandler;

    @PostConstruct
    public void init() {
        //从容器中获取下一个处理器
        nextHandler = null;
    }

    /**
     * 处理器执行方法
     *
     * @param param param
     */
    @Override
    public void handle(OperationPromotionPlanSingleEditRo param, Set<String> accumulationSet) {
        if (!accumulationSet.add("DefaultPlanCheckHandler")) {
            return;
        }

        SkyLogUtils.infoByMethod(log, StringUtils.format("DefaultPlanCheckHandler[start],param:{}", JSON.toJSONString(param)), "", "");
        if (!param.getDefaultFlag()) {
            defaultPlanCheckHandler.next("DefaultPlanCheckHandler", param, accumulationSet);
            return;
        }
        //校验兜底推广计划
        OperationPositionVo operationPositionVo = operationPositionService.selectOperationPositionPage(OperationPositionListRo.builder()
                        .id(param.getPositionId()).pageIndex(1)
                        .pageSize(1)
                        .build())
                .getList().stream().findFirst().orElse(null);

        if (ObjectUtil.isNull(operationPositionVo)) {
            throw new ApiException("运营位不存在");
        }
        Map<Integer, Set<Integer>> exitDefaultInfos = new HashMap<>(operationPositionVo.getPlatforms().size());
        operationPositionVo.getPlatforms().forEach(platform -> {
            exitDefaultInfos.put(platform.getCode(), new HashSet<>());
        });
        //查询运营位下的兜底推广计划
        List<OperationPromotionPlan> exitDefaultPlans = operationPromotionPlanMapper.selectList(new LambdaQueryWrapper<OperationPromotionPlan>()
                .eq(OperationPromotionPlan::getPositionId, param.getPositionId())
                .eq(OperationPromotionPlan::getValidFlag, NumberUtils.INTEGER_ONE)
                .ne(ObjectUtil.isNotNull(param.getId()), OperationPromotionPlan::getId, param.getId())
                .eq(OperationPromotionPlan::getDefaultFlag, PlanDefaultFlagEnum.DEFAULT.getCode())
                .eq(OperationPromotionPlan::getDeleteFlag, DeleteFlagEnum.NOT_DELETE)
        );
        //处理存在的兜底推广计划 <platform,area>
        exitDefaultPlans.forEach(plan -> {
            PlatformEnum platformEnum = EnumUtil.getBy(PlatformEnum::getCode, plan.getPlatform());
            if (ObjectUtil.equal(platformEnum, PlatformEnum.ALL_PLATFORM)) {
                exitDefaultInfos.forEach((platform, areas) -> {
                    PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, plan.getPromoteType());
                    if (ObjectUtil.equal(promoteTypeEnum, PromoteTypeEnum.DEFAULT)) {
                        areas.add(PromoteTypeEnum.DOMESTIC.getCode());
                        areas.add(PromoteTypeEnum.OVERSEAS.getCode());
                        //境外 不参与
                    } else if (!PromoteTypeEnum.FOREIGN.getCode().equals(promoteTypeEnum.getCode())) {
                        areas.add(promoteTypeEnum.getCode());
                    }
                });
            } else {
                Set<Integer> areas = exitDefaultInfos.get(Integer.valueOf(plan.getPlatform()));
                PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, plan.getPromoteType());
                if (ObjectUtil.equal(promoteTypeEnum, PromoteTypeEnum.DEFAULT)) {
                    areas.add(PromoteTypeEnum.DOMESTIC.getCode());
                    areas.add(PromoteTypeEnum.OVERSEAS.getCode());
                } else if (!PromoteTypeEnum.FOREIGN.getCode().equals(promoteTypeEnum.getCode())) {
                    areas.add(promoteTypeEnum.getCode());
                }
            }

        });

        PlatformEnum platformEnum = EnumUtil.getBy(PlatformEnum::getCode, param.getPlatform());
        PromoteTypeEnum promoteTypeEnum = EnumUtil.getBy(PromoteTypeEnum::getCode, param.getPromoteType());
        if (ObjectUtil.equal(platformEnum, PlatformEnum.ALL_PLATFORM)) {
            List<PlatformVo> platforms = operationPositionVo.getPlatforms();
            for (PlatformVo platform : platforms) {
                Set<Integer> areas = exitDefaultInfos.get(platform.getCode());
                if (ObjectUtil.equal(promoteTypeEnum, PromoteTypeEnum.DEFAULT)) {
                    if (areas.contains(PromoteTypeEnum.DOMESTIC.getCode()) && areas.contains(PromoteTypeEnum.OVERSEAS.getCode())) {
                        throw new ApiException(StrUtil.format("该运营位{}平台已覆盖全部区域", platformEnum.getName()));
                    } else if (areas.contains(PromoteTypeEnum.DOMESTIC.getCode())) {
                        throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), PromoteTypeEnum.DOMESTIC.getName()));
                    } else if (areas.contains(PromoteTypeEnum.OVERSEAS.getCode())) {
                        throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), PromoteTypeEnum.OVERSEAS.getName()));
                    }
                } else {
                    if (!areas.add(promoteTypeEnum.getCode())) {
                        throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), promoteTypeEnum.getName()));
                    }
                }
            }
        } else {
            Set<Integer> areas = exitDefaultInfos.get(Integer.valueOf(param.getPlatform()));
            if (ObjectUtil.equal(promoteTypeEnum, PromoteTypeEnum.DEFAULT)) {
                if (areas.contains(PromoteTypeEnum.DOMESTIC.getCode()) && areas.contains(PromoteTypeEnum.OVERSEAS.getCode())) {
                    throw new ApiException(StrUtil.format("该运营位{}平台已覆盖全部区域", platformEnum.getName()));
                } else if (areas.contains(PromoteTypeEnum.DOMESTIC.getCode())) {
                    throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), PromoteTypeEnum.DOMESTIC.getName()));
                } else if (areas.contains(PromoteTypeEnum.OVERSEAS.getCode())) {
                    throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), PromoteTypeEnum.OVERSEAS.getName()));
                }
            } else {
                if (!areas.add(promoteTypeEnum.getCode())) {
                    throw new ApiException(StrUtil.format("该运营位{}平台已覆盖{}区域", platformEnum.getName(), promoteTypeEnum.getName()));
                }
            }
        }

        defaultPlanCheckHandler.next("DefaultPlanCheckHandler", param, accumulationSet);
    }


}


