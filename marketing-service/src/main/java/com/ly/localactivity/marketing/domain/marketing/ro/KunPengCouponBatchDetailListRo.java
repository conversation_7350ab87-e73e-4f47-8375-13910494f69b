package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * KunPengCouponBatchDetailRo
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Data
public class KunPengCouponBatchDetailListRo implements Serializable {
    private static final long serialVersionUID = 4568465393511791456L;

    @ApiModelProperty(value = "批次号")
    @NotNull(message = "批次号不能为空")
    private List<String> batchNoList;
}