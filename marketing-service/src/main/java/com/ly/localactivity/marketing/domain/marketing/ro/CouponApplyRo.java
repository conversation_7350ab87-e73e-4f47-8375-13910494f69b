package com.ly.localactivity.marketing.domain.marketing.ro;

import com.ly.localactivity.marketing.common.validation.annotation.FutureOrPresentDay;
import com.ly.localactivity.marketing.common.validation.group.BusinessValidation;
import com.ly.localactivity.marketing.common.validation.group.PlatformValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description CouponApplyRo
 * @date 2024-04-19
 */
@ApiModel(value = "CouponApplyRo", description = "优惠券申请RO")
@Data
public class CouponApplyRo implements Serializable {

    private static final long serialVersionUID = -3861328203150113177L;
    @ApiModelProperty("coupon_batch_list#ID")
    private Long batchId;
    @ApiModelProperty("红包类型 1-平台 2-商家")
    @Range(min = 1, max = 2, message = "红包类型配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer type;
    @ApiModelProperty(value = "品类ID")
    @NotNull(message = "产品品类不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer productCategoryId;
    @ApiModelProperty(value = "红包名称")
    @NotBlank(message = "红包名称不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private String name;
    @ApiModelProperty("红包状态，1-未提交，2-未生效（待审核），3-已生效，4-已下架")
    @Range(min = 1, max = 4, message = "红包状态值错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer status;
    @ApiModelProperty(value = "使用有效期类型，1-固定日期段，2-动态有效期，3-倒计时(小时)，4-倒计时(分钟)")
    @Range(min = 1, max = 4, message = "红包使用有效器类型配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer validDateType;
    @ApiModelProperty("使用有效期范围")
    private List<LocalDateTime> validDateRange;
    @ApiModelProperty("红包使用开始时间")
    private LocalDateTime validBeginDate;
    @ApiModelProperty("红包使用结束时间")
    private LocalDateTime validEndDate;
    @ApiModelProperty(value = "动态生效天数，x天开始生效")
    private Integer dynamicStartDay;
    @ApiModelProperty(value = "动态有效时长")
    private Integer dynamicDurationDay;
    @ApiModelProperty("倒计时小时")
    private Integer countdownHour;
    @ApiModelProperty("倒计时分钟")
    private Integer countdownMinute;
    @ApiModelProperty("卡劵类型 0-满减，1-折扣")
    @Range(min = 0, max = 1, message = "红包类型配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer couponType;
    @ApiModelProperty("门槛")
    @Range(min = 1, message = "红包门槛配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer discountSill;
    @ApiModelProperty("优惠金额")
    private Integer discountAmount;
    @ApiModelProperty("最高优惠额度")
    private Integer discountMaxAmount;
    @ApiModelProperty("折扣率")
    private Integer discountRate;
    @ApiModelProperty("库存")
    @Range(min = 1, max = 10000, message = "库存配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer stock;
    @ApiModelProperty("单日领取上线")
    @Range(min = 1, max = 10000, message = "单日领取上限配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer receiveDayLimit;
    @ApiModelProperty("单日用户领取上限 -1表示不限")
    @Range(min = -1, max = 10000, message = "单日用户领取上限配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer receiveUserLimit;
    @ApiModelProperty("是否可退")
    @Range(min = 0, max = 1, message = "是否可退配置错误", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer refundFlag;
    @ApiModelProperty("活动有效期范围")
    private List<LocalDateTime>  eventValidDateRange;//活动有效期
    @ApiModelProperty("活动有效期类型 1-固定时间段")
    @NotNull(message = "活动有效期类型不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private Integer eventValidDateType;
    @ApiModelProperty("活动开始时间")
    @FutureOrPresentDay(message = "活动开始时间不能小于现在", groups = {BusinessValidation.class, PlatformValidation.class})
    private LocalDateTime eventValidBeginDate;
    @ApiModelProperty("活动结束时间")
    @FutureOrPresentDay(message = "活动结束时间不能小于现在", groups = {BusinessValidation.class, PlatformValidation.class})
    private LocalDateTime eventValidEndDate;
    @ApiModelProperty("投放点位类型(module) 12-红包运营 13、IM咨询")
    @NotEmpty(message = "投放点位不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private List<Integer> operationPositionModules;
    @ApiModelProperty("投放平台")
    @NotEmpty(message = "投放平台不能为空", groups = {BusinessValidation.class, PlatformValidation.class})
    private List<String> platform;

}
