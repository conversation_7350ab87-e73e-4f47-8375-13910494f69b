package com.ly.localactivity.marketing.domain.marketing.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
/**
 * OperationPositionPageRo
 *
 * <AUTHOR>
 * @date 2024-1-31
 */
@Data
@ApiModel(value = "OperationPositionPageRo",description = "频道页编辑/新增请求参数")
public class OperationPositionPageRo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;



    /**
     * 频道页面名称
     */
    @ApiModelProperty(value = "频道页面名称")
    @NotNull(message = "频道页版本不能为空")
    @NotBlank(message = "频道页版本不能为空")
    private String name;

    /**
     * 频道页Code
     */
    @ApiModelProperty(value = "频道页Code")
    @NotBlank(message = "频道页Code不能为空")
    private String code;

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    @NotNull(message = "所属项目不能为空")
    private Integer project;

    /**
     * 频道页版本
     */
    @ApiModelProperty(value = "频道页版本")
    @NotNull(message = "频道页版本不能为空")
    @NotBlank(message = "频道页版本不能为空")
    private String version;

    /**
     * 频道页排序值
     */
    @ApiModelProperty(value = "频道页排序值")
    @NotNull(message = "频道页排序值不能为空")
    private Integer sortNo;

    /**
     * 频道页是否有效（1正常 0停用）
     */
    @ApiModelProperty(value = "频道页状态")
    private Integer status;
}
