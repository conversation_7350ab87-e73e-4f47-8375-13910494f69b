package com.ly.localactivity.marketing.service.marketing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchDetail;
import com.ly.localactivity.marketing.domain.marketing.CouponBatchPlatform;
import com.ly.localactivity.marketing.domain.marketing.vo.PlatformVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 红包批次适用平台 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface ICouponBatchPlatformService extends IService<CouponBatchPlatform> {

    /**
     * 保存或更新
     * @param couponBatchDetail
     * @param platformVoList
     * @return
     */
    Boolean saveOrUpdate(CouponBatchDetail couponBatchDetail, List<PlatformVo> platformVoList);

    /**
     * 根据主键删除
     * @param invalidIdList
     * @return
     */
    Boolean deleteByIds(List<Long> invalidIdList);

    /**
     * 根据红包批次明细id查询
     * @param couponBatchDetailIdList
     * @return
     */
    Map<Long, List<PlatformVo>> selectByCouponBatchDetailIdList(List<Long> couponBatchDetailIdList);
}
