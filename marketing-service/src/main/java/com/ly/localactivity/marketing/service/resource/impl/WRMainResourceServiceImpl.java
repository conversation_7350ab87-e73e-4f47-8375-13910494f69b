package com.ly.localactivity.marketing.service.resource.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.localactivity.framework.enums.DataFlagEnum;
import com.ly.localactivity.framework.enums.DeleteFlagEnum;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.resource.WRMainResource;
import com.ly.localactivity.marketing.mapper.resource.WRMainResourceMapper;
import com.ly.localactivity.marketing.service.resource.IWRMainResourceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 玩乐主资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Service
public class WRMainResourceServiceImpl extends ServiceImpl<WRMainResourceMapper, WRMainResource> implements IWRMainResourceService {
    @Resource
    private WRMainResourceMapper wrMainResourceMapper;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public List<WRMainResource> getCanSaleProductByMainResourceSerialIds(List<String> mainResourceSerialIdList) {
        if (CollectionUtil.isEmpty(mainResourceSerialIdList)) {
            return new ArrayList<>();
        }
        return wrMainResourceMapper.getCanSaleProductByMainResourceSerialIds(mainResourceSerialIdList);
    }

    /**
     * 通过供应商id获取可以销售产品
     *
     * @param supplierId        供应商id
     * @param firstCategoryId
     * @return {@link List}<{@link WRMainResource}>
     */
    @Override
    public List<WRMainResource> getCanSaleProductBySupplierId(Long supplierId, Integer firstCategoryId) {
        if (supplierId == null) {
            return Collections.emptyList();
        }
        return wrMainResourceMapper.getCanSaleProductBySupplierId(supplierId, firstCategoryId);
    }

    /**
     *  通过主资源id获取供应商id 缓存
     * @param mainResourceSerialIdList
     * @return
     */
    @Override
    public Map<String, Long> getMainResourceSupplierId(List<String> mainResourceSerialIdList) {
        if (CollectionUtil.isEmpty(mainResourceSerialIdList)){
            return Collections.emptyMap();
        }
        Map<String, Long> mainResourceSupplierIdMap = new ConcurrentHashMap<>();
        mainResourceSerialIdList.forEach(serialId -> {
            String supplierIdStr = redisUtils.get(getMainResourceSerialIdKey(serialId));
            if (StringUtils.isNotBlank(supplierIdStr)){
                long supplierId = Long.parseLong(supplierIdStr);
                // 0是防止无数据重复调用
                if (supplierId != 0L){
                    mainResourceSupplierIdMap.put(serialId, supplierId);
                }
            }else {
                LambdaQueryWrapper<WRMainResource> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WRMainResource::getMRSerialId, serialId);
                queryWrapper.eq(WRMainResource::getMRDataFlag, DataFlagEnum.Valid.getCode());
                queryWrapper.eq(WRMainResource::getMRDelete, DeleteFlagEnum.UnDeleted.getCode());
                WRMainResource mainResource = wrMainResourceMapper.selectOne(queryWrapper);
                if (mainResource != null){
                    redisUtils.setex(serialId, String.valueOf(mainResource.getMRSupplierId()), 7 * 60 * 60 * 24L);
                    mainResourceSupplierIdMap.put(serialId, mainResource.getMRSupplierId());
                }else {
                    // 2分钟防止重复调用
                    redisUtils.setnx(serialId, String.valueOf(0), 2 * 60L);
                }
            }
        });

        return mainResourceSupplierIdMap;
    }

    private String getMainResourceSerialIdKey(String mainResourceSerialId) {
        return "mainResource:serialId:" + mainResourceSerialId;
    }

}
