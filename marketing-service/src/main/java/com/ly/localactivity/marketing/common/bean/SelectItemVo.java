package com.ly.localactivity.marketing.common.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 下拉框选项
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectItemVo {
    @ApiModelProperty("标签")
    private String label;
    @ApiModelProperty("值")
    private String value;
}
