package com.ly.localactivity.marketing.domain.marketing.ro;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import com.ly.localactivity.marketing.domain.marketing.dto.OperationPromotionPlanLinkDto;
import com.ly.localactivity.marketing.domain.marketing.vo.OperationPromotionPlanChannelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OperationPromotionPlanSingleEditRo
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OperationPromotionPlanSingleEditRo", description = "运营推广方案单个编辑Ro")
@EqualsAndHashCode(callSuper = false)
public class OperationPromotionPlanSingleEditRo {
    @ApiModelProperty(value = "运营点位id", required = true)
    @NotNull(message = "运营点位id不能为空")
    private Long positionId;
    @ApiModelProperty(value = "运营点位Code", required = true)
    @NotNull(message = "运营点位Code不能为空")
    private String positionCode;
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String platform;
    @ApiModelProperty(value = "推广方式", required = true)
    @NotNull(message = "推广方式不能为空")
    private Integer promoteType;
    @ApiModelProperty(value = "输出形式", required = true)
    @NotNull(message = "输出形式不能为空")
    private Integer outputType;
    @ApiModelProperty(value = "推广地区ids(按照推广地区分开维护则为一条数据)", required = true)
    @NotEmpty(message = "推广地区ids不能为空")
    private List<Long> regionIds;
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "推广计划名称", required = true)
    @NotBlank(message = "推广计划名称不能为空")
    private String name;
    @ApiModelProperty(value = "自定义名称")
    private String alias;
    @ApiModelProperty(value = "是否占位")
    private Boolean placeFlag;
    @ApiModelProperty(value = "有效期类型，1-长期有效，2-固定日期段", required = true)
    @NotNull(message = "有效期类型不能为空")
    @Range(min = 1, max = 2, message = "有效期类型只能为1或2")
    private Integer validDateType;
    @ApiModelProperty(value = "有效开始日期")
    private LocalDateTime validBeginDate;
    @ApiModelProperty(value = "有效结束日期")
    private LocalDateTime validEndDate;
    @ApiModelProperty(value = "顺位", required = true)
    @NotNull(message = "顺位不能为空")
    @Min(value = 1, message = "顺位必须大于0")
    private Integer indexValue;
    @ApiModelProperty(value = "是否置顶/锁定", required = true)
    @NotNull(message = "是否置顶/锁定不能为空")
    private Boolean topFlag;
    @ApiModelProperty(value = "是否兜底", required = true)
    @NotNull(message = "是否兜底不能为空")
    private Boolean defaultFlag;
    @ApiModelProperty(value = "默认展示展示方式，1-最后顺位，2-兜底展示", required = true)
    @NotNull(message = "默认展示展示方式不能为空")
    @Range(min = 1, max = 2, message = "默认展示展示方式只能为1或2")
    private Integer defaultShowType;
    @ApiModelProperty(value = "推广原因 数据字典:promotion_reason")
    private String reasonType;
    @ApiModelProperty(value = "推广原因备注")
    private String reasonRemark;
    @ApiModelProperty(value = "有效标记", required = true)
    @NotNull(message = "有效标记不能为空")
    private Boolean validFlag;
    @ApiModelProperty(value = "图片地址")
    private String imageUrl;
    @ApiModelProperty(value = "图片名称")
    private String imageName;
    @ApiModelProperty(value = "图片上传信息")
    private String imageInfo;
    @ApiModelProperty(value = "关键字(推广标题)")
    private String keyword;


    @ApiModelProperty(value = "链接")
    private List<OperationPromotionPlanLinkDto> links;

    @ApiModelProperty(value = "是否指定渠道")
    private Boolean designatedChannels;
    @ApiModelProperty(value = "指定渠道")
    private List<OperationPromotionPlanChannelVo> channels;

    @ApiModelProperty(value = "自定义排序")
    private Integer customSort;

    @ApiModelProperty(hidden = true)
    @JSONField(serialize = false)
    private String code;
    @ApiModelProperty(value = "是否分开维护", hidden = true)
    @JSONField(serialize = false)
    private Boolean defaultIsSeparate;

    @ApiModelProperty(value = "副标题")
    private String subTitle;
    /**
     * 运营点位信息 参数校验时候传递
     */
    @ApiModelProperty(hidden = true)
    @JSONField(serialize = false)
    private Integer keyWordMaxLength;
    @ApiModelProperty(hidden = true)
    @JSONField(serialize = false)
    private Integer subTitleMaxLength;
}
