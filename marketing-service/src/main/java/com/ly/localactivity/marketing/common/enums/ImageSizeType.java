package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ImageSizeType
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Getter
@AllArgsConstructor
public enum ImageSizeType {
    Unknown(0, "未知"),
    /**
     * 横图
     */
    Big_Horizontal(100, "横图"),
    Middle_Horizontal(101, "中横图"),
    Small_Horizontal(102, "小横图"),
    /**
     * 竖图
     */
    Big_Vertical(200, "竖图"),
    Middle_Vertical(201, "中竖图"),
    Small_Vertical(202, "小竖图"),
    /**
     * 正方
     */
    Square(300, "正方"),

    ;
    private Integer code;
    private String name;
}
