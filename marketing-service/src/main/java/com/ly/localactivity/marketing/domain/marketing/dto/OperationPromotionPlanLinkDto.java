package com.ly.localactivity.marketing.domain.marketing.dto;

import com.ly.localactivity.marketing.domain.marketing.ro.LinkParamRo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description OperationPromotionPlanLinkDto
 * @date 2024-06-21
 */
@Data
public class OperationPromotionPlanLinkDto {


    @ApiModelProperty(value = "推广计划id")
    private String platform;
    @ApiModelProperty(value = "推广链接")
    private String linkUrl;
    @ApiModelProperty(value = "推广链接方式")
    private Integer linkType;
    @ApiModelProperty(value = "推广链接模版id")
    private Long linkTemplateId;
    @ApiModelProperty(value = "推广链接参数")
    private List<LinkParamRo> linkParams;
    @ApiModelProperty(value = "是否自动验证下架")
    private Boolean autoVerifyFlag;
    @ApiModelProperty(value = "自动验证下架参数code")
    private List<String> autoVerifyCodes;
    @ApiModelProperty(value = "推广链接前缀")
    private String linkUrlPrefix;
    @ApiModelProperty(value = "链接类型，1-原生链接，2-webview链接")
    private Integer linkUrlType;
    @ApiModelProperty(value = "是否需要验证,默认false,true-需要验证")
    private Boolean needVerify;
}
