package com.ly.localactivity.marketing.service.marketing.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.enums.DeleteFlagEnum;
import com.ly.localactivity.marketing.domain.base.SysAppDto;
import com.ly.localactivity.marketing.domain.marketing.SysAppList;
import com.ly.localactivity.marketing.mapper.marketing.SysAppListMapper;
import com.ly.localactivity.marketing.service.marketing.ISysAppListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * app信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29
 */
@Service
public class SysAppListServiceImpl extends ServiceImpl<SysAppListMapper, SysAppList> implements ISysAppListService {

    /**
     * 通过appid获取
     *
     * @param appId 应用程序id
     * @return {@link SysAppDto}
     */
    @Override
    public SysAppDto getByAppId(String appId) {
        SysAppList sysAppList = this.getOne(new LambdaQueryWrapper<SysAppList>().eq(SysAppList::getAppId, appId)
                .eq(SysAppList::getDeleteFlag, DeleteFlagEnum.UnDeleted.getCode()));
        if (sysAppList == null) {
            return null;
        }
        return SysAppDto.builder()
                .id(sysAppList.getId())
                .appId(sysAppList.getAppId())
                .appSecret(sysAppList.getAppSecret())
                .name(sysAppList.getName())
                .build();

    }
}
