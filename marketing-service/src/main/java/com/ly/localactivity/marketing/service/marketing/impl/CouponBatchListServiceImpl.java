package com.ly.localactivity.marketing.service.marketing.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ly.localactivity.framework.annotation.MultipleTransaction;
import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.common.TransactionConstant;
import com.ly.localactivity.framework.enums.DataFlagEnum;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.exception.ApiException;
import com.ly.localactivity.framework.model.api.CommonPage;
import com.ly.localactivity.framework.mq.TurboMqUtils;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.model.vo.CouponExportVo;
import com.ly.localactivity.marketing.common.constants.BatchNoErrorMessageConst;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.enums.*;
import com.ly.localactivity.marketing.common.enums.coupon.CouponProjectTypeEnum;
import com.ly.localactivity.marketing.common.utils.AdminTokenUtils;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.common.utils.TemplateUtils;
import com.ly.localactivity.marketing.domain.common.WCEbkSupplierInfo;
import com.ly.localactivity.marketing.domain.marketing.*;
import com.ly.localactivity.marketing.domain.marketing.dto.*;
import com.ly.localactivity.marketing.domain.marketing.ro.*;
import com.ly.localactivity.marketing.domain.marketing.vo.*;
import com.ly.localactivity.marketing.domain.resource.WRMainResource;
import com.ly.localactivity.marketing.domain.resource.vo.ProductOptionVo;
import com.ly.localactivity.marketing.external.coupon.dto.CouponBatchExtDto;
import com.ly.localactivity.marketing.external.coupon.dto.ProjectDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.service.CouponAdminService;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchDetailMapper;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchListMapper;
import com.ly.localactivity.marketing.mapper.marketing.CouponBatchRuleMapper;
import com.ly.localactivity.marketing.service.common.IWCEbkSupplierInfoService;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.marketing.*;
import com.ly.localactivity.marketing.service.resource.IWRMainResourceService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 优惠券列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
@Slf4j
public class CouponBatchListServiceImpl extends ServiceImpl<CouponBatchListMapper, CouponBatchList> implements ICouponBatchListService {
    @Resource
    private CouponBatchListMapper couponBatchListMapper;
    @Resource
    private ICouponBatchListService couponBatchListService;
    @Resource
    private AdminTokenUtils adminTokenUtils;
    @Resource
    private ICouponBatchOperationService couponBatchOperationService;
    @Resource
    private IOperationPositionService operationPositionService;
    @Resource
    private ICouponBatchDetailService couponBatchDetailService;
    @Resource
    private ICouponBatchPlatformService couponBatchPlatformService;
    @Resource
    private ICouponBatchRuleService couponBatchRuleService;
    @Resource
    private ICouponBatchValidDateService couponBatchValidDateService;
    @Resource
    private BizLogUtils bizLogUtils;
    @Resource
    private ICouponBatchOperationChannelService couponBatchOperationChannelService;
    @Resource
    private IWCEbkSupplierInfoService ebkSupplierInfoService;
    @Resource
    private IWRMainResourceService mainResourceService;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private CouponAdminService couponAdminService;
    @Resource
    private CouponBatchDetailMapper couponBatchDetailMapper;
    @Resource
    private CouponBatchRuleMapper couponBatchRuleMapper;
    @Resource
    private TurboMqUtils turboMqUtils;

    /**
     * 分页查询红包列表
     *
     * @param request 请求
     * @return {@link CommonPage<CouponBatchListVo>}
     */
    @Override
    public CommonPage<CouponBatchListVo> pageQueryCouponBatchList(QueryCouponBatchListRo request) {
        // 处理供应商id
        if (StringUtils.isNotBlank(request.getSupplierQuery()) && StringUtils.isNumeric(request.getSupplierQuery())) {
            request.setSupplierIdList(Collections.singletonList(Long.valueOf(request.getSupplierQuery())));
        } else if (StringUtils.isNotBlank(request.getSupplierQuery())) {
            LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
            ebkSupplierInfoQueryWrapper.like(WCEbkSupplierInfo::getEICSupplierName, request.getSupplierQuery());
            List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
            if (CollectionUtil.isEmpty(ebkSupplierInfoList)) {
                return CommonPage.emptyPage(request.getPageIndex(), request.getPageSize());
            }
            request.setSupplierIdList(ebkSupplierInfoList.stream().map(WCEbkSupplierInfo::getEICSupplierId).collect(Collectors.toList()));
        }
        // 分页查询
        Page<CouponBatchListVo> page = Page.of(request.getPageIndex(), request.getPageSize());
        IPage<CouponBatchListVo> couponBatchListListPage = couponBatchListMapper.pageSelectCouponList(page, request);
        CommonPage<CouponBatchListVo> couponBatchListVoCommonPage = CommonPage.restPage(couponBatchListListPage);
        List<CouponBatchListVo> couponBatchListVoList = couponBatchListVoCommonPage.getList();
        if (CollectionUtil.isEmpty(couponBatchListVoList)) {
            return couponBatchListVoCommonPage;
        }

        List<Long> batchListIdList = couponBatchListVoList.stream().map(CouponBatchListVo::getId).collect(Collectors.toList());
        // 批次详情 用于计算状态
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdDetailMap = couponBatchDetailService.selectByCouponBatchId(batchListIdList);
        Map<Long, List<CouponBatchOperationDto>> couponBatchIdOperationMap = new HashMap<>();
        Map<Long, String> supplierIdNameMap = new HashMap<>();
        Map<Long, CouponBatchRuleDto> couponBatchIdRuleMap = new HashMap<>();
        List<WRMainResource> mainResourceList = new ArrayList<>();
        if (CouponBatchListTypeEnum.PLATFORM.getCode().equals(request.getType())) {
            // 点位信息
            couponBatchIdOperationMap = couponBatchOperationService.selectByCouponBatchId(batchListIdList);
        } else if (CouponBatchListTypeEnum.SUPPLIER.getCode().equals(request.getType())) {
            // 规则信息
            couponBatchIdRuleMap = couponBatchRuleService.selectByCouponBatchId(batchListIdList);

            // 关联在售产品
            List<String> mainResourceSerialIdList = couponBatchIdRuleMap.values().stream().
                    filter(rule -> CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(rule.getLimitType())).
                    map(CouponBatchRuleDto::getDataIdList).
                    filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).map(String::valueOf).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            mainResourceList = mainResourceService.getCanSaleProductByMainResourceSerialIds(mainResourceSerialIdList);

            // 供应商名称
            List<Long> supplierIdList = couponBatchListVoList.stream().map(CouponBatchListVo::getSupplierId).collect(Collectors.toList());
            LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
            ebkSupplierInfoQueryWrapper.in(WCEbkSupplierInfo::getEICSupplierId, supplierIdList);
            ebkSupplierInfoQueryWrapper.eq(WCEbkSupplierInfo::getEICDataFlag, DataFlagEnum.Valid.getCode());
            List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
            supplierIdNameMap = ebkSupplierInfoList.stream().collect(Collectors.toMap(WCEbkSupplierInfo::getEICSupplierId, WCEbkSupplierInfo::getEICSupplierName, (a, b) -> a));
        }

        // 计算红包信息
        this.calcCouponBatchInfo(request.getType(), couponBatchListVoList, couponBatchIdDetailMap, couponBatchIdOperationMap, supplierIdNameMap, couponBatchIdRuleMap, mainResourceList);

        return couponBatchListVoCommonPage;
    }

    /**
     * 计算红包信息
     *
     * @param type
     * @param couponBatchListVoList
     * @param couponBatchIdDetailMap
     * @param couponBatchIdOperationMap
     * @param supplierIdNameMap
     * @param couponBatchIdRuleMap
     * @param mainResourceList
     */
    private void calcCouponBatchInfo(Integer type, List<CouponBatchListVo> couponBatchListVoList, Map<Long, List<CouponBatchDetailDto>> couponBatchIdDetailMap, Map<Long, List<CouponBatchOperationDto>> couponBatchIdOperationMap, Map<Long, String> supplierIdNameMap, Map<Long, CouponBatchRuleDto> couponBatchIdRuleMap, List<WRMainResource> mainResourceList) {
        if (CollectionUtil.isEmpty(couponBatchListVoList)) {
            return;
        }
        for (CouponBatchListVo couponBatchListVo : couponBatchListVoList) {
            // 红包批次
            List<CouponBatchDetailDto> couponBatchDetailDtoList = couponBatchIdDetailMap.get(couponBatchListVo.getId());
            if (CollectionUtil.isNotEmpty(couponBatchDetailDtoList)) {
                if (CouponBatchListTypeEnum.PLATFORM.getCode().equals(type)) {
                    couponBatchListVo.setCouponBatchDetailCount(couponBatchDetailDtoList.size());
                } else {
                    couponBatchListVo.setCouponBatchDetailList(couponBatchDetailDtoList);
                }
            }
            // 点位信息
            List<CouponBatchOperationDto> couponBatchOperationDtoList = couponBatchIdOperationMap.get(couponBatchListVo.getId());
            couponBatchListVo.setCouponBatchOperationList(couponBatchOperationDtoList);
            // 供应商名称
            couponBatchListVo.setSupplierName(supplierIdNameMap.get(couponBatchListVo.getSupplierId()));
            // 规则信息
            CouponBatchRuleDto couponBatchRuleDto = couponBatchIdRuleMap.get(couponBatchListVo.getId());
            if (ObjectUtil.isNotNull(couponBatchRuleDto)) {
                couponBatchListVo.setCanSaleProductCount(NumberUtils.INTEGER_ZERO);
                // couponBatchListVo.setCouponBatchRule(couponBatchRuleDto);
                // 限制类型
                couponBatchListVo.setLimitType(couponBatchRuleDto.getLimitType());
                // 计算关联产品数目
                if (CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponBatchRuleDto.getLimitType()) && CollectionUtil.isNotEmpty(couponBatchRuleDto.getDataIdList()) && CollectionUtil.isNotEmpty(mainResourceList)) {
                    couponBatchListVo.setCanSaleProductCount((int) mainResourceList.stream().filter(mainResource ->
                            couponBatchRuleDto.getDataIdList().contains(mainResource.getMRSerialId())).distinct().count());
                }
            }
            // 计算红包营销状态
            couponBatchListVo.setMarketingStatus(this.calcCouponBatchInfoMarketingStatus(couponBatchListVo.getStatus(), couponBatchDetailDtoList));
            // 下架原因
            if (CouponMarketingStatusEnum.DE_LISTED.getCode().equals(couponBatchListVo.getMarketingStatus())) {
                if (CouponStatusEnum.B_OUT.getCode().equals(couponBatchListVo.getStatus())) {
                    couponBatchListVo.setInvalidReason(CouponInvalidReasonEnum.SUPPLIER_INVALID.getReason() + " " + couponBatchListVo.getInvalidReason());
                } else if (CouponStatusEnum.TC_OUT.getCode().equals(couponBatchListVo.getStatus())) {
                    couponBatchListVo.setInvalidReason(CouponInvalidReasonEnum.TC_INVALID.getReason() + " " + couponBatchListVo.getInvalidReason());
                } else if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
                    couponBatchListVo.setInvalidReason(CouponInvalidReasonEnum.NO_BATCH_DETAIL.getReason());
                }
                // 未开始描述
            } else if (CouponMarketingStatusEnum.NOT_START.getCode().equals(couponBatchListVo.getMarketingStatus())) {
                couponBatchListVo.setInvalidReason(CouponInvalidReasonEnum.NOT_START.getReason());
                // 审核驳回原因
            } else if (CouponMarketingStatusEnum.INVALID.getCode().equals(couponBatchListVo.getMarketingStatus()) && CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchListVo.getAuditStatus())) {
                couponBatchListVo.setInvalidReason(couponBatchListVo.getAuditComment());
            }
        }

    }

    /**
     * 查询红包统计数据
     *
     * @param request
     * @return
     */
    @Override
    public List<CouponStatisticsVo> queryCouponStatistics(QueryCouponBatchListRo request) {
        // 查询所有数据汇总
        QueryCouponBatchListRo queryAllCouponBatchListRo = new QueryCouponBatchListRo();
        queryAllCouponBatchListRo.setTypeList(Arrays.asList(CouponBatchListTypeEnum.PLATFORM.getCode(), CouponBatchListTypeEnum.SUPPLIER.getCode()));
        List<CouponStatisticsVo> allCouponStatisticsVoList = couponBatchListMapper.queryCouponStatistics(queryAllCouponBatchListRo);
        if (CollectionUtil.isEmpty(allCouponStatisticsVoList)) {
            return allCouponStatisticsVoList;
        }
        //清空汇总类型统计外的数据
        clearTypeCount(allCouponStatisticsVoList);

        boolean needQuery = true;
        // 处理供应商id
        if (StringUtils.isNotBlank(request.getSupplierQuery()) && StringUtils.isNumeric(request.getSupplierQuery())) {
            request.setSupplierIdList(Collections.singletonList(Long.valueOf(request.getSupplierQuery())));
        } else if (StringUtils.isNotBlank(request.getSupplierQuery())) {
            LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
            ebkSupplierInfoQueryWrapper.like(WCEbkSupplierInfo::getEICSupplierName, request.getSupplierQuery());
            List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
            if (CollectionUtil.isNotEmpty(ebkSupplierInfoList)) {
                request.setSupplierIdList(ebkSupplierInfoList.stream().map(WCEbkSupplierInfo::getEICSupplierId).collect(Collectors.toList()));
            } else {
                needQuery = false;
            }
        }
        Map<Integer, CouponStatisticsVo> typeMap = new HashMap<>();
        if (needQuery) {
            List<CouponStatisticsVo> queryCouponStatisticsVoList = couponBatchListMapper.queryCouponStatistics(request);
            typeMap = queryCouponStatisticsVoList.stream().collect(Collectors.toMap(CouponStatisticsVo::getType, Function.identity(), (a, b) -> a));
        }

        // 查询符合条件数据汇总
        for (CouponStatisticsVo allCouponStatisticsVo : allCouponStatisticsVoList) {
            CouponStatisticsVo couponStatisticsVo = typeMap.get(allCouponStatisticsVo.getType());
            if (couponStatisticsVo == null) {
                continue;
            }
            // 赋值
            allCouponStatisticsVo.setInvalidCount(couponStatisticsVo.getInvalidCount());
            allCouponStatisticsVo.setActiveCount(couponStatisticsVo.getActiveCount());
            allCouponStatisticsVo.setNotStartCount(couponStatisticsVo.getNotStartCount());
            allCouponStatisticsVo.setExpiredCount(couponStatisticsVo.getExpiredCount());
            allCouponStatisticsVo.setDeListedCount(couponStatisticsVo.getDeListedCount());
            allCouponStatisticsVo.setUnCommitCount(couponStatisticsVo.getUnCommitCount());
            allCouponStatisticsVo.setWaitAuditCount(couponStatisticsVo.getWaitAuditCount());
            allCouponStatisticsVo.setAuditingCount(couponStatisticsVo.getAuditingCount());
            allCouponStatisticsVo.setPassCount(couponStatisticsVo.getPassCount());
            allCouponStatisticsVo.setRejectCount(couponStatisticsVo.getRejectCount());
        }

        return allCouponStatisticsVoList;
    }

    /**
     * 清空汇总类型统计外的数据
     *
     * @param allCouponStatisticsVoList
     */
    private void clearTypeCount(List<CouponStatisticsVo> allCouponStatisticsVoList) {
        for (CouponStatisticsVo couponStatisticsVo : allCouponStatisticsVoList) {
            couponStatisticsVo.setAllActiveCount(couponStatisticsVo.getActiveCount());
            couponStatisticsVo.setAllWaitAuditCount(couponStatisticsVo.getWaitAuditCount());
            couponStatisticsVo.setInvalidCount(null);
            couponStatisticsVo.setActiveCount(null);
            couponStatisticsVo.setNotStartCount(null);
            couponStatisticsVo.setExpiredCount(null);
            couponStatisticsVo.setDeListedCount(null);
            couponStatisticsVo.setUnCommitCount(null);
            couponStatisticsVo.setWaitAuditCount(null);
            couponStatisticsVo.setAuditingCount(null);
            couponStatisticsVo.setPassCount(null);
            couponStatisticsVo.setRejectCount(null);
        }
    }

    @Override
    public CommonPage<CouponBatchDetailDto> pageQueryCouponBatchDetailList(QueryCouponBatchDetailListRo request) {
        CommonPage<CouponBatchDetailDto> couponBatchDetailDtoCommonPage = couponBatchDetailService.pageQuery(request);
        List<CouponBatchDetailDto> couponBatchDetailDtoList = couponBatchDetailDtoCommonPage.getList();
        if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
            return couponBatchDetailDtoCommonPage;
        }
        // 查询所属项目
        List<String> batchNoList = couponBatchDetailDtoList.stream().map(CouponBatchDetailDto::getBatchNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(batchNoList)) {
            List<CouponBatchExtDto> externalCouponBatchList = couponQueryService.getExternalCouponBatchList(batchNoList);
            Map<String, CouponBatchExtDto> externalCouponBatchMap = externalCouponBatchList.stream().collect(Collectors.toMap(CouponBatchExtDto::getBatchNo, Function.identity(), (a, b) -> a));
            for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailDtoList) {
                CouponBatchExtDto couponBatchExtDto = externalCouponBatchMap.get(couponBatchDetailDto.getBatchNo());
                if (couponBatchExtDto != null) {
                    couponBatchDetailDto.setProjectList(couponBatchExtDto.getProjectList());
                }
            }
        }

        return couponBatchDetailDtoCommonPage;
    }

    @Override
    public CouponBatchListDto queryCouponBatchDetail(CouponBatchQueryRo request) {
        CouponBatchList couponBatchList = couponBatchListMapper.selectById(request.getCouponBatchId());
        if (couponBatchList == null) {
            throw new ApiException("红包不存在");
        }

        CouponBatchListDto couponBatchListDto = BeanUtil.copyProperties(couponBatchList, CouponBatchListDto.class);
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdMap = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        couponBatchListDto.setCouponBatchDetailList(couponBatchIdMap.get(request.getCouponBatchId()));
        // 计算红包营销状态和下架原因
        this.calcMarketingStatusAndInvalidReason(couponBatchListDto, couponBatchListDto.getCouponBatchDetailList());

        // 未设置过名称需要去查询数据
        if (Boolean.TRUE.equals(request.getNeedProductInfo()) && CollectionUtil.isNotEmpty(couponBatchListDto.getCouponBatchDetailList()) && StringUtils.isEmpty(couponBatchListDto.getCouponBatchDetailList().get(0).getBatchName())) {
            CouponBatchDetailDto couponBatchDetailDto = couponBatchListDto.getCouponBatchDetailList().get(0);
            Map<Long, CouponBatchRuleDto> couponBatchIdRuleMap = couponBatchRuleService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
            CouponBatchRuleDto couponBatchRuleDto = couponBatchIdRuleMap.get(request.getCouponBatchId());
            // 关联在售产品
            List<WRMainResource> mainResourceList = new ArrayList<>();
            if (couponBatchRuleDto != null) {
                if (CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponBatchRuleDto.getLimitType()) && CollectionUtil.isNotEmpty(couponBatchRuleDto.getDataIdList())) {
                    mainResourceList = mainResourceService.getCanSaleProductByMainResourceSerialIds(couponBatchRuleDto.getDataIdList());
                } else if (CouponRuleLimitTypeEnum.ALL.getCode().equals(couponBatchRuleDto.getLimitType())) {
                    mainResourceList = mainResourceService.getCanSaleProductBySupplierId(couponBatchList.getSupplierId(), couponBatchList.getProductCategoryId());
                }
            }
            // 只取前4个
            couponBatchListDto.setCanSaleMainResourceList(CollectionUtil.sub(mainResourceList, 0, 6));

            // 供应商名称
            LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
            ebkSupplierInfoQueryWrapper.eq(WCEbkSupplierInfo::getEICSupplierId, couponBatchList.getSupplierId());
            List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
            if (CollectionUtil.isNotEmpty(ebkSupplierInfoList)) {
                couponBatchListDto.setSupplierName(ebkSupplierInfoList.get(0).getEICSupplierName());
            }
            if (CollectionUtil.isNotEmpty(mainResourceList)) {
                // 跳转链接
                String linkUrlConfigStr = ConfigUtils.get(ConfigConst.COUPON_LINK_URL);
                LinkUrlConfigDto linkUrlConfigDto = JSON.parseObject(linkUrlConfigStr, LinkUrlConfigDto.class);
                if (linkUrlConfigDto != null && StringUtils.isEmpty(couponBatchDetailDto.getLinkUrlApp())) {
                    couponBatchDetailDto.setLinkUrlApp(TemplateUtils.getContent(linkUrlConfigDto.getAppDetailUrl(), MapUtil.of(LinkUrlConfigDto.PRODUCT_CODE, mainResourceList.get(0).getMRSerialId())));
                }
                if (linkUrlConfigDto != null && StringUtils.isEmpty(couponBatchDetailDto.getLinkUrlWx())) {
                    couponBatchDetailDto.setLinkUrlWx(TemplateUtils.getContent(linkUrlConfigDto.getWxDetailUrl(), MapUtil.of(LinkUrlConfigDto.PRODUCT_CODE, mainResourceList.get(0).getMRSerialId())));
                }
            }
        }

        return couponBatchListDto;
    }


    /**
     * 查询优惠券简单信息
     *
     * @param couponBatchId 优惠券批次id
     * @return {@link CouponSimpleInfoVo}
     */
    @Override
    public CouponSimpleInfoVo queryCouponSimpleInfo(Long couponBatchId) {
        CouponBatchList couponBatchList = getById(couponBatchId);
        if (ObjectUtil.isNull(couponBatchList)) {
            throw new ApiException("红包不存在");
        }
        CouponSimpleInfoVo couponSimpleInfoVo = new CouponSimpleInfoVo();
        couponSimpleInfoVo.setCouponCode(couponBatchList.getCode());
        couponSimpleInfoVo.setCouponBatchId(couponBatchList.getId());
        couponSimpleInfoVo.setAuditStatus(couponBatchList.getAuditStatus());
        couponSimpleInfoVo.setStatus(couponBatchList.getStatus());
        couponSimpleInfoVo.setMarketingStatus(CouponMarketingStatusEnum.INVALID.getCode());
        Map<Long, List<CouponBatchDetailDto>> map = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(couponBatchId));
        if (CollectionUtil.isNotEmpty(map)
                && CollectionUtil.isNotEmpty(map.get(couponBatchId))) {
            CouponBatchDetailDto batchDetail = map.get(couponBatchId).get(0);
            couponSimpleInfoVo.setBatchNo(batchDetail.getBatchNo());
            couponSimpleInfoVo.setBatchCname(batchDetail.getBatchCname());
            couponSimpleInfoVo.setEventValidBeginDate(LocalDateTimeUtil.format(batchDetail.getEventValidBeginDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            couponSimpleInfoVo.setEventValidEndDate(LocalDateTimeUtil.format(batchDetail.getEventValidEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            couponSimpleInfoVo.setMarketingStatus(calcCouponBatchInfoMarketingStatus(couponSimpleInfoVo.getStatus(), map.get(couponBatchId)));
            // 下架原因
            if (CouponMarketingStatusEnum.DE_LISTED.getCode().equals(couponSimpleInfoVo.getMarketingStatus())) {
                if (CouponStatusEnum.B_OUT.getCode().equals(couponBatchList.getStatus())) {
                    couponSimpleInfoVo.setInvalidReason(CouponInvalidReasonEnum.SUPPLIER_INVALID.getReason() + StringUtils.SPACE + couponBatchList.getInvalidReason());
                } else if (CouponStatusEnum.TC_OUT.getCode().equals(couponBatchList.getStatus())) {
                    couponSimpleInfoVo.setInvalidReason(CouponInvalidReasonEnum.TC_INVALID.getReason() + StringUtils.SPACE + couponBatchList.getInvalidReason());
                } else {
                    couponSimpleInfoVo.setInvalidReason(CouponInvalidReasonEnum.NO_BATCH_DETAIL.getReason());
                }
            } else if (CouponMarketingStatusEnum.NOT_START.getCode().equals(couponSimpleInfoVo.getMarketingStatus())) {
                couponSimpleInfoVo.setInvalidReason(CouponInvalidReasonEnum.NOT_START.getReason());
                // 审核驳回原因
            } else if (CouponMarketingStatusEnum.INVALID.getCode().equals(couponSimpleInfoVo.getMarketingStatus()) && CouponAuditStatusEnum.REJECT.getCode().equals(couponSimpleInfoVo.getAuditStatus())) {
                couponSimpleInfoVo.setInvalidReason(couponBatchList.getAuditComment());
            }
        }
        return couponSimpleInfoVo;
    }

    /**
     * 更改优惠券审核状态
     *
     * @param request 请求
     * @return {@link Boolean}
     */
    @Override
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    public Boolean changeCouponAuditStatus(ChangeCouponAuditStatusRo request) {
        CouponBatchList couponBatchList = getById(request.getCouponBatchId());
        if (ObjectUtil.isNull(couponBatchList)) {
            throw new ApiException("红包不存在");
        }
        if (!CouponAuditStatusEnum.WAIT_AUDIT.getCode().equals(couponBatchList.getAuditStatus()) && CouponAuditStatusEnum.UN_COMMIT.getCode().equals(request.getAuditStatus())) {
            throw new ApiException("当前红包不是待审核状态");
        }
        if (CouponAuditStatusEnum.WAIT_AUDIT.getCode().equals(request.getAuditStatus()) && !(CouponAuditStatusEnum.UN_COMMIT.getCode().equals(couponBatchList.getAuditStatus()) || CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchList.getAuditStatus()))) {
            throw new ApiException("当前红包状态不可提交审核");
        }
        if (CouponAuditStatusEnum.WAIT_AUDIT.getCode().equals(request.getAuditStatus()) && (CouponAuditStatusEnum.UN_COMMIT.getCode().equals(couponBatchList.getAuditStatus()) || CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchList.getAuditStatus()))) {
            CouponBatchRule ruleServiceOne = couponBatchRuleService.getOne(new LambdaQueryWrapper<CouponBatchRule>().eq(CouponBatchRule::getCouponBatchId, request.getCouponBatchId()));
            if (ObjectUtil.isNull(ruleServiceOne)) {
                throw new ApiException("当前红包尚未关联产品，无法提交审核");
            }
        }

        Map<Long, List<CouponBatchDetailDto>> couponBatchIdMap = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(couponBatchList.getId()));
        List<CouponBatchDetailDto> couponBatchDetailDtoList = couponBatchIdMap.get(couponBatchList.getId());
        if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
            throw new ApiException("红包详情不存在, 无法提交审核");
        }

        // 需要重置平台红包批次信息
        for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailDtoList) {
            couponBatchDetailDto.setBatchNo(StringUtils.EMPTY);
            couponBatchDetailDto.setBatchName(StringUtils.EMPTY);
            couponBatchDetailDto.setBatchCname(StringUtils.EMPTY);
            couponBatchDetailDto.setUseDesc(StringUtils.EMPTY);
            couponBatchDetailDto.setNotice(StringUtils.EMPTY);
            couponBatchDetailDto.setSceneDesc(StringUtils.EMPTY);
            couponBatchDetailDto.setLinkUrlApp(StringUtils.EMPTY);
            couponBatchDetailDto.setLinkUrlWx(StringUtils.EMPTY);
            couponBatchDetailDto.setStatus(CouponDetailStatusEnum.WAIT_AUDIT.getCode());
            couponBatchDetailDto.setCommitStatus(CouponDetailCommitStatusEnum.WAIT_COMMIT.getCode());
        }

        couponBatchDetailService.saveOrUpdate(couponBatchList, couponBatchDetailDtoList);

        CouponBatchList newBatchList = BeanUtil.copyProperties(couponBatchList, CouponBatchList.class);
        String modifier = adminTokenUtils.getEmployeeName();
        String modifierNo = adminTokenUtils.getEmployeeNo();
        couponBatchList.setAuditStatus(request.getAuditStatus());
        couponBatchList.setModifier(modifier);
        couponBatchList.setModifierNo(modifierNo);
        if (StringUtils.isNotBlank(request.getAuditComment())) {
            couponBatchList.setAuditComment(request.getAuditComment());
        }
        couponBatchList.setModifiedTime(LocalDateTime.now());
        if (updateById(couponBatchList)) {
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(LogActionEnum.Update)
                    .logType1("ICouponBatchListService")
                    .logType2("改变红包审核状态")
                    .filter(request.getCouponBatchId().toString())
                    .content(BeanCompareUtils.compareStr(couponBatchList, newBatchList))
                    .operateTime(LocalDateTime.now())
                    .operator(modifier)
                    .operatorNum(modifierNo)
                    .build());
            return true;
        }
        return false;
    }

    /**
     * 更改状态
     *
     * @param request 请求
     * @return {@link Boolean}
     */
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    @Override
    public Boolean changeStatus(CouponChangeStatusRo request) {
        CouponBatchList couponBatchList = getById(request.getCouponBatchId());
        if (ObjectUtil.isNull(couponBatchList)) {
            throw new ApiException("红包不存在");
        }

        if ((CouponStatusEnum.B_OUT.getCode().equals(request.getStatus()) || CouponStatusEnum.TC_OUT.getCode().equals(request.getStatus())) && StringUtils.isBlank(request.getInvalidReason())) {
            throw new ApiException("下架原因不能为空");
        }

        CouponBatchList newBatchList = BeanUtil.copyProperties(couponBatchList, CouponBatchList.class);
        String modifier = adminTokenUtils.getEmployeeName();
        String modifierNo = adminTokenUtils.getEmployeeNo();
        newBatchList.setStatus(request.getStatus());
        newBatchList.setModifier(modifier);
        newBatchList.setModifierNo(modifierNo);
        if (StringUtil.isNotBlank(request.getInvalidReason())) {
            newBatchList.setInvalidReason(request.getInvalidReason());
        }
        newBatchList.setModifiedTime(LocalDateTime.now());

        if (CouponStatusEnum.B_OUT.getCode().equals(request.getStatus()) || CouponStatusEnum.TC_OUT.getCode().equals(request.getStatus())) {
            //  下架操作 不可逆 调用鲲鹏接口关闭红包
            List<CouponBatchDetail> couponBatchDetails = couponBatchDetailMapper.selectList(new QueryWrapper<CouponBatchDetail>().lambda().eq(CouponBatchDetail::getCouponBatchId, request.getCouponBatchId()).eq(CouponBatchDetail::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
            for (CouponBatchDetail couponBatchDetail : couponBatchDetails) {
                CouponCommonResult<String> closeResult = couponAdminService.closeActivity(couponBatchDetail.getBatchNo());
                if (closeResult == null || !Boolean.TRUE.equals(closeResult.getSuccess())) {
                    throw new ApiException("调用鲲鹏接口关闭红包失败");
                }
            }
        }
        if (updateById(newBatchList)) {
            // couponBatchListService.refreshCouponBatchListStatus(Collections.singletonList(request.getCouponBatchId()));
            bizLogUtils.writeLog(BizLogDto.builder()
                    .action(LogActionEnum.Update)
                    .logType1("ICouponBatchListService")
                    .logType2("改变红包状态")
                    .filter(request.getCouponBatchId().toString())
                    .content(BeanCompareUtils.compareStr(couponBatchList, newBatchList))
                    .operateTime(LocalDateTime.now())
                    .operator(modifier)
                    .operatorNum(modifierNo)
                    .build());
            return true;
        }
        return false;
    }

    /**
     * 查询已绑定其他优惠券的refId列表
     *
     * @param request
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> queryHasBindOtherCouponRefIdList(QueryHasBindOtherCouponRefIdListRo request) {
        List<String> bindOtherRefidList = new ArrayList<>();
        if (CollectionUtil.isEmpty(request.getRefIdList())) {
            return bindOtherRefidList;
        }
        // 所有的refId关联的红包信息
        LambdaQueryWrapper<CouponBatchOperationChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CouponBatchOperationChannel::getRefid, request.getRefIdList());
        List<CouponBatchOperationChannel> allCouponBatchOperationChannels = couponBatchOperationChannelService.list(queryWrapper);

        if (CollectionUtil.isEmpty(allCouponBatchOperationChannels)) {
            return bindOtherRefidList;
        }
        // 去重后所有的红包id
        List<Long> couponBatchIds = allCouponBatchOperationChannels.stream()
                .map(CouponBatchOperationChannel::getCouponBatchId)
                .distinct().collect(Collectors.toList());
        // 用于计算红包状态
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdDetailMap = couponBatchDetailService.selectByCouponBatchId(couponBatchIds);
        List<CouponBatchList> couponBatchListList = couponBatchListMapper.selectBatchIds(couponBatchIds);
        Map<Long, CouponBatchList> couponBatchListMap = couponBatchListList.stream().collect(Collectors.toMap(CouponBatchList::getId, Function.identity()));

        Map<String, List<Long>> refidToCouponBatchIdMap = allCouponBatchOperationChannels.stream()
                .collect(Collectors.groupingBy(CouponBatchOperationChannel::getRefid,
                        Collectors.mapping(CouponBatchOperationChannel::getCouponBatchId, Collectors.toList())));

        for (String refId : request.getRefIdList()) {
            // 1.获取当前 refId 关联的所有红包 id
            List<Long> relatedCouponBatchIds = refidToCouponBatchIdMap.get(refId);
            if (CollectionUtil.isEmpty(relatedCouponBatchIds)) {
                continue;
            }
            // 2.判断是否需要计算状态 ->  未传递红包id | 绑定了其他红包id
            for (Long couponBatchId : relatedCouponBatchIds) {
                CouponBatchList couponBatchList = couponBatchListMap.get(couponBatchId);
                List<CouponBatchDetailDto> couponBatchDetailDtoList = couponBatchIdDetailMap.get(couponBatchId);

                if (couponBatchId.equals(request.getCouponBatchId()) || couponBatchList == null || couponBatchDetailDtoList == null) {
                    continue;
                }
                // 3.计算营销状态
                Integer marketingStatus = calcCouponBatchInfoMarketingStatus(couponBatchList.getStatus(), couponBatchDetailDtoList);

                // 4.添加未生效、活动中、未开始状态的 refId
                if (marketingStatus.equals(CouponMarketingStatusEnum.INVALID.getCode())
                        || marketingStatus.equals(CouponMarketingStatusEnum.ACTIVE.getCode())
                        || marketingStatus.equals(CouponMarketingStatusEnum.NOT_START.getCode())) {
                    bindOtherRefidList.add(refId);
                    break;
                }
            }
        }
        return bindOtherRefidList;
    }

    @Override
    public List<CheckCouponBatchVo> checkCouponBatchList(CheckCouponBatchRo request) {
        List<CheckCouponBatchVo> checkCouponBatchVoList = new ArrayList<>();

        // 查询红包批次是否已经被使用详情
        List<CouponBatchDetailDto> dbCouponBatchDetailDtoList = couponBatchDetailService.selectByBatchNoList(request.getBatchNoList(), false);
        if (CollectionUtil.isEmpty(dbCouponBatchDetailDtoList)) {
            dbCouponBatchDetailDtoList = new ArrayList<>();
        }
        Map<String, CouponBatchDetailDto> dbBatchNoMap = dbCouponBatchDetailDtoList.stream().collect(Collectors.toMap(CouponBatchDetailDto::getBatchNo, Function.identity(), (a, b) -> a));
        List<String> needQueryKunPengBatchNoList = request.getBatchNoList().stream().filter(batchNo -> !dbBatchNoMap.containsKey(batchNo)).collect(Collectors.toList());

        //  查询红包批次是否能被正常使用 调用鲲鹏接口
        Map<String, CouponBatchExtDto> batchNoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(needQueryKunPengBatchNoList)) {
            List<CouponBatchExtDto> externalCouponBatchList = couponQueryService.getExternalCouponBatchList(request.getBatchNoList());
            if (CollectionUtil.isNotEmpty(externalCouponBatchList)) {
                batchNoMap = externalCouponBatchList.stream().collect(Collectors.toMap(CouponBatchExtDto::getBatchNo, Function.identity(), (a, b) -> a));
            }
        }

        for (String batchNo : request.getBatchNoList()) {
            CouponBatchDetailDto dbCouponBatchDetailDto = dbBatchNoMap.get(batchNo);
            CouponBatchExtDto couponBatchExtDto = batchNoMap.get(batchNo);
            CheckCouponBatchVo checkCouponBatchVo = new CheckCouponBatchVo();
            checkCouponBatchVo.setBatchNo(batchNo);

            // 1.判断是否已经被绑定
            if (dbCouponBatchDetailDto != null && !Objects.equals(dbCouponBatchDetailDto.getCouponBatchId(), request.getCouponBatchId())) {
                checkCouponBatchVo.setErrorMessage(BatchNoErrorMessageConst.BATCH_NO_HAS_BIND);
                // 2.判断是否能被正常使用
            } else if (dbCouponBatchDetailDto == null) {
                // 3.红包状态是否正常
                if (couponBatchExtDto == null || !CouponDetailStatusEnum.PASS.getCode().equals(couponBatchExtDto.getBatchStatus())) {
                    checkCouponBatchVo.setErrorMessage(BatchNoErrorMessageConst.BATCH_NO_NOT_VALID);
                    // 4.判断所属醒目是否正确
                } else if (CollectionUtil.isEmpty(couponBatchExtDto.getProjectList()) || couponBatchExtDto.getProjectList().stream().map(ProjectDto::getProjectId).noneMatch(id -> CouponProjectTypeEnum.DayTrip.getProjectId().equals(id))) {
                    checkCouponBatchVo.setErrorMessage(BatchNoErrorMessageConst.BATCH_NO_PROJECT_ERROR);
                    // 5.判断红包类型是否正确
                } else if (!CouponTypeEnum.FULL_DISCOUNT.getCode().equals(couponBatchExtDto.getCouponType()) && !CouponTypeEnum.DISCOUNT.getCode().equals(couponBatchExtDto.getCouponType()) && !CouponTypeEnum.FULL_DISCOUNT_GRADIENT.getCode().equals(couponBatchExtDto.getCouponType())) {
                    checkCouponBatchVo.setErrorMessage(BatchNoErrorMessageConst.BATCH_NO_TYPE_ERROR);
                } else if (ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchExtDto.getExpType()) && couponBatchExtDto.getEndDate() != null && couponBatchExtDto.getExpEndDate() != null && couponBatchExtDto.getEndDate().isAfter(couponBatchExtDto.getExpEndDate())) {
                    checkCouponBatchVo.setErrorMessage(BatchNoErrorMessageConst.BATCH_NO_DATE_ERROR);
                }

            }
            if (StringUtils.isNotBlank(checkCouponBatchVo.getErrorMessage())) {
                checkCouponBatchVoList.add(checkCouponBatchVo);
            }
        }

        return checkCouponBatchVoList;
    }

    /**
     * 查询优惠券批量相关产品
     *
     * @param request 请求
     * @return {@link List}<{@link ProductOptionVo}>
     */
    @Override
    public List<ProductOptionVo> queryCouponBatchRelatedProductList(CouponRelatedProductRo request) {
        Long supplierId;
        if (request.getSupplierId() == null && StringUtils.isNotBlank(adminTokenUtils.getEmployeeNo())) {
            supplierId = Long.valueOf(adminTokenUtils.getEmployeeNo());
        } else {
            supplierId = request.getSupplierId();
        }

        if (supplierId == null) {
            return Collections.emptyList();
        }
        CouponBatchList couponBatchList = couponBatchListMapper.selectById(request.getCouponBatchId());
        if (couponBatchList == null) {
            throw new ApiException("红包不存在");
        }
        // 查询红包规则
        Map<Long, CouponBatchRuleDto> longCouponBatchRuleDtoMap = couponBatchRuleService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        // 红包规则不存在
        if (CollectionUtil.isEmpty(longCouponBatchRuleDtoMap) || ObjectUtil.isNull(longCouponBatchRuleDtoMap.get(request.getCouponBatchId()))) {
            return Collections.emptyList();
        }

        CouponBatchRuleDto couponBatchRuleDto = longCouponBatchRuleDtoMap.get(request.getCouponBatchId());
        Integer limitType = couponBatchRuleDto.getLimitType();
        CouponRuleLimitTypeEnum couponRuleLimitTypeEnum = EnumUtil.getBy(CouponRuleLimitTypeEnum::getCode, limitType);
        //指定产品
        List<String> dataIdList = longCouponBatchRuleDtoMap.get(request.getCouponBatchId()).getDataIdList();
        List<WRMainResource> mainResourceList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataIdList)) {
            if (CouponRuleLimitTypeEnum.PRODUCT.equals(couponRuleLimitTypeEnum)) {
                mainResourceList = mainResourceService.getCanSaleProductByMainResourceSerialIds(dataIdList);
            }
        } else if (CollectionUtil.isEmpty(dataIdList) && CouponRuleLimitTypeEnum.ALL.equals(couponRuleLimitTypeEnum)) {
            mainResourceList = mainResourceService.getCanSaleProductBySupplierId(supplierId, couponBatchList.getProductCategoryId());
        } else {
            return Collections.emptyList();
        }
        return mainResourceList.stream().map(mainResource -> {
            ProductOptionVo productOptionVo = new ProductOptionVo();
            productOptionVo.setValue(mainResource.getMRSerialId());
            productOptionVo.setLabel(StrUtil.BRACKET_START + mainResource.getMRSerialId() + StrUtil.BRACKET_END + mainResource.getMRTitle() + "+" + mainResource.getMRSubTitle());
            return productOptionVo;
        }).collect(Collectors.toList());
    }

    /**
     * 刷新红包状态
     *
     * @param couponBatchIdList 优惠券批次id
     * @return
     */
    @Override
    public Boolean refreshCouponBatchListStatus(List<Long> couponBatchIdList) {
        if (CollectionUtil.isEmpty(couponBatchIdList)) {
            return false;
        }
        LambdaQueryWrapper<CouponBatchList> queryWrapper = new LambdaQueryWrapper<CouponBatchList>().in(CouponBatchList::getId, couponBatchIdList);
        List<CouponBatchList> couponBatchListList = couponBatchListMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(couponBatchListList)) {
            return false;
        }
        // 数据准备
        List<Long> existsCouponBatchIdList = couponBatchListList.stream().map(CouponBatchList::getId).collect(Collectors.toList());
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdDetailMap = couponBatchDetailService.selectByCouponBatchId(existsCouponBatchIdList);
        Map<Long, CouponBatchRuleDto> couponBatchIdRuleMap = couponBatchRuleService.selectByCouponBatchId(existsCouponBatchIdList);
        // 计算红包状态
        for (CouponBatchList couponBatchList : couponBatchListList) {
            Integer status = couponBatchList.getStatus();
            String invalidReason = couponBatchList.getInvalidReason();
            List<CouponBatchDetailDto> couponBatchDetailDtoList = couponBatchIdDetailMap.get(couponBatchList.getId());
            // 商家红包未审核通过则根据审核状态判断状态 //  && CouponStatusEnum.EXPIRED.getCode().equals(couponBatchList.getStatus())
            if (CouponBatchListTypeEnum.SUPPLIER.getCode().equals(couponBatchList.getType())) {
                // 未提交和驳回状态为未提交
                invalidReason = StringUtils.EMPTY;
                if (CouponAuditStatusEnum.UN_COMMIT.getCode().equals(couponBatchList.getAuditStatus()) || CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchList.getAuditStatus())) {
                    status = CouponStatusEnum.UN_COMMIT.getCode();
                    if (CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchList.getAuditStatus())) {
                        invalidReason = couponBatchList.getAuditComment();
                    }
                } else if (CouponAuditStatusEnum.WAIT_AUDIT.getCode().equals(couponBatchList.getAuditStatus())) {
                    status = CouponStatusEnum.INVALID.getCode();
                } else if (CouponAuditStatusEnum.AUDITING.getCode().equals(couponBatchList.getAuditStatus()) || CouponAuditStatusEnum.PASS.getCode().equals(couponBatchList.getAuditStatus())) {
                    status = this.calcCouponBatchInfoStatus(couponBatchDetailDtoList, couponBatchIdRuleMap.get(couponBatchList.getId()));
                    if (CouponStatusEnum.VALID.getCode().equals(status)) {
                        couponBatchList.setAuditStatus(CouponAuditStatusEnum.PASS.getCode());
                    } else if (CouponStatusEnum.REJECT.getCode().equals(status)) {
                        status = CouponStatusEnum.UN_COMMIT.getCode();
                        // 鲲鹏驳回则红包审核驳回
                        couponBatchList.setAuditStatus(CouponAuditStatusEnum.REJECT.getCode());
                    }
                }

                // 平台红包是否维护完成所有信息
            } else if (CouponBatchListTypeEnum.PLATFORM.getCode().equals(couponBatchList.getType())) {
                status = this.calcCouponBatchInfoStatus(couponBatchDetailDtoList, couponBatchIdRuleMap.get(couponBatchList.getId()));
                if (!couponBatchIdDetailMap.containsKey(couponBatchList.getId())) {
                    invalidReason = CouponInvalidReasonEnum.NO_BATCH_DETAIL.getReason();
                } else if (!couponBatchIdRuleMap.containsKey(couponBatchList.getId())) {
                    invalidReason = CouponInvalidReasonEnum.NO_RULE.getReason();
                }
                if (CouponStatusEnum.REJECT.getCode().equals(status)) {
                    // 平台已下架则不更新状态
                    status = null;
                }
            }
            if (CouponStatusEnum.VALID.getCode().equals(status)) {
                invalidReason = StringUtils.EMPTY;
            }
            // 有变化则更新 人工下架不做更新 只更新审核状态
            if (!CouponStatusEnum.TC_OUT.getCode().equals(couponBatchList.getStatus()) && !CouponStatusEnum.B_OUT.getCode().equals(couponBatchList.getStatus())) {
                couponBatchList.setStatus(status);
                couponBatchList.setInvalidReason(invalidReason);
            }
            couponBatchList.setModifiedTime(LocalDateTime.now());
            couponBatchListMapper.updateById(couponBatchList);
        }

        return true;
    }

    /**
     * 查询鲲鹏红包批次详情
     *
     * @param request 请求
     * @return
     */
    @Override
    public List<CouponBatchDetailDto> queryKunPengCouponBatchDetail(KunPengCouponBatchDetailListRo request) {
        // 查询鲲鹏批次信息 已有数据需同步到本地
        List<CouponBatchExtDto> couponBatchList = couponQueryService.getExternalCouponBatchList(request.getBatchNoList());
        if (CollectionUtil.isEmpty(couponBatchList)) {
            return Collections.emptyList();
        }
        // 转换为红包批次详情
        List<CouponBatchDetailDto> newCouponBatchDetailList = CouponBatchDetailDto.detailDtoFromExtDto(couponBatchList);
        // 计算红包营销状态
        newCouponBatchDetailList.forEach(couponBatchDetailService::calcDetailMarketingStatus);
        // 返回新查询的鲲鹏批次信息
        return newCouponBatchDetailList;
    }

    @Override
    public List<CouponBatchDetailDto> queryKunPengCouponBatchDetailAndUpdateDb(KunPengCouponBatchDetailListRo request) {
        // 查询本地红包批次数据
        List<CouponBatchDetailDto> oldCouponBatchDetailDtoList = couponBatchDetailService.selectByBatchNoList(request.getBatchNoList(), null);

        // 去除过期的红包批次
        List<String> notNeedQueryBatchNoList = oldCouponBatchDetailDtoList.stream().filter(
                dto -> {
                    // 状态已过期 并且不是 满减阶梯需要一直查询规则
                    if (!CouponTypeEnum.FULL_DISCOUNT_GRADIENT.getCode().equals(dto.getCouponType()) && !CouponDetailStatusEnum.PASS.getCode().equals(dto.getStatus()) && !CouponDetailStatusEnum.AUDITING.getCode().equals(dto.getStatus())) {
                        return true;
                    }
                    return false;
                }
        ).map(CouponBatchDetailDto::getBatchNo).collect(Collectors.toList());

        // 查询鲲鹏批次信息 已有数据需同步到本地
        List<String> needQueryBatchNoList = request.getBatchNoList().stream().filter(batchNo -> !notNeedQueryBatchNoList.contains(batchNo)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(needQueryBatchNoList)) {
            return Collections.emptyList();
        }
        SkyLogUtils.infoByMethod(log, StringUtils.format("开始调用查询鲲鹏红包批次信息接口:{}", JSON.toJSONString(needQueryBatchNoList)), "", "");
        List<CouponBatchExtDto> couponBatchList = couponQueryService.getExternalCouponBatchList(needQueryBatchNoList);
        SkyLogUtils.infoByMethod(log, StringUtils.format("调用查询鲲鹏红包批次信息接口结果:{}", JSON.toJSONString(couponBatchList)), "", "");
        if (CollectionUtil.isEmpty(couponBatchList)) {
            return Collections.emptyList();
        }
        // 转换为红包批次详情
        List<CouponBatchDetailDto> newCouponBatchDetailList = CouponBatchDetailDto.detailDtoFromExtDto(couponBatchList);
        Map<String, CouponBatchDetailDto> batchNoMap = newCouponBatchDetailList.stream().collect(Collectors.toMap(CouponBatchDetailDto::getBatchNo, Function.identity(), (a, b) -> a));

        // 有历史数据需要去更新红包状态和批次信息
        if (CollectionUtil.isNotEmpty(oldCouponBatchDetailDtoList)) {
            // 更新红包信息
            for (CouponBatchDetailDto couponBatchDetailDto : oldCouponBatchDetailDtoList) {
                CouponBatchDetailDto newCouponBatchDetailDto = batchNoMap.get(couponBatchDetailDto.getBatchNo());
                if (newCouponBatchDetailDto == null) {
                    // TODO 暂不删除红包
                    continue;
                }
                newCouponBatchDetailDto.setId(couponBatchDetailDto.getId());
                couponBatchDetailService.updateDetail(newCouponBatchDetailDto);
                turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC
                        , CouponBatchSyncDto.builder().batchNo(newCouponBatchDetailDto.getBatchNo()).build());
            }
        }
        // 计算红包营销状态
        newCouponBatchDetailList.forEach(couponBatchDetailService::calcDetailMarketingStatus);
        // 返回新查询的鲲鹏批次信息
        return newCouponBatchDetailList;
    }

    /**
     * 计算红包状态
     *
     * @param couponBatchDetailDtoList
     * @param couponBatchRuleDto
     */
    private Integer calcCouponBatchInfoStatus(List<CouponBatchDetailDto> couponBatchDetailDtoList, CouponBatchRuleDto couponBatchRuleDto) {
        Integer status = CouponMarketingStatusEnum.INVALID.getCode();
        if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
            return status;
        }
        // 无红包批次号 等于未申请鲲鹏红包
        List<CouponBatchDetailDto> validCouponBatchDetailList = couponBatchDetailDtoList.stream().filter(dto -> StringUtils.isNotBlank(dto.getBatchNo())).collect(Collectors.toList());
        // 批次和规则数据未维护完成 则未生效
        if (CollectionUtil.isEmpty(validCouponBatchDetailList) || ObjectUtil.isNull(couponBatchRuleDto)) {
            return status;
        }
        // 鲲鹏红包审核中 则未生效 无需推进红包主表状态
        // 鲲鹏红包审核通过 并且未超过有效期 有一个未超过则已生效
        boolean valid = false;
        // 鲲鹏红包审核通过 且当前时间超过有效期 全部过期则已过期
        boolean expired = true;
        // 是否全部审核驳回
        boolean allReject = true;
        LocalDate currentDay = LocalDate.now();
        for (CouponBatchDetailDto couponBatchDetailDto : validCouponBatchDetailList) {
            // 有一个未过期 则未过期
            if (!currentDay.isAfter(couponBatchDetailDto.getEventValidEndDate().toLocalDate())) {
                expired = false;
                if (CouponDetailStatusEnum.PASS.getCode().equals(couponBatchDetailDto.getStatus())) {
                    valid = true;
                    allReject = false;
                    break;
                } else if (CouponDetailStatusEnum.AUDITING.getCode().equals(couponBatchDetailDto.getStatus())) {
                    // 有一个审核中 则未生效
                    allReject = false;
                    break;
                }
            }
            // 有一个未驳回 则未驳回
            if (!CouponDetailStatusEnum.REJECT.getCode().equals(couponBatchDetailDto.getStatus())) {
                allReject = false;
            }
        }
        if (valid) {
            status = CouponStatusEnum.VALID.getCode();
        } else if (allReject) {
            status = CouponStatusEnum.REJECT.getCode();
        } else if (expired) {
            status = CouponStatusEnum.EXPIRED.getCode();
        } else {
            // 都不是则不推进状态， 真实状态由计算得出
            status = null;
        }
        return status;
    }

    /**
     * 计算红包营销状态
     *
     * @param couponBatchDetailDtoList
     */
    private int calcCouponBatchInfoMarketingStatus(int couponBatchListStatus, List<CouponBatchDetailDto> couponBatchDetailDtoList) {
        int status = CouponMarketingStatusEnum.INVALID.getCode();
        if (CollectionUtil.isEmpty(couponBatchDetailDtoList)) {
            return status;
        }
        // 无红包批次号 等于未申请鲲鹏红包
        List<CouponBatchDetailDto> validCouponBatchDetailList = couponBatchDetailDtoList.stream().filter(dto -> StringUtils.isNotBlank(dto.getBatchNo())).collect(Collectors.toList());
        // 数据未维护完成 则未生效
        if (CollectionUtil.isEmpty(validCouponBatchDetailList) || CouponStatusEnum.UN_COMMIT.getCode().equals(couponBatchListStatus)) {
            return status;
        }
//        // 未生效无需计算
//        if (CouponStatusEnum.UN_COMMIT.getCode().equals(couponBatchListStatus) || CouponStatusEnum.INVALID.getCode().equals(couponBatchListStatus)) {
//            return status;
//        } else
        // 商家主动下架则下架
        if (CouponStatusEnum.TC_OUT.getCode().equals(couponBatchListStatus) || CouponStatusEnum.B_OUT.getCode().equals(couponBatchListStatus)) {
            return CouponMarketingStatusEnum.DE_LISTED.getCode();
            // 红包过期则过期
        } else if (CouponStatusEnum.EXPIRED.getCode().equals(couponBatchListStatus)) {
            return CouponMarketingStatusEnum.EXPIRED.getCode();
        }
        List<Integer> marketStatusList = validCouponBatchDetailList.stream().map(CouponBatchDetailDto::getMarketingStatus).collect(Collectors.toList());

        // 有一个活动中 则活动中
        if (marketStatusList.contains(CouponMarketingStatusEnum.ACTIVE.getCode())) {
            return CouponMarketingStatusEnum.ACTIVE.getCode();
            // 有一个未开始 则未开始
        } else if (marketStatusList.contains(CouponMarketingStatusEnum.NOT_START.getCode())) {
            return CouponMarketingStatusEnum.NOT_START.getCode();
        } else if (marketStatusList.contains(CouponMarketingStatusEnum.INVALID.getCode())) {
            // 有未生效的 则未生效
            return CouponMarketingStatusEnum.INVALID.getCode();
        } else if (marketStatusList.contains(CouponMarketingStatusEnum.EXPIRED.getCode())) {
            // 是否全部过期
            return CouponMarketingStatusEnum.EXPIRED.getCode();
        } else {
            // 全部下架 则已下架
            return CouponMarketingStatusEnum.DE_LISTED.getCode();
        }

    }

    /**
     * 审核红包
     *
     * @param request
     * @return
     */
    @Override
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    public Boolean auditCouponBatch(AuditCouponBatchRo request) {
        CouponBatchDetailDto kunPengCouponApply = request.getKunPengCouponApply();
        if (CouponAuditResultEnum.PASS.getCode().equals(request.getAuditResult()) && kunPengCouponApply == null) {
            throw new ApiException("鲲鹏红包不能为空");
        }
        // 审核通过需要校验活动有效期
        if (CouponAuditResultEnum.PASS.getCode().equals(request.getAuditResult())) {
            if (kunPengCouponApply.getEventValidBeginDate() == null || kunPengCouponApply.getEventValidEndDate() == null) {
                throw new ApiException("活动有效期不能为空");
            }
            if (kunPengCouponApply.getCurrentDate().isAfter(kunPengCouponApply.getEventValidEndDate())) {
                throw new ApiException("活动有效期小于当前日期, 无法审核");
            }
            if (kunPengCouponApply.getCurrentDate().isAfter(kunPengCouponApply.getEventValidBeginDate())) {
                kunPengCouponApply.setEventValidBeginDate(kunPengCouponApply.getCurrentDate());
            }
        }

        if (CouponAuditResultEnum.REJECT.getCode().equals(request.getAuditResult()) && StringUtils.isEmpty(request.getAuditComment())) {
            throw new ApiException("不通过理由不能为空");
        }

        CouponBatchList couponBatchList = couponBatchListMapper.selectById(request.getCouponBatchId());
        if (couponBatchList == null) {
            throw new ApiException("未查询到红包信息");
        }

        if (!CouponAuditStatusEnum.WAIT_AUDIT.getCode().equals(couponBatchList.getAuditStatus())) {
            throw new ApiException("当前红包状态不可审核");
        }

        if (CouponAuditResultEnum.PASS.getCode().equals(request.getAuditResult())) {
            // 审核通过需要推送鲲鹏红包申请
            couponBatchList.setAuditStatus(CouponAuditStatusEnum.AUDITING.getCode());
            couponBatchList.setStatus(CouponStatusEnum.INVALID.getCode());
            couponBatchList.setModifier(adminTokenUtils.getEmployeeName());
            couponBatchList.setModifierNo(adminTokenUtils.getEmployeeNo());
            couponBatchList.setModifiedTime(LocalDateTime.now());
            couponBatchList.setAuditComment(StringUtils.EMPTY);
            couponBatchList.setAuditUser(adminTokenUtils.getEmployeeName());
            couponBatchList.setAuditTime(LocalDateTime.now());
            couponBatchListMapper.updateById(couponBatchList);
            // 推送鲲鹏
            Boolean result = couponBatchListService.pushKunPengCouponApplyAndApprove(kunPengCouponApply);
            if (!Boolean.TRUE.equals(result)) {
                throw new ApiException("推送鲲鹏红包申请失败，请稍后重试");
            }
            couponBatchDetailService.saveOrUpdate(couponBatchList, Collections.singletonList(kunPengCouponApply));
            turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC
                    , CouponBatchSyncDto.builder().batchNo(kunPengCouponApply.getBatchNo()).build());
        } else if (CouponAuditResultEnum.REJECT.getCode().equals(request.getAuditResult())) {
            // 审核不通过结束
            if (CouponAuditStatusEnum.REJECT.getCode().equals(couponBatchList.getAuditStatus())) {
                return true;
            }
            couponBatchList.setStatus(CouponStatusEnum.UN_COMMIT.getCode());
            couponBatchList.setAuditStatus(CouponAuditStatusEnum.REJECT.getCode());
            couponBatchList.setModifier(adminTokenUtils.getEmployeeName());
            couponBatchList.setModifierNo(adminTokenUtils.getEmployeeNo());
            couponBatchList.setModifiedTime(LocalDateTime.now());
            couponBatchList.setAuditComment(request.getAuditComment());
            couponBatchList.setAuditUser(adminTokenUtils.getEmployeeName());
            couponBatchList.setAuditTime(LocalDateTime.now());
            couponBatchListMapper.updateById(couponBatchList);
        }

        // 日志
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("ICouponBatchListService")
                .logType2("审核红包")
                .filter(String.valueOf(request.getCouponBatchId()))
                .content(JSON.toJSONString(request))
                .operateTime(LocalDateTime.now())
                .operator(adminTokenUtils.getEmployeeName())
                .operatorNum(adminTokenUtils.getEmployeeNo())
                .build());

        return true;
    }

    /**
     * 保存红包信息
     *
     * @param request
     * @return
     */
    @Override
    @MultipleTransaction(name = TransactionConstant.TcZBActivityMarketing)
    public Long saveCouponBatch(CouponBatchListDto request) {
        SkyLogUtils.infoByMethod(log, StringUtils.format("保存红包批次信息,请求参数:{}", JSON.toJSONString(request)), "", "");
        CouponBatchList couponBatchList = BeanUtil.copyProperties(request, CouponBatchList.class);
        if (CouponBatchListTypeEnum.SUPPLIER.getCode().equals(couponBatchList.getType()) && ObjectUtil.isNull(request.getSupplierId())) {
            String employeeNo = adminTokenUtils.getEmployeeNo();
            if (StringUtils.isEmpty(employeeNo)) {
                throw new ApiException("商家红包商家id不能为空");
            }
            couponBatchList.setSupplierId(Long.valueOf(employeeNo));
        }

        // 校验参数
        this.checkParameters(request);

        // 保存红包信息 每次都需要更新 操作时间
        couponBatchList = couponBatchListService.saveCouponBatchList(couponBatchList);
        // 保存红包批次信息
        couponBatchDetailService.saveOrUpdate(couponBatchList, request.getCouponBatchDetailList());
        // 保存点位信息
        couponBatchOperationService.saveOrUpdate(couponBatchList, request.getCouponBatchOperationList());
        // 保存渠道信息
        couponBatchOperationChannelService.saveOrUpdate(couponBatchList, request.getCouponBatchOperationChannelList(), request.getCouponBatchOperationList());
        // 保存规则信息
        couponBatchRuleService.saveOrUpdate(couponBatchList, request.getCouponBatchRule());
        // 刷新红包状态 有批次推送mq 无批次 只计算红包状态
        if (CollectionUtil.isNotEmpty(request.getCouponBatchDetailList())) {
            for (CouponBatchDetailDto couponBatchDetailDto : request.getCouponBatchDetailList()) {
                turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC
                        , CouponBatchSyncDto.builder().batchNo(couponBatchDetailDto.getBatchNo()).build());
            }
        } else {
            couponBatchListService.refreshCouponBatchListStatus(Collections.singletonList(couponBatchList.getId()));
        }

        // 日志
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("ICouponBatchListService")
                .logType2("编辑红包")
                .filter(String.valueOf(request.getId()))
                .content(JSON.toJSONString(couponBatchList))
                .operateTime(LocalDateTime.now())
                .operator(adminTokenUtils.getEmployeeName())
                .operatorNum(adminTokenUtils.getEmployeeNo())
                .build());

        return couponBatchList.getId();
    }

    /**
     * 校验参数
     *
     * @param request
     */
    private void checkParameters(CouponBatchListDto request) {
        // 校验红包批次信息
        if (CollectionUtil.isNotEmpty(request.getCouponBatchDetailList())) {
            // 检查有效期是否正确 只检查新增时的有效期
            for (CouponBatchDetailDto couponBatchDetailDto : request.getCouponBatchDetailList()) {
                if (couponBatchDetailDto.getId() == null && couponBatchDetailDto.getCouponBatchValidDate() != null
                        && ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchDetailDto.getCouponBatchValidDate().getValidDateType())
                        && couponBatchDetailDto.getCouponBatchValidDate().getValidEndDate() != null
                        && couponBatchDetailDto.getEventValidEndDate() != null
                        && couponBatchDetailDto.getEventValidEndDate().isAfter(couponBatchDetailDto.getCouponBatchValidDate().getValidEndDate())
                ) {
                    throw new ApiException("红包活动有效期结束时间不能晚于领取有效期结束时间");
                }
            }
        }
        // 校验refId是否可用
        if (CollectionUtil.isNotEmpty(request.getCouponBatchOperationChannelList())) {
            QueryHasBindOtherCouponRefIdListRo queryHasBindOtherCouponRefIdListRo = new QueryHasBindOtherCouponRefIdListRo();
            queryHasBindOtherCouponRefIdListRo.setCouponBatchId(request.getId());
            queryHasBindOtherCouponRefIdListRo.setRefIdList(request.getCouponBatchOperationChannelList().stream().map(CouponBatchOperationChannelDto::getRefid).collect(Collectors.toList()));
            List<String> hasBindOtherCouponRefIdList = couponBatchListService.queryHasBindOtherCouponRefIdList(queryHasBindOtherCouponRefIdListRo);
            if (CollectionUtil.isNotEmpty(hasBindOtherCouponRefIdList)) {
                throw new ApiException(hasBindOtherCouponRefIdList + "已绑定其他红包");
            }
        }


    }

    /**
     * 查询红包规则
     *
     * @param request
     * @return
     */
    @Override
    public CouponBatchListDto queryCouponBatchRule(CouponBatchQueryRo request) {
        CouponBatchList couponBatchList = couponBatchListMapper.selectById(request.getCouponBatchId());
        if (couponBatchList == null) {
            throw new ApiException("红包不存在");
        }

        CouponBatchListDto couponBatchListDto = BeanUtil.copyProperties(couponBatchList, CouponBatchListDto.class);
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdMap = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        // 计算红包营销状态
        this.calcMarketingStatusAndInvalidReason(couponBatchListDto, couponBatchIdMap.get(request.getCouponBatchId()));

        // 查询规则明细
        Map<Long, CouponBatchRuleDto> couponBatchIdRuleDtoMap = couponBatchRuleService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        if (CollectionUtil.isEmpty(couponBatchIdRuleDtoMap) || ObjectUtil.isNull(couponBatchIdRuleDtoMap.get(request.getCouponBatchId()))) {
            return couponBatchListDto;
        }

        CouponBatchRuleDto couponBatchRuleDto = couponBatchIdRuleDtoMap.get(request.getCouponBatchId());
        if (CollectionUtil.isEmpty(couponBatchRuleDto.getDataIdList())) {
            couponBatchRuleDto.setDataIdList(Collections.emptyList());
        }
        couponBatchListDto.setCouponBatchRule(couponBatchRuleDto);

        return couponBatchListDto;
    }

    /**
     * 查询红包渠道
     *
     * @param request
     * @return
     */
    @Override
    public CouponBatchListDto queryCouponBatchOperation(CouponBatchQueryRo request) {
        CouponBatchList couponBatchList = couponBatchListMapper.selectById(request.getCouponBatchId());
        if (couponBatchList == null) {
            throw new ApiException("红包不存在");
        }

        CouponBatchListDto couponBatchListDto = BeanUtil.copyProperties(couponBatchList, CouponBatchListDto.class);
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdMap = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        // 计算红包营销状态和下架原因
        this.calcMarketingStatusAndInvalidReason(couponBatchListDto, couponBatchIdMap.get(couponBatchListDto.getId()));
        // 查询红包点位信息
        Map<Long, List<CouponBatchOperationDto>> couponBatchIdOperationMap = couponBatchOperationService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        couponBatchListDto.setCouponBatchOperationList(couponBatchIdOperationMap.get(request.getCouponBatchId()));


        if (CollectionUtil.isNotEmpty(couponBatchListDto.getCouponBatchOperationList())) {
            couponBatchListDto.setCouponBatchOperationCodeList(couponBatchListDto.getCouponBatchOperationList().stream().map(CouponBatchOperationDto::getOperationPositionCode).collect(Collectors.toList()));
            // limitType = 4 需要详情点位code
            LambdaQueryWrapper<CouponBatchRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CouponBatchRule::getCouponBatchId, request.getCouponBatchId());
            List<CouponBatchRule> couponBatchRuleList = couponBatchRuleMapper.selectList(lambdaQueryWrapper);
            String positionCodeListStr = ConfigUtils.get(ConfigConst.COUPON_DETAIL_POSITION_CODE);
            if (StringUtils.isNotBlank(positionCodeListStr) && CollectionUtil.isNotEmpty(couponBatchRuleList) && CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponBatchRuleList.get(0).getLimitType())) {
                List<String> positionCodeList = JSON.parseArray(positionCodeListStr, String.class);
                for (CouponBatchOperationDto couponBatchOperationDto : couponBatchListDto.getCouponBatchOperationList()) {
                    couponBatchOperationDto.setDetailPositionCodeList(positionCodeList);
                }
            }
        }


        // 查询红包批次渠道
        Map<Long, List<CouponBatchOperationChannelDto>> couponBatchIdChannelMap = couponBatchOperationChannelService.selectByCouponBatchId(Collections.singletonList(request.getCouponBatchId()));
        couponBatchListDto.setCouponBatchOperationChannelList(couponBatchIdChannelMap.get(request.getCouponBatchId()));

        return couponBatchListDto;
    }

    /**
     * 删除优惠券批次列表
     *
     * @param request 请求
     * @return {@link Boolean}
     */
    @MultipleTransaction(name = {TransactionConstant.TcZBActivityMarketing})
    @Override
    public Boolean deleteCouponBatchList(CouponBatchQueryRo request) {
        Long couponBatchId = request.getCouponBatchId();
        String modifier = adminTokenUtils.getEmployeeName();
        String employeeNo = adminTokenUtils.getEmployeeNo();
        Boolean b1 = baseMapper.deleteByIds(Collections.singletonList(couponBatchId), modifier, employeeNo);
        Map<Long, List<CouponBatchDetailDto>> detailMapping = couponBatchDetailService.selectByCouponBatchId(Collections.singletonList(couponBatchId));
        if (CollectionUtil.isEmpty(detailMapping) || CollectionUtil.isEmpty(detailMapping.get(request.getCouponBatchId()))) {
            return true;
        }
        List<CouponBatchDetailDto> couponBatchDetailDtos = detailMapping.get(request.getCouponBatchId());
        List<Long> detailIds = couponBatchDetailDtos.stream().map(CouponBatchDetailDto::getId).collect(Collectors.toList());
        Boolean b = couponBatchDetailService.deleteByIds(detailIds);

        // 日志
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Update)
                .logType1("ICouponBatchListService")
                .logType2("删除红包")
                .filter(String.valueOf(request.getCouponBatchId()))
                .content(JSON.toJSONString(request))
                .operateTime(LocalDateTime.now())
                .operator(adminTokenUtils.getEmployeeName())
                .operatorNum(adminTokenUtils.getEmployeeNo())
                .build());

        return b && b1;
    }


    /**
     * 计算红包营销状态和下架原因
     *
     * @param couponBatchListDto
     */
    private void calcMarketingStatusAndInvalidReason(CouponBatchListDto couponBatchListDto, List<CouponBatchDetailDto> couponBatchDetailDtoList) {
        // 计算红包营销状态
        couponBatchListDto.setMarketingStatus(calcCouponBatchInfoMarketingStatus(couponBatchListDto.getStatus(), couponBatchDetailDtoList));
        // 下架原因
        if (CouponMarketingStatusEnum.DE_LISTED.getCode().equals(couponBatchListDto.getStatus())) {
            if (CouponStatusEnum.B_OUT.getCode().equals(couponBatchListDto.getStatus())) {
                couponBatchListDto.setInvalidReason(CouponInvalidReasonEnum.SUPPLIER_INVALID.getReason() + " " + couponBatchListDto.getInvalidReason());
            } else if (CouponStatusEnum.TC_OUT.getCode().equals(couponBatchListDto.getStatus())) {
                couponBatchListDto.setInvalidReason(CouponInvalidReasonEnum.TC_INVALID.getReason() + " " + couponBatchListDto.getInvalidReason());
            } else {
                couponBatchListDto.setInvalidReason(CouponInvalidReasonEnum.NO_BATCH_DETAIL.getReason());
            }
        }
    }

    /**
     * 保存红包批次列表
     *
     * @param couponBatchList
     * @return
     */
    @Override
    public CouponBatchList saveCouponBatchList(CouponBatchList couponBatchList) {
        if (couponBatchList.getId() != null) {
            CouponBatchList oldCouponBatchList = couponBatchListMapper.selectById(couponBatchList.getId());
            if (oldCouponBatchList == null) {
                throw new ApiException("当前红包不存在");
            }
            couponBatchList.setModifier(adminTokenUtils.getEmployeeName());
            couponBatchList.setModifierNo(adminTokenUtils.getEmployeeNo());
            couponBatchList.setModifiedTime(LocalDateTime.now());
            couponBatchListMapper.updateById(couponBatchList);
        } else {
            couponBatchList.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());
            couponBatchList.setCreator(adminTokenUtils.getEmployeeName());
            couponBatchList.setCreatorNo(adminTokenUtils.getEmployeeNo());
            couponBatchList.setCreateTime(LocalDateTime.now());
            couponBatchList.setModifier(adminTokenUtils.getEmployeeName());
            couponBatchList.setModifierNo(adminTokenUtils.getEmployeeNo());
            couponBatchList.setModifiedTime(LocalDateTime.now());
            couponBatchListMapper.insert(couponBatchList);
        }
        return couponBatchListMapper.selectById(couponBatchList.getId());
    }

    @Override
    public Boolean pushKunPengCouponApplyAndApprove(CouponBatchDetailDto kunPengCouponApply) {
        // 待审核或者批次号为空 推送鲲鹏
        if ((CouponDetailStatusEnum.WAIT_AUDIT.getCode().equals(kunPengCouponApply.getStatus()) && !CouponDetailCommitStatusEnum.COMMIT_SUCCESS.getCode().equals(kunPengCouponApply.getCommitStatus())) || StringUtils.isEmpty(kunPengCouponApply.getBatchNo())) {
            // 活动结束日期期小于当前日期 则审核驳回
            if (kunPengCouponApply.getCurrentDate().isAfter(kunPengCouponApply.getEventValidEndDate())) {
                kunPengCouponApply.setStatus(CouponDetailStatusEnum.REJECT.getCode());
                return true;
            }
            // 活动开始日期期小于当前日期 则使用当前日期作为开始日期
            if (kunPengCouponApply.getCurrentDate().isAfter(kunPengCouponApply.getEventValidBeginDate())) {
                kunPengCouponApply.setEventValidBeginDate(kunPengCouponApply.getCurrentDate());
            }

            CouponCommonResult<String> createResult = couponAdminService.createOrUpdateActivity(kunPengCouponApply);
            kunPengCouponApply.setCommitStatus(CouponDetailCommitStatusEnum.WAIT_COMMIT.getCode());
            kunPengCouponApply.setCommitTime(LocalDateTime.now());
            if (ObjectUtil.isNotNull(createResult) && createResult.getSuccess() != null && createResult.getSuccess() && StringUtils.isNotBlank(createResult.getResult())) {
                kunPengCouponApply.setStatus(CouponDetailStatusEnum.WAIT_AUDIT.getCode());
                kunPengCouponApply.setCommitStatus(CouponDetailCommitStatusEnum.COMMIT_SUCCESS.getCode());
                kunPengCouponApply.setBatchNo(createResult.getResult());
            } else {
                //推送鲲鹏失败
                SkyLogUtils.warnByMethod(log, "推送鲲鹏创建红包失败", "", "");
                kunPengCouponApply.setCommitStatus(CouponDetailCommitStatusEnum.COMMIT_ERROR.getCode());
                return false;
            }
        }
        // 待审核 并且提交成功 则提交审核
        if (CouponDetailStatusEnum.WAIT_AUDIT.getCode().equals(kunPengCouponApply.getStatus()) && CouponDetailCommitStatusEnum.COMMIT_SUCCESS.getCode().equals(kunPengCouponApply.getCommitStatus()) && StringUtils.isNotEmpty(kunPengCouponApply.getBatchNo())) {
            // 提交审核
            CouponCommonResult<String> approveResult = couponAdminService.approveActivity(kunPengCouponApply.getBatchNo());
            if (ObjectUtil.isNotNull(approveResult) && approveResult.getSuccess() != null && approveResult.getSuccess()) {
                kunPengCouponApply.setStatus(CouponDetailStatusEnum.AUDITING.getCode());
            } else {
                //推送鲲鹏失败
                SkyLogUtils.warnByMethod(log, "推送鲲鹏审核红包失败", "", "");
                return false;
            }
        }

        if (CouponDetailStatusEnum.AUDITING.getCode().equals(kunPengCouponApply.getStatus())) {
            // 查询红包状态
            CouponBatchExtDto externalCoupon = couponQueryService.getExternalCoupon(kunPengCouponApply.getBatchNo());
            if (externalCoupon != null) {
                kunPengCouponApply.setStatus(externalCoupon.getBatchStatus());
            }
        }

        return true;
    }

    /**
     * 刷新红包状态
     *
     * @param request
     * @return
     */
    @Override
    public Boolean refreshWaitingAuditCouponBatchDetail(BatchPushCouponRo request) {
        request.setCouponBatchListStatusList(Arrays.asList(CouponStatusEnum.INVALID.getCode()));
        request.setCouponBatchDetailStatusList(Arrays.asList(CouponDetailStatusEnum.AUDITING.getCode()));
        List<CouponBatchDetail> waitAuditCouponBatchDetailList = couponBatchDetailService.selectNeedRefreshCouponBatchDetailList(request);
        SkyLogUtils.infoByMethod(log, StringUtils.format("需要刷新的待审核红包批次号:{}", waitAuditCouponBatchDetailList.stream().map(CouponBatchDetail::getBatchNo).collect(Collectors.toList())), "", "");
        for (CouponBatchDetail couponBatchDetail : waitAuditCouponBatchDetailList) {
            if (StringUtils.isEmpty(couponBatchDetail.getBatchNo())) {
                continue;
            }
            turboMqUtils.sendMsg(MQConst.TOPIC_LA_MARKETING_ADMIN_COUPON_SYNC
                    , CouponBatchSyncDto.builder().batchNo(couponBatchDetail.getBatchNo()).build());
        }

        return true;
    }

    /**
     * 控制红包状态
     *
     * @param couponBatchId
     * @param status
     */
    @Override
    public Boolean controlBatchList(String couponBatchId, Integer status) {
        if (StringUtils.isBlank(couponBatchId) || status == null) {
            return false;
        }

        if (!status.equals(CouponStatusEnum.TC_OUT.getCode()) && !status.equals(CouponStatusEnum.VALID.getCode())) {
            return false;
        }

        CouponBatchList couponBatchList = couponBatchListMapper.selectById(couponBatchId);
        if (couponBatchList == null) {
            return false;
        }
        couponBatchList.setStatus(status);
        couponBatchList.setModifiedTime(LocalDateTime.now());
        couponBatchList.setModifier(adminTokenUtils.getModifier());
        couponBatchListMapper.updateById(couponBatchList);

        return true;
    }

    /**
     * 查询红包导出列表
     *
     * @param request
     * @return
     */
    @Override
    public List<CouponExportVo> exportCouponBatchList(QueryCouponBatchListRo request) {
        // 处理供应商id
        if (StringUtils.isNotBlank(request.getSupplierQuery()) && StringUtils.isNumeric(request.getSupplierQuery())) {
            request.setSupplierIdList(Collections.singletonList(Long.valueOf(request.getSupplierQuery())));
        } else if (StringUtils.isNotBlank(request.getSupplierQuery())) {
            LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
            ebkSupplierInfoQueryWrapper.like(WCEbkSupplierInfo::getEICSupplierName, request.getSupplierQuery());
            List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
            if (CollectionUtil.isEmpty(ebkSupplierInfoList)) {
                return null;
            }
            request.setSupplierIdList(ebkSupplierInfoList.stream().map(WCEbkSupplierInfo::getEICSupplierId).collect(Collectors.toList()));
        }
        // 查询
        List<CouponBatchListVo> couponBatchListVoList = Optional.ofNullable(couponBatchListMapper.pageSelectCouponList(new Page<>(1, -1), request)).map(IPage::getRecords).orElse(new ArrayList<>());
        if (CollectionUtil.isEmpty(couponBatchListVoList)) {
            return null;
        }


        List<Long> batchListIdList = couponBatchListVoList.stream().map(CouponBatchListVo::getId).collect(Collectors.toList());
        // 批次详情
        Map<Long, List<CouponBatchDetailDto>> couponBatchIdDetailMap = couponBatchDetailService.selectByCouponBatchId(batchListIdList);
        // 规则
        Map<Long, CouponBatchRuleDto> couponBatchIdRuleMap = couponBatchRuleService.selectByCouponBatchId(batchListIdList);

        // 供应商名称
        List<Long> supplierIdList = couponBatchListVoList.stream().filter(vo -> CouponBatchListTypeEnum.SUPPLIER.getCode().equals(vo.getType())).map(CouponBatchListVo::getSupplierId).collect(Collectors.toList());
        LambdaQueryWrapper<WCEbkSupplierInfo> ebkSupplierInfoQueryWrapper = new LambdaQueryWrapper<>();
        ebkSupplierInfoQueryWrapper.in(WCEbkSupplierInfo::getEICSupplierId, supplierIdList);
        ebkSupplierInfoQueryWrapper.eq(WCEbkSupplierInfo::getEICDataFlag, DataFlagEnum.Valid.getCode());
        List<WCEbkSupplierInfo> ebkSupplierInfoList = ebkSupplierInfoService.list(ebkSupplierInfoQueryWrapper);
        Map<Long, String> supplierIdNameMap = ebkSupplierInfoList.stream().collect(Collectors.toMap(WCEbkSupplierInfo::getEICSupplierId, WCEbkSupplierInfo::getEICSupplierName, (a, b) -> a));

        // 转成红包导出样式
        return couponBatchListVoList.stream().map(vo -> this.transformToExportVo(vo, couponBatchIdDetailMap.get(vo.getId()), couponBatchIdRuleMap.get(vo.getId()), supplierIdNameMap.get(vo.getSupplierId()))).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 获取所有优惠券批量关系资源规则dto
     *
     * @return {@link CouponBatchRelationResourceRuleDto }
     */
    @Override
    public List<CouponBatchRelationResourceRuleDto> getAllCouponBatchRelationResourceRuleDto() {
        String positionCodeListStr = ConfigUtils.get(ConfigConst.COUPON_DETAIL_POSITION_CODE);
        if (StrUtil.isBlank(positionCodeListStr)) {
            return Collections.emptyList();
        }
        List<String> positionCodeList = JSON.parseArray(positionCodeListStr, String.class);
        if (CollectionUtil.isEmpty(positionCodeList)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapper<CouponBatchList> joinWrapper = JoinWrappers.lambda(CouponBatchList.class)
                .innerJoin(CouponBatchDetail.class, CouponBatchDetail::getCouponBatchId, CouponBatchList::getId)
                .ge(CouponBatchDetail::getEventValidEndDate, LocalDateTime.now())
                .gt(CouponBatchDetail::getRemainStock, 0)
                .eq(CouponBatchList::getStatus, CouponStatusEnum.VALID.getCode())
                .eq(CouponBatchDetail::getStatus, CouponDetailStatusEnum.PASS.getCode())
                .eq(CouponBatchList::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .innerJoin(CouponBatchOperation.class, CouponBatchOperation::getCouponBatchId, CouponBatchList::getId)
                .in(CouponBatchOperation::getOperationPositionCode, positionCodeList)
                .groupBy(CouponBatchList::getId).distinct();
        List<CouponBatchList> couponBatchLists = couponBatchListMapper.selectJoinList(CouponBatchList.class, joinWrapper);
        if (CollectionUtil.isEmpty(couponBatchLists)) {
            SkyLogUtils.infoByMethod(log, "未查询到有效的红包批次", "", "");
            return Collections.emptyList();
        }
        SkyLogUtils.infoByMethod(log, StrUtil.format("查询到的红包批次:{}", JSON.toJSONString(couponBatchLists)), "", "");
        List<CouponBatchRelationResourceRuleDto> result = new CopyOnWriteArrayList<>();
        Map<Long, CouponBatchList> couponBatchListMap = couponBatchLists.stream().collect(Collectors.toMap(CouponBatchList::getId, Function.identity(), (a, b) -> a));
        Map<Long, CouponBatchRuleDto> couponBatchRuleDtoMap = couponBatchRuleService.selectByCouponBatchId(new ArrayList<>(couponBatchListMap.keySet()));
        if (CollectionUtil.isEmpty(couponBatchRuleDtoMap)) {
            SkyLogUtils.infoByMethod(log, "未查询到红包规则", "", "");
            return Collections.emptyList();
        }
        SkyLogUtils.infoByMethod(log, StrUtil.format("查询到的红包规则:{}", JSON.toJSONString(couponBatchRuleDtoMap)), "", "");
        couponBatchRuleDtoMap.values().parallelStream().forEach(couponBatchRuleDto -> {
            CouponBatchList couponBatchList = couponBatchListMap.get(couponBatchRuleDto.getCouponBatchId());
            if (ObjectUtil.isNull(couponBatchList)) {
                return;
            }
            //处理一级品类
            CouponBatchRelationResourceRuleDto batchRelationResourceRuleDto = new CouponBatchRelationResourceRuleDto();
            batchRelationResourceRuleDto.setSupplierId(couponBatchList.getSupplierId());
            batchRelationResourceRuleDto.getFirstCategoryList().add(couponBatchList.getProductCategoryId());
            result.add(batchRelationResourceRuleDto);
            //处理其他条件
            CouponRuleLimitTypeEnum ruleLimitTypeEnum = EnumUtil.getBy(CouponRuleLimitTypeEnum::getCode, couponBatchRuleDto.getLimitType());
            switch (ruleLimitTypeEnum) {
                case PRODUCT:
                    batchRelationResourceRuleDto.getResourceSerialIdList().addAll(couponBatchRuleDto.getDataIdList());
                    break;
                case DEPARTURE:
                    batchRelationResourceRuleDto.getDepartureList().addAll(couponBatchRuleDto.getDataIdList());
                    break;
                case CATEGORY:
                    batchRelationResourceRuleDto.getSecondCategoryList().addAll(couponBatchRuleDto.getDataIdList());
                    break;
                case ALL:
                    batchRelationResourceRuleDto.setAll(Boolean.TRUE);
                default:
                    break;
            }
        });
        SkyLogUtils.infoByMethod(log, StrUtil.format("查询到的红包批次关联资源规则:{}", JSON.toJSONString(result)), "", "");
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<CouponExportVo> transformToExportVo(CouponBatchListVo couponBatchListVo, List<CouponBatchDetailDto> couponBatchDetailList, CouponBatchRuleDto couponBatchRule, String supplierName) {
        if (couponBatchListVo == null) {
            return null;
        }
        List<CouponExportVo> couponExportVoList = new ArrayList<>();
        // 计算营销状态
        couponBatchListVo.setMarketingStatus(this.calcCouponBatchInfoMarketingStatus(couponBatchListVo.getStatus(), couponBatchDetailList));
        if (CollectionUtil.isNotEmpty(couponBatchDetailList)) {
            for (CouponBatchDetailDto couponBatchDetailDto : couponBatchDetailList) {
                couponExportVoList.add(transformSingleExportVo(couponBatchListVo, couponBatchDetailDto, couponBatchRule, supplierName));
            }
        } else {
            couponExportVoList.add(transformSingleExportVo(couponBatchListVo, null, couponBatchRule, supplierName));
        }

        return couponExportVoList;
    }

    private CouponExportVo transformSingleExportVo(CouponBatchListVo couponBatchListVo, CouponBatchDetailDto couponBatchDetail, CouponBatchRuleDto couponBatchRule, String supplierName) {
        CouponExportVo couponExportVo = new CouponExportVo();
        couponExportVo.setId(couponBatchListVo.getId());
        couponExportVo.setSupplierId(couponBatchListVo.getSupplierId());
        couponExportVo.setSupplierName(supplierName);
        CouponBatchListTypeEnum couponBatchListTypeEnum = CouponBatchListTypeEnum.getByCode(couponBatchListVo.getType());
        if (couponBatchListTypeEnum != null) {
            couponExportVo.setTypeName(couponBatchListTypeEnum.getDesc());
        }
        CouponMarketingStatusEnum couponMarketingStatusEnum = CouponMarketingStatusEnum.getByCode(couponBatchListVo.getMarketingStatus());
        if (couponMarketingStatusEnum != null) {
            couponExportVo.setMarketingStatusName(couponMarketingStatusEnum.getDesc());
        }

        if (couponBatchDetail != null) {
            couponExportVo.setBatchNo(couponBatchDetail.getBatchNo());
            couponExportVo.setBatchName(couponBatchDetail.getBatchName());
            couponExportVo.setBatchCname(couponBatchDetail.getBatchCname());
            CouponTypeEnum couponTypeEnum = CouponTypeEnum.getByCode(couponBatchDetail.getCouponType());
            if (couponTypeEnum != null) {
                couponExportVo.setCouponTypeName(couponTypeEnum.getDesc());
            }
            couponExportVo.setNotice(couponBatchDetail.getNotice());
            if (CollectionUtil.isNotEmpty(couponBatchDetail.getEventValidDateRange())) {
                couponExportVo.setEventValidDateRange(StringUtils.join("-", couponBatchDetail.getEventValidDateRange()));
            }
            if (couponBatchDetail.getRefundFlag() != null) {
                couponExportVo.setIsRefund(couponBatchDetail.getRefundFlag() == 0 ? "不可退" : "可退");
            }
            couponExportVo.setStock(couponBatchDetail.getStock());
            couponExportVo.setReceiveStock(couponBatchDetail.getReceiveStock());
            CouponBatchValidDateDto couponBatchValidDate = couponBatchDetail.getCouponBatchValidDate();
            if (couponBatchValidDate != null && ValidDateTypeEnum.LONG_TERM.getCode().equals(couponBatchValidDate.getValidDateType()) && CollectionUtil.isNotEmpty(couponBatchValidDate.getValidDateRange())) {
                couponExportVo.setValidDateRange(StringUtils.join("-", couponBatchValidDate.getValidDateRange()));
            } else if (couponBatchValidDate != null && ValidDateTypeEnum.SPECIFIED_PERIOD.getCode().equals(couponBatchValidDate.getValidDateType())) {
                String validDateRange = "";
                if (couponBatchValidDate.getDynamicStartDay() == 0) {
                    validDateRange += "当日";
                } else if (couponBatchValidDate.getDynamicStartDay() == 1) {
                    validDateRange += "当日";
                } else if (couponBatchValidDate.getDynamicStartDay() > 1) {
                    validDateRange += couponBatchValidDate.getDynamicDurationDay() + "天后";
                }
                validDateRange += "开始生效，有效时长：" + couponBatchValidDate.getDynamicDurationDay() + "天";
                couponExportVo.setValidDateRange(validDateRange);
            } else if (couponBatchValidDate != null && ValidDateTypeEnum.SPECIFIED_HOURS.getCode().equals(couponBatchValidDate.getValidDateType())) {
                couponExportVo.setValidDateRange("从领取开始生效，有效时长：" + couponBatchValidDate.getCountdownHour() + "小时");
            } else if (couponBatchValidDate != null && ValidDateTypeEnum.SPECIFIED_MINUTES.getCode().equals(couponBatchValidDate.getValidDateType())) {
                couponExportVo.setValidDateRange("从领取开始生效，有效时长：" + couponBatchValidDate.getCountdownMinute() + "分钟");
            }

            if (couponBatchRule != null) {
                if (CouponRuleLimitTypeEnum.ALL.getCode().equals(couponBatchRule.getLimitType())) {
                    couponExportVo.setProductRange(CouponBatchListTypeEnum.PLATFORM.getCode().equals(couponBatchListVo.getType()) ? "所有产品" : "该商家下所有产品");
                } else if (CouponRuleLimitTypeEnum.PRODUCT.getCode().equals(couponBatchRule.getLimitType())) {
                    couponExportVo.setProductRange("指定产品: " + StringUtils.join(",", couponBatchRule.getDataIdList()));
                } else if (CouponRuleLimitTypeEnum.CATEGORY.getCode().equals(couponBatchRule.getLimitType())) {
                    couponExportVo.setProductRange("指定分类: " + StringUtils.join(",", couponBatchRule.getDataIdList()));
                } else if (CouponRuleLimitTypeEnum.DEPARTURE.getCode().equals(couponBatchRule.getLimitType())) {
                    couponExportVo.setProductRange("指定出发城市: " + StringUtils.join(",", couponBatchRule.getDataIdList()));
                }
            }
            if (CollectionUtil.isNotEmpty(couponBatchDetail.getPlatformList())) {
                couponExportVo.setPlatForm(StringUtils.join(",", couponBatchDetail.getPlatformList().stream().map(PlatformVo::getName).collect(Collectors.toList())));
            }
        }
        return couponExportVo;
    }

}
