package com.ly.localactivity.marketing.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.utils.ConfigUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.common.bean.EsSearchResponse;
import com.ly.localactivity.marketing.common.bean.EsSearchResult;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.external.coupon.service.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EsSearchUtils
 *
 * <AUTHOR>
 * @date 2024/5/21
 */
@Slf4j
public class EsSearchUtils {
    private static final String address = "es.dss.17usoft.com";

    /**
     * 保存对象
     *
     * @param index
     * @param objects
     * @param env     环境
     * @return
     */
    public static boolean putObject(EsIndex index, List<?> objects, String env) {
        if (CollectionUtil.isEmpty(objects)) {
            return false;
        }

        if (StringUtils.isBlank(env)) {
            env = SpringUtil.getActiveProfile();
        }
        String indexName = index.getName() + "-" + env;

        String url = MessageFormat.format("http://{0}/index/{1}/type/info/bulk", address, indexName);
        try {
            doPost(url, JSON.toJSONString(objects), index.getToken());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 模板搜索
     *
     * @param index
     * @param template
     * @param param
     * @return
     */
    public static <T> EsSearchResult<T> searchTemplete(EsIndex index, EsTemplate template, Object param) {
        if (template == null) {
            return null;
        }
        String version = getTemplateVersion(template);
        String indexName = index.getName() + "-" + SpringUtil.getActiveProfile();
        String url = MessageFormat.format("http://{0}/index/{1}/template/{2}/{3}/search", address, indexName, template.getName(), version);

        try {
            long start = System.currentTimeMillis();

            String res = doPost(url, JSON.toJSONString(param), index.getToken());
            SkyLogUtils.infoByMethod(log, StringUtils.format("es搜索：url:{},cost:{}ms,req:{},res:{}", url, (System.currentTimeMillis() - start), JSON.toJSONString(param), res), "", "");
//            log.info("es搜索：url:{},req:{},res:{}", url, JSON.toJSONString(param), res);
            if (StringUtils.isBlank(res)) {
                return null;
            }

            EsSearchResponse<T> response = JSON.parseObject(res, new TypeReference<EsSearchResponse<T>>() {
            });
            if (response != null) {
                return response.getResult();
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    private static String doPost(String url, String params, String token) throws Exception {
        String version = com.ly.localactivity.marketing.common.utils.ConfigUtils.get(ConfigConst.COUPON_API_VERSION);
        if (StringUtils.isNotBlank(version) && "2".equals(version)) {
            return doClientUtilPost(url, params, token);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        headers.put("Authentication", token);

        String result = HttpSender.create()
                .typeCode("couponCoreService_getCouponList")
                .url(url)
                .headers(headers)
                .body(JSON.parse(params))
                .doPost();


        return result;
//        CloseableHttpClient httpclient = HttpClients.createDefault();
//        HttpPost httpPost = new HttpPost(url);// 创建httpPost
//        httpPost.setHeader("Accept", "application/json");
//        httpPost.setHeader("Content-Type", "application/json");
//        httpPost.setHeader("Authentication", token);
//        String charSet = "UTF-8";
//        StringEntity entity = new StringEntity(params, charSet);
//        httpPost.setEntity(entity);
//        CloseableHttpResponse response = null;
//
//        try {
//            response = httpclient.execute(httpPost);
//
//            StatusLine status = response.getStatusLine();
//            int state = status.getStatusCode();
//            if (HttpStatus.SC_OK == state) {
//                HttpEntity responseEntity = response.getEntity();
//                String jsonString = EntityUtils.toString(responseEntity);
//                return jsonString;
//            } else {
//                //logger.error("请求返回:"+state+"("+url+")");
//            }
//        } finally {
//            if (response != null) {
//                try {
//                    response.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            try {
//                httpclient.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return null;
    }

    private static String doClientUtilPost(String url, String params, String token) throws Exception {

//        CloseableHttpClient httpclient = HttpClients.createDefault();
//        HttpPost httpPost = new HttpPost(url);// 创建httpPost
//        httpPost.setHeader("Accept", "application/json");
//        httpPost.setHeader("Content-Type", "application/json");
//        httpPost.setHeader("Authentication", token);
//        String charSet = "UTF-8";
//        StringEntity entity = new StringEntity(params, charSet);
//        httpPost.setEntity(entity);
//        CloseableHttpResponse response = null;
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        headers.put("Authentication", token);

        try {
            String timeOutJsonStr = com.ly.localactivity.marketing.common.utils.ConfigUtils.get(ConfigConst.HTTP_CLIENT_RETRY_TIME_OUT);
            int retryTimeOut = 55;
            if (StringUtils.isNotEmpty(timeOutJsonStr)) {
                JSONObject jsonObject = JSON.parseObject(timeOutJsonStr);
                Integer timeOut = jsonObject.getInteger("ES_URL");
                if (timeOut != null) {
                    retryTimeOut = timeOut;
                }
            }
//            response = httpclient.execute(httpPost);
            String jsonString = HttpClientUtil.postAndRetry(url, params, headers,1, retryTimeOut, null);
            if(StringUtils.isNotEmpty(jsonString)){
                return jsonString;
            }
//            StatusLine status = response.getStatusLine();
//            int state = status.getStatusCode();
//            if (state == HttpStatus.SC_OK) {
//                HttpEntity responseEntity = response.getEntity();
//                String jsonString = EntityUtils.toString(responseEntity);
//                return jsonString;
//            } else {
//                //logger.error("请求返回:"+state+"("+url+")");
//            }
        } finally {
//            if (response != null) {
//                try {
//                    response.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            try {
//                httpclient.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
        }
        return null;
    }

    /**
     * 获取模板版本
     *
     * @param template 模板
     * @return {@link String}
     */
    private static String getTemplateVersion(EsTemplate template) {
        String str = ConfigUtils.get(ConfigConst.ES_TEMPLATE_CONFIG, "");
        if (StringUtils.isBlank(str)) {
            return template.getVersion();
        }
        Map<String, String> map = JSON.parseObject(str, new TypeReference<Map<String, String>>() {
        });
        if (MapUtil.isEmpty(map) || !map.containsKey(template.getName())) {
            return template.getVersion();
        }
        return map.get(template.getName());
    }

    public enum EsIndex {
        CouponBatchList("tczb-marketing-coupon-batch", "75716bd9-c449-45d5-b471-0bef74fc788a"),
        ;

        private String name;
        private String token;

        private EsIndex(String name, String token) {
            this.name = name;
            this.token = token;
        }

        public String getName() {
            return name;
        }

        public String getToken() {
            return token;
        }
    }

    public enum EsTemplate {
        Coupon_Search_General("coupon_search_general", "1.1.2"),
        Coupon_Search_Batchno("coupon_search_batchno", "1.0.0"),
        ;

        //成员变量
        private String name;
        private String version;

        private EsTemplate(String name, String version) {
            this.name = name;
            this.version = version;
        }

        public String getName() {
            return name;
        }

        public String getVersion() {
            return version;
        }
    }
}
