package com.ly.localactivity.marketing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * CouponConsumeStatusEnum
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@Getter
@AllArgsConstructor
public enum CouponConsumeStatusEnum {
    /**
     * 已核销
     */
    Consumed(1, "已核销"),
    /**
     * 未核销
     */
    Unconsumed(2, "未核销"),
    ;
    private Integer code;
    private String name;
}
