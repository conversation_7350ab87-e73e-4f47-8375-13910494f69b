
# 日志配置
logging:
  level:
    root: info
    org.springframework.boot.autoconfigure: error
    #    com.baomidou.mybatisplus: debug
    com.ly.localactivity.openapi.mapper: debug
  file:
    path: /data/logs

local-activity:
  apiDoc:
    enabled: false
  testApi:
    enabled: false
  config:
    turbomq:
      enabled: true
      namesrvAddr: mqnameserver.17usoft.com:9876;mqnameserverbak.17usoft.com:9876
      producerGroups:
        - tcla_consumer_group_lamarketing_admin_default
    redis:
      enabled: true
      groupName: redis.group.tcscenery.localactivity.marketing.api
      prefix: tclami
      env: product




