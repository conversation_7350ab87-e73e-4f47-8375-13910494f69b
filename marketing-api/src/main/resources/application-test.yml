
# 日志配置
logging:
  level:
    root: info
    org.springframework.boot.autoconfigure: error
    #    com.baomidou.mybatisplus: debug
    com.ly.localactivity.openapi.mapper: debug
  file:
    path: /data/logs

home:
  url: wx.test.17u.cn/travelaroundapi

local-activity:
  apiDoc:
    enabled: true
  testApi:
    enabled: true
  config:
    turbomq:
      enabled: true
      namesrvAddr: mqnameserver.qa.17usoft.com:9876
      producerGroups:
        - tcla_consumer_group_lamarketing_admin_default
    redis:
      enabled: true
      groupName: redis.group.tcscenery.localactivity.marketing.api
      prefix: tclami
      env: test



