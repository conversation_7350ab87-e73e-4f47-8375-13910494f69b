<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="logPath"  source="logging.file.path"/>
    <appender name="TCSCENERY-JAVA-LOCALACTIVITY-EXTERNAL-API-LOG-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--        <file>~/data/logs/skynet-tcscenery.java.localactivity.external.api/app/tcscenery.java.localactivity.external.api.log</file>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${logPath}/skynet-tcscenery.java.localactivity.marketing.api/app/tcscenery.java.localactivity.marketing.api.%d{yyyy-MM-dd}.log
                <!--                ~/data/logs/skynet-tcscenery.java.localactivity.external.api/skynet_archived/tcscenery.java.localactivity.external.api.%d{yyyy-MM-dd}.log-->
            </fileNamePattern>
            <maxHistory>6</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{apmTrace} %-5level %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- checklist appe   -->
    <appender name="checklistAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>
                ${logPath}/skynet-tcscenery.java.localactivity.marketing.api/checklist/checklist.%d{yyyy-MM-dd-HH-mm}.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <pattern>%m%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC-INFO" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>500000</queueSize>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="TCSCENERY-JAVA-LOCALACTIVITY-EXTERNAL-API-LOG-FILE"/>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CHECKLIST-ASYNC-INFO" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>500000</queueSize>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="checklistAppender"/>
    </appender>

    <springProfile name="dev">
        <root level="INFO" >
            <!--        测试使用 不要推送到线上环境-->
            <appender-ref ref="STDOUT" />
            <appender-ref ref="ASYNC-INFO"/>
        </root>
    </springProfile>
    <springProfile name="!dev">
        <root level="INFO" >
            <appender-ref ref="ASYNC-INFO"/>
        </root>
    </springProfile>

    <logger name="com.ly.dal" level="ERROR"/>
    <logger name="com.ly.spat.dsf.monitor" level="DEBUG"/>
    <logger name="com.ly.spat.dsf.client" level="DEBUG"/>
    <logger name="com.ly.spat.dsf.utils.NetUtils" level="DEBUG"/>

    <!-- 使用checklist-client包中的配置的category ,防止不必要日志日志输出-->
    <logger name="checklistLogEntityLogger" level="INFO" additivity="false">
        <appender-ref ref="CHECKLIST-ASYNC-INFO"/>
    </logger>

    <!--    <logger name="checklistLogEntityLogger" level="INFO" additivity="false">-->
    <!--        <appender-ref ref="CHECKLIST-ASYNC-INFO"/>-->
    <!--    </logger>-->

    <!--    <logger name="com.ly.wl.zby.mapper.markting" level="INFO" additivity="false">-->
    <!--        <appender-ref ref="STDOUT"/>-->
    <!--    </logger>-->
    <!--    <logger name="com.ly.wl.zby.mapper.base" level="INFO" additivity="false">-->
    <!--        <appender-ref ref="STDOUT"/>-->
    <!--    </logger>-->
</configuration>
