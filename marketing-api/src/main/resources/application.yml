spring:
  profiles:
    active: @activatedProperties@
    include:
      - @activatedProperties@-api

server:
  tomcat:
    uri-encoding: UTF-8
    connection-timeout: 180000
    # 连接数满后的排队数
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # tomcat启动初始化的线程数
      min-spare: 100
    max-connections: 10000
  servlet:
    context-path: /localactivity-marketingapi
    encoding:
      charset: utf-8
      force: true
  port: 8080
  compression:
    enabled: true
    mime-types: application/json


# mybatis
mybatis-plus:
#  mapper-locations: classpath*:/mapper/**/*.xml
#  #实体扫描，多个package用逗号或者分号分隔
#  typeAliasesPackage: com.ly.localactivity.*.domain
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: false
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      logic-delete-field: deleteFlag  # 全局逻辑删除的实体字段名
    banner: false
  #原生配置
  configuration:
#    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


local-activity:
  appName: tcscenery.java.localactivity.marketing.api
  fastjson-converter:
    enabled: true
  config:
    redis:
      enabled: false
      groupName: redis.group.tcscenery.localactivity.marketing.api
      prefix: tclam
    dal:
      className:
        mysql: com.mysql.jdbc.Driver
        sqlServer: com.microsoft.sqlserver.jdbc.SQLServerDriver
      TCZBActivityMarketing:
        enabled: true
        name: TCZBActivityMarketing
        mapperLocation: classpath*:mapper/marketing/*.xml
        basePackages: com.ly.localactivity.marketing.mapper.marketing
        transactionManager: true
      TCZBActivityCommon:
        enabled: true
        name: TCZBActivityCommon
        mapperLocation: classpath*:mapper/common/*.xml
        basePackages: com.ly.localactivity.marketing.mapper.common
        transactionManager: true
      TCZBActivityLog:
        enabled: true
        name: TCZBActivityLog
        mapperLocation: classpath*:mapper/log/*.xml
        basePackages: com.ly.localactivity.marketing.mapper.log
        transactionManager: true
      TCZBActivityResource:
        enabled: true
        name: TCZBActivityResource
        mapperLocation: classpath*:mapper/resource/**/*.xml
        basePackages: com.ly.localactivity.marketing.mapper.resource
        transactionManager: false

