<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd


http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <bean id="serviceAspect" class="com.ly.localactivity.marketing.common.interceptor.ChecklistLogAspect"/>
    <aop:config>
        <aop:pointcut id="si"
                      expression="@annotation(com.ly.localactivity.framework.adapter.CheckListLog) or @annotation(com.elong.hotel.annotation.DaoLog) or @annotation(com.elong.hotel.annotation.ServiceLog) "/>
        <!-- <aop:pointcut id="si" expression="within(@org.springframework.web.bind.annotation.ResponseBody * ) "/>-->
        <aop:advisor pointcut-ref="si" advice-ref="serviceAspect"/>
    </aop:config>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>