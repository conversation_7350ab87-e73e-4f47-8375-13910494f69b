package com.ly.localactivity;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * MiddlewareApplication
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
//@ComponentScan({"com.ly.localactivity", "com.ly.localactivity"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ImportResource("classpath:checklist.xml")
@EnableTransactionManagement
public class ApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
    }
}
