//package com.ly.localactivity;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeansException;
//import org.springframework.beans.factory.SmartInitializingSingleton;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.stereotype.Component;
//
///**
// * TestBean
// *
// * <AUTHOR>
// * @date 2024/1/15
// */
//@Slf4j
//@Component
//public class TestBean implements ApplicationContextAware, SmartInitializingSingleton {
//    @Override
//    public void afterSingletonsInstantiated() {
//        log.info("TestBean afterSingletonsInstantiated");
//    }
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        log.info("TestBean setApplicationContext");
//    }
//}
