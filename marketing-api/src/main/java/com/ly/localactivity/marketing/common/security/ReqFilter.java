package com.ly.localactivity.marketing.common.security;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.common.CommonConstant;
import com.ly.localactivity.framework.enums.HttpMethodEnum;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.model.api.ResultCode;
import com.ly.localactivity.framework.utils.http.HttpUtils;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.SignUtils;
import com.ly.localactivity.marketing.common.constants.ConfigConst;
import com.ly.localactivity.marketing.common.utils.ConfigUtils;
import com.ly.localactivity.marketing.common.utils.SpringUtil;
import com.ly.localactivity.marketing.domain.base.SysAppDto;
import com.ly.localactivity.marketing.service.marketing.ISysAppListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * ReqFilter
 *
 * @date 2023/12/19
 */
@Slf4j
@Order(1)
@WebFilter(
        filterName = "reqFilter",
        urlPatterns = {"/*"}
)
@Component
public class ReqFilter extends OncePerRequestFilter {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ISysAppListService sysAppListService;

    private final Set<String> ALLOWED_PATHS = new HashSet<>();

    @PostConstruct
    public void init() {
        ALLOWED_PATHS.addAll(Arrays.asList("/health", "/job/**","/operationPromotion/refresh"));

        //非生产环境放行其它接口
        String activeProfile = SpringUtil.getActiveProfile();
        if (StringUtils.isNotBlank(activeProfile) && !activeProfile.equals("product")) {
            ALLOWED_PATHS.addAll(Arrays.asList("/doc.html", "/swagger-resources/**"
                    , "/v2/api-docs", "/webjars/**", "/test/**"));
        }
    }


    /**
     * 签名验证时间（TIMES =分钟 * 秒 * 毫秒）
     * 当前设置为：5分钟有效期
     */
    private static final Integer TIMES = 5 * 60 * 1000;

    /**
     * 请求id redis时间(秒)
     */
    private static final Integer REQUEST_ID_REDIS_TIME = 10 * 60;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {

        // 排除放行url
        String path = request.getRequestURI().substring(request.getContextPath().length()).replaceAll("[/]+$", "");

        if (matches(path, ALLOWED_PATHS)) {
            chain.doFilter(request, response);
            return;
        }

        String requestId = "";
        String appKey = "";
        JSONObject jsonObject = null;
        ServletRequest requestWrapper = null;
        // 判断请求方式
        String method = request.getMethod();
        if (HttpMethodEnum.POST.name().equals(method)) {
            // 获取请求Body参数，需要使用 BodyReaderHttpServletRequestWrapper进行处理，否则在后面的流中获取不到参数
            requestWrapper = new BodyReaderHttpServletRequestWrapper(request);
            String body = HttpUtils.getBodyString(requestWrapper);
            if (StringUtils.isEmpty(body)) {
                writeResponse(response, CommonResult.failed(ResultCode.SIGN_EMPTY));
                return;
            }
            // 解析参数转JSON格式
            jsonObject = JSONObject.parseObject(body);

        }
        if (HttpMethodEnum.GET.name().equals(method)) {
            // 获取请求参数
            Map<String, String> allRequestParam = getAllRequestParam(request);
            Set<Map.Entry<String, String>> entries = allRequestParam.entrySet();
            // 参数转JSON格式
            final JSONObject jo = new JSONObject();
            entries.forEach(key -> {
                jo.put(key.getKey(), key.getValue());
            });
            jsonObject = jo;
        }

        // 验签
        boolean validation = validSign(jsonObject, response);
        if (!validation) {
            return;
        }

        //将appkey和requestId放入request中
        appKey = jsonObject.getString(CommonConstant.REQUEST_APP_ID);
        requestId = jsonObject.getString(CommonConstant.REQUEST_ID);

        //验签通过放行
        if (HttpMethodEnum.POST.name().equals(method)) {
            requestWrapper.setAttribute(CommonConstant.REQUEST_ID, requestId);
            requestWrapper.setAttribute(CommonConstant.REQUEST_APP_ID, appKey);
            chain.doFilter(requestWrapper, response);
        } else {
            request.setAttribute(CommonConstant.REQUEST_ID, requestId);
            request.setAttribute(CommonConstant.REQUEST_APP_ID, appKey);
            chain.doFilter(request, response);
        }

    }

    /**
     * 有效请求id校验，防重放
     *
     * @param requestId 请求id
     * @return boolean
     */
    private boolean validRequestId(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            return true;
        }
        String key = "vrq_" + requestId;
        if (redisUtils.exists(key)) {
            return false;
        }
        redisUtils.setnx(key, requestId, REQUEST_ID_REDIS_TIME);
        return true;
    }

    /**
     * 验签
     *
     * @param body     请求参数
     * @param response
     * @return
     * @throws IOException
     */
    private boolean validSign(JSONObject body, HttpServletResponse response) throws IOException {
        if (body == null) {
            return false;
        }
        // 拿出请求签名
        String requestId = body.getString(CommonConstant.REQUEST_ID);
        String appKey = body.getString(CommonConstant.REQUEST_APP_ID);
        String sign = body.getString(CommonConstant.REQUEST_SIGN);
        Long timestamp = body.getLong(CommonConstant.REQUEST_TIMESTAMP);

        //测试签名校验
        String testSign = ConfigUtils.get(ConfigConst.Api_Test_Sign);
        if (StringUtils.isNotBlank(testSign) && testSign.equals(sign)) {
            return true;
        }

        //参数为空校验
        if (StringUtils.isBlank(appKey)
                || StringUtils.isBlank(requestId)
                || StringUtils.isBlank(sign)
                || timestamp == null
                || timestamp == 0) {
            writeResponse(response, CommonResult.failed(ResultCode.SIGN_EMPTY));
            return false;
        }

        //重放校验
        if (!validRequestId(requestId)) {
            writeResponse(response, CommonResult.failed(ResultCode.REPEATABLE_REQUEST));
            return false;
        }

        // 校验签名是否失效
        long thisTime = System.currentTimeMillis() - timestamp;
        if (thisTime > TIMES) {
            // 比对时间是否失效
            writeResponse(response, CommonResult.failed(ResultCode.TIMESTAMP_FAILED));
            return false;
        }

        //取appSecret
        SysAppDto appDto = sysAppListService.getByAppId(appKey);
        if (appDto == null || StringUtils.isBlank(appDto.getAppSecret())) {
            writeResponse(response, CommonResult.failed(ResultCode.SIGN_FAILED));
            return false;
        }
        String appSecret = appDto.getAppSecret();

        body.remove("sign");
        // 根据APPID查询的密钥进行重签
        String sign1 = SignUtils.getSign(appSecret, body);

        // 校验签名
        if (!StringUtils.equals(sign1, sign)) {
            // APPID查询的密钥进行签名 和 用户签名进行比对
            writeResponse(response, CommonResult.failed(ResultCode.SIGN_FAILED));
            return false;
        }

        return true;
    }

    private static void writeResponse(HttpServletResponse response, CommonResult result) throws IOException {
        response.setCharacterEncoding("utf-8");
        PrintWriter writer = response.getWriter();
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        writer.print(JSON.toJSONString(result));
        writer.close();
    }

    /**
     * 获取客户端GET请求中所有的请求参数
     *
     * @param request
     * @return
     */
    private Map<String, String> getAllRequestParam(final HttpServletRequest request) {
        Map<String, String> res = new HashMap<String, String>();
        Enumeration<?> temp = request.getParameterNames();
        if (null != temp) {
            while (temp.hasMoreElements()) {
                String en = (String) temp.nextElement();
                String value = request.getParameter(en);
                res.put(en, value);
                //如果字段的值为空，判断若值为空，则删除这个字段>
                if (null == res.get(en) || "".equals(res.get(en))) {
                    res.remove(en);
                }
            }
        }
        return res;
    }

    /**
     * 判断指定url地址是否匹配指定url集合中的任意一个
     *
     * @param urlPath 指定url地址
     * @param urls    需要检查的url集合
     * @return 是否匹配  匹配返回true，不匹配返回false
     */
    public static boolean matches(String urlPath, Set<String> urls) {
        if (StringUtils.isEmpty(urlPath) || CollectionUtil.isEmpty(urls)) {
            return false;
        }
        for (String url : urls) {
            if (isMatch(url, urlPath)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 判断url是否与规则配置:
     * ? 表示单个字符
     * * 表示一层路径内的任意字符串，不可跨层级
     * ** 表示任意层路径
     *
     * @param url     匹配规则
     * @param urlPath 需要匹配的url
     * @return
     */
    public static boolean isMatch(String url, String urlPath) {
        AntPathMatcher matcher = new AntPathMatcher();
        return matcher.match(url, urlPath);
    }

}
