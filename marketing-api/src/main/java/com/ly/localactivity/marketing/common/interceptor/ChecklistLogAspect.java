package com.ly.localactivity.marketing.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.elong.hotel.annotation.MApiController;
import com.elong.hotel.core.ChecklistLogInterceptor;
import com.elong.hotel.core.Interceptor;
import com.elong.hotel.entity.ServiceResponseBase;
import com.elong.hotel.entity.ServiceResponseBaseForMapi;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 注解方法拦截切面
 * @create 2017-12-19 14:50:20
 * <AUTHOR>
 * @since 1.0.0
 */
public class ChecklistLogAspect implements InitializingBean, MethodInterceptor {

	/**
	 * 拦截器实例
	 */
	private Interceptor interceptor;
	
	/**
	 * spring容器初始化bean后执行方法
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		interceptor = new ChecklistLogInterceptor();
	}
	
	/**
	 * 方法调用
	 */
	@Override
	public Object invoke(MethodInvocation mi) throws Throwable {
		Object result = initResult(mi);
		interceptor.preProcess(mi);
		try {
			result = mi.proceed();
		} catch (Throwable e) {
			interceptor.afterThrowing(mi, result, e);
			throw e;
		} finally {
			interceptor.postProcess(mi, result);
		}
		
		// 判断如果为HTTPAPI，则返回JSON
		if(result instanceof ServiceResponseBase) {
			return JSON.toJSONString(result);
		}
		
		if (result instanceof ServiceResponseBaseForMapi) {
		  return JSON.toJSONString(result);
		}
		
		return result;
	}
	
	/**
	 * 初始化返回值
	 * @param mi
	 * 方法代理
	 * @return
	 * 返回值
	 */
	private Object initResult(MethodInvocation mi){
		Object ret = null;
		try {
			if(mi.getMethod().isAnnotationPresent(ResponseBody.class)) {
			  ret = new ServiceResponseBase(); 
			  if (mi.getMethod().getDeclaringClass().isAnnotationPresent(MApiController.class)) {
			    ret = new ServiceResponseBaseForMapi();
			  }
			}else if(mi.getMethod().getReturnType().getName().equals(ModelAndView.class.getName())) {
				String mv = "error";
				ret = new ModelAndView(mv);
			}
		} catch (Exception e) {

		}
		return ret;
	}

}
