package com.ly.localactivity.marketing.common.security;

import com.ly.localactivity.framework.common.CommonConstant;
import com.ly.localactivity.framework.model.api.CommonResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * ResponseAdvice
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Component
@ControllerAdvice(basePackages = {"com.ly.localactivity.marketing.controller"})
public class ApiResultIntercepter implements ResponseBodyAdvice<Object> {

    @Resource
    private HttpServletRequest request;

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter parameter,
                                  MediaType mediaType, Class c, ServerHttpRequest serverHttpRequest,
                                  ServerHttpResponse serverHttpResponse) {
        if (body == null) {
            body = CommonResult.failed();
        }
        if (body instanceof CommonResult) {
            CommonResult result = (CommonResult) body;
            result.setRequestId(request.getAttribute(CommonConstant.REQUEST_ID) != null ? request.getAttribute(CommonConstant.REQUEST_ID).toString() : "");
            return result;
        }

        return body;
    }

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }
}
