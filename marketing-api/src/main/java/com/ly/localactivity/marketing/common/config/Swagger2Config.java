package com.ly.localactivity.marketing.common.config;


import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyName;
import com.fasterxml.jackson.databind.introspect.Annotated;
import com.fasterxml.jackson.databind.introspect.JacksonAnnotationIntrospector;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.configuration.ObjectMapperConfigured;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebFlux;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import javax.annotation.PostConstruct;
import java.lang.annotation.Annotation;

/**
 * Swagger2Config
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@ConditionalOnProperty(prefix = "local-activity.apiDoc", name = "enabled", havingValue = "true")
@Configuration
@EnableKnife4j
@EnableSwagger2WebMvc
@EnableSwagger2WebFlux
public class Swagger2Config {

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${local-activity.apiDoc.enabled}")
    private Boolean isEnable = true;

    /**
     * 设置对象映射器，解决将fastjson2作为序列化时取不到model
     */
    @PostConstruct
    public void setObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        objectMapper.registerModule(module);
        JacksonAnnotationIntrospector jacksonAnnotationIntrospector=  new JacksonAnnotationIntrospector();
        objectMapper.setAnnotationIntrospector(new JacksonAnnotationIntrospector() {
            @Override
            public boolean isAnnotationBundle(Annotation ann) {
                if (ann.annotationType() == JSONField.class) {
                    return true;
                }
                return super.isAnnotationBundle(ann);
            }
            @Override
            public PropertyName findNameForSerialization(Annotated a) {
                PropertyName nameForSerialization = super.findNameForSerialization(a);
                if (nameForSerialization == null || nameForSerialization == PropertyName.USE_DEFAULT) {
                    JSONField jsonField = _findAnnotation(a, JSONField.class);
                    if (jsonField != null) {
                        return PropertyName.construct(jsonField.name());
                    }
                }
                return nameForSerialization;
            }
            @Override
            public PropertyName findNameForDeserialization(Annotated a) {
                PropertyName nameForDeserialization = super.findNameForDeserialization(a);
                if (nameForDeserialization == null || nameForDeserialization == PropertyName.USE_DEFAULT) {
                    JSONField jsonField = _findAnnotation(a, JSONField.class);
                    if (jsonField != null) {
                        return PropertyName.construct(jsonField.name());
                    }
                }
                return nameForDeserialization;
            }
        });
        ObjectMapperConfigured objectMapperConfigured = new ObjectMapperConfigured(applicationContext, objectMapper);
        applicationContext.publishEvent(objectMapperConfigured);
    }

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(isEnable)
                .apiInfo(this.apiInfo())
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build()
                //.globalRequestParameters(globalRequestParameters())
                ;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("marketing-api")
                .description("周边玩乐营销平台api文档")
                .termsOfServiceUrl("http://localhost:8081/")
                .contact(new Contact("周边玩乐研发组","",""))
                .version("1.0")
                .build();
    }

//    /**
//     * 请求头配置
//     * @return
//     */
//    private List<RequestParameter> globalRequestParameters() {
//        RequestParameterBuilder parameterBuilder = new RequestParameterBuilder()
//                .in(ParameterType.HEADER).name("Authorization").required(true)
//                .query(param -> param.model(model -> model.scalarModel(ScalarType.STRING)));
//        return Collections.singletonList(parameterBuilder.build());
//    }


}
