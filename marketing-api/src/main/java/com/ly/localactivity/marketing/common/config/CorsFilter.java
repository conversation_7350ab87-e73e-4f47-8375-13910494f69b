//package com.ly.localactivity.marketing.common.config;
//
//import javax.servlet.*;
//import javax.servlet.FilterConfig;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
///**
// * CorsFilter
// *
// * <AUTHOR>
// * @date 2024/1/17
// */
//public class CorsFilter implements Filter {
//    @Override
//    public void doFilter(ServletRequest ro, ServletResponse dto, FilterChain chain)
//            throws IOException, ServletException {
//        HttpServletResponse res = (HttpServletResponse) dto;
//
//        res.setHeader("Access-Control-Allow-Origin", "*");
//        res.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
//        res.setHeader("Access-Control-Max-Age", "3600");
//        res.setHeader("Access-Control-Allow-Headers", "x-requested-with, Content-Type, Accept, Origin");
//        res.setHeader("Access-Control-Allow-Credentials", "true");
//
//        chain.doFilter(ro, dto);
//    }
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//    }
//
//    @Override
//    public void destroy() {
//    }
//}
