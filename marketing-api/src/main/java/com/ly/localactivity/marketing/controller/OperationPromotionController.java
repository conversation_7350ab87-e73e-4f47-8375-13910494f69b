package com.ly.localactivity.marketing.controller;

import cn.hutool.core.util.StrUtil;
import com.ly.localactivity.framework.adapter.CheckListLog;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.marketing.application.IOperationPromotionApp;
import com.ly.localactivity.marketing.application.model.ro.PromotionPlanApiRo;
import com.ly.localactivity.marketing.application.model.vo.OperationPositionApiVo;
import com.ly.localactivity.marketing.common.constants.RedisConstants;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * OperationPromotionController
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@Api(tags = "运营推广")
@RestController
@RequestMapping("/operationPromotion")
@Slf4j
public class OperationPromotionController {

    @Resource
    private IOperationPromotionApp operationPromotionApp;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisUtils redisUtils;

    @CheckListLog
    @ApiOperation(value = "获取推广计划")
    @PostMapping("/getOperationPositionApiVoList")
    public CommonResult<List<OperationPositionApiVo>> getOperationPositionApiVoList(@Valid @RequestBody PromotionPlanApiRo request) {
        Long cacheVersion = Optional.ofNullable(redisUtils.get(RedisConstants.GET_OPERATION_POSITION_API_VO_CACHE_VERSION_KEY_PREFIX)).filter(StrUtil::isNotBlank).map(Long::valueOf).orElse(0L);
        request.setCacheVersion(String.valueOf(cacheVersion));
        return CommonResult.success(operationPromotionApp.getOperationPositionApiVoList(request));
    }


    @ApiOperation(value = "刷新缓存")
    @PostMapping("/refresh")
    public CommonResult<String> refresh() {
        RLock lock = redissonClient.getLock(RedisConstants.OPERATION_CACHE_REFRESH_LIMIT_KEY);
        try {
            // 尝试获取锁，等待时间为 0 秒，持有锁的时间为 20 秒
            boolean lockFlag = lock.tryLock(0, 20, TimeUnit.SECONDS);
            if (lockFlag) {
                // 如果成功获取锁，执行缓存刷新操作
                long cacheVersion = (redisUtils.incr(RedisConstants.GET_OPERATION_POSITION_API_VO_CACHE_VERSION_KEY_PREFIX));
                SkyLogUtils.infoByMethod(log, StrUtil.format("refresh success key:{}", cacheVersion), "refresh", "");
                return CommonResult.success("success");
            } else {
                return CommonResult.failed("20秒内只能刷新一次，请稍后再试");
            }
        } catch (InterruptedException e) {
            return CommonResult.failed("Failed to acquire lock. Please try again later.");
        }
    }

}
