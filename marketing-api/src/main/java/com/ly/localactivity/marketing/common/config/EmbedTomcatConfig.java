package com.ly.localactivity.marketing.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.ProtocolHandler;
import org.apache.coyote.http11.AbstractHttp11Protocol;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * EmbedTomcatConfig
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@Slf4j
@Configuration
public class EmbedTomcatConfig {
    @Bean
    WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> tomcatSetProtocol(){
        return factory -> {
            ((TomcatServletWebServerFactory) factory).addConnectorCustomizers(connector -> {
                ProtocolHandler protocol = connector.getProtocolHandler();
                String res=String.format("Tomcat config(%s)  -- MaxConnection:%s;MaxThreads:%s;MinSpareThreads:%s",                         protocol.getClass().getName(),
                        ((AbstractHttp11Protocol<?>) protocol).getMaxConnections(),
                        ((AbstractHttp11Protocol<?>) protocol).getMaxThreads(),
                        ((AbstractHttp11Protocol<?>) protocol).getMinSpareThreads());
                log.info(res);
            });
        };
    }
}
