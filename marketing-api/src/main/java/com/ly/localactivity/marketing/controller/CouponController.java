package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.adapter.CheckListLog;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.application.ICouponServiceApp;
import com.ly.localactivity.marketing.application.model.ro.*;
import com.ly.localactivity.marketing.application.model.vo.*;
import com.ly.localactivity.marketing.common.annotation.ApiLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * CouponController
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
@Api(tags = "红包接口")
@RestController
@RequestMapping("/coupon")
@Slf4j
public class CouponController {
    @Resource
    private ICouponServiceApp couponServiceApp;

     @CheckListLog
     @ApiOperation(value = "红包点位", notes = "返回对应的红包点位")
     @PostMapping("/couponPosition")
     public CommonResult<CouponPositionVo> couponPosition(@RequestBody @Valid CouponPositionQueryRo couponPositionQueryRo) {
     return couponServiceApp.couponPosition(couponPositionQueryRo);
     }

    /**
     * 查询符合条件的优惠券列表和用户已领取的优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    @CheckListLog
    @ApiOperation(value = "查询符合条件的优惠券列表和用户已领取的优惠券列表",notes = "查询符合条件的优惠券列表和用户已领取的优惠券列表")
    @PostMapping("/queryCouponListAndUserCouponList")
    public CommonResult<CouponQueryResponse> queryCouponListAndUserCouponList(@RequestBody @Valid CouponQueryRo queryRo) {
        return couponServiceApp.queryCouponListV3(queryRo);
    }

    /**
     * 下单查询优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponOrderQueryResponse}>
     */
    @CheckListLog
    @ApiOperation(value = "下单查询优惠券列表")
    @PostMapping("/queryCouponOrderList")
    public CommonResult<CouponOrderQueryResponse> queryCouponOrderList(@RequestBody @Valid CouponOrderQueryRo queryRo) {
        return couponServiceApp.queryCouponOrderList(queryRo);
    }

    /**
     * 领券
     *
     * @param rechargeRo 充值ro
     * @return {@link CommonResult}<{@link CouponRechargeResponse}>
     */
    @ApiLog
    @CheckListLog
    @ApiOperation(value = "领券")
    @PostMapping("/rechargeCoupon")
    public CommonResult<CouponRechargeResponse> rechargeCoupon(@RequestBody @Valid CouponRechargeRo rechargeRo) {
        return couponServiceApp.rechargeCoupon(rechargeRo);
    }

    /**
     * 占用优惠券
     *
     * @param occupyRo 占领ro
     * @return {@link CommonResult}
     */
    @ApiLog
    @CheckListLog
    @ApiOperation(value = "占用优惠券")
    @PostMapping("/occupyCoupon")
    public CommonResult occupyCoupon(@RequestBody @Valid CouponOccupyRo occupyRo) {
        return couponServiceApp.occupyCoupon(occupyRo);
    }

    /**
     * 恢复占用
     *
     * @param recoverRo 恢复ro
     * @return {@link CommonResult}
     */
    @ApiLog
    @CheckListLog
    @ApiOperation(value = "恢复占用")
    @PostMapping("/recoverCoupon")
    public CommonResult recoverCoupon(@RequestBody @Valid CouponRecoverRo recoverRo) {
        return couponServiceApp.recoverCoupon(recoverRo);
    }

    /**
     * 使用优惠券
     *
     * @param consumeRo 消耗ro
     * @return {@link CommonResult}
     */
    @ApiLog
    @CheckListLog
    @ApiOperation(value = "使用优惠券")
    @PostMapping("/consumeCoupon")
    public CommonResult consumeCoupon(@RequestBody @Valid CouponConsumeRo consumeRo) {
        return couponServiceApp.consumeCoupon(consumeRo);
    }

    /**
     * 退还优惠券
     *
     * @param returnRo 返回ro
     * @return {@link CommonResult}
     */
    @ApiLog
    @CheckListLog
    @ApiOperation(value = "退还优惠券")
    @PostMapping("/returnCoupon")
    public CommonResult returnCoupon(@RequestBody @Valid CouponReturnRo returnRo) {
        return couponServiceApp.returnCoupon(returnRo);
    }

    @CheckListLog
    @ApiOperation(value = "搜索优惠券列表", notes = "只给搜索列表用，数据可能会有延迟")
    @PostMapping("/searchCouponList")
    @Deprecated
    public CommonResult<List<CouponVo>> searchCouponList(@RequestBody @Valid CouponListQueryRo queryRo) {
        return couponServiceApp.searchList(queryRo);
    }

//    @CheckListLog
//    @ApiOperation(value = "优惠券详情-作废", notes = "只返回当前用户的优惠券详情")
//    @PostMapping("/detail")
//    public CommonResult<List<CouponVo>> getCouponDetail(@RequestBody @Valid CouponDetailRo detailRo) {
//        return couponServiceApp.getCouponDetail(detailRo);
//    }

    @CheckListLog
    @ApiOperation(value = "用户优惠券列表", notes = "只返回当前用户的已领取的优惠券列表")
    @PostMapping("/queryUserCouponList")
    public CommonResult<List<CouponVo>> queryUserCouponList(@RequestBody @Valid CouponUserQueryRo userQueryRo) {
        return couponServiceApp.getUserCoupon(userQueryRo);
    }


    /**
     * 查询所有符合条件的优惠券列表
     *
     * @param queryRo 查询ro
     * @return {@link CommonResult}<{@link CouponQueryResponse}>
     */
    @CheckListLog
    @ApiOperation(value = "查询所有符合条件的优惠券列表",notes = "查询所有符合条件的优惠券列表")
    @PostMapping("/queryAllCouponList")
    public CommonResult<List<CouponBatchVo>> queryAllCouponList(@RequestBody @Valid AllCouponQueryRo queryRo) {
        return couponServiceApp.queryAllCouponList(queryRo);
    }
}
