//package com.ly.localactivity.marketing.common.config;
//
//import com.ly.localactivity.marketing.common.interceptor.GzipResponseInterceptor;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.annotation.Resource;
//
///**
// * WebConfig
// *
// * <AUTHOR>
// * @date 2024/5/22
// */
//@Configuration
//public class WebConfig implements WebMvcConfigurer {
//    @Resource
//    private GzipResponseInterceptor gzipResponseInterceptor;
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        // 注册自定义拦截器
//        registry.addInterceptor(gzipResponseInterceptor)
//                // 指定拦截所有请求，可以根据需要指定特定路径
//                .addPathPatterns("/**")
//                // 排除不需要拦截的路径
//                .excludePathPatterns("");
//    }
//
//}
