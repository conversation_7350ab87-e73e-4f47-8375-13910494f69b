package com.ly.localactivity.marketing.controller;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.marketing.common.constants.EnvConst;
import com.ly.localactivity.marketing.common.utils.StringUtils;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponEsSearchDto;
import com.ly.localactivity.marketing.external.coupon.model.CouponCommonResult;
import com.ly.localactivity.marketing.external.coupon.ro.CouponListRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponOrderListRequest;
import com.ly.localactivity.marketing.external.coupon.service.CouponCoreService;
import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import com.ly.localactivity.marketing.service.coupon.ICouponSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TestController
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@Api(tags = "测试接口")
@RestController
@RequestMapping("/test")
@ConditionalOnProperty(prefix = "local-activity.testApi", name = "enabled", havingValue = "true")
public class TestController {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ICouponQueryService couponQueryService;
    @Resource
    private ICouponSearchService couponSearchService;
    @Resource
    private CouponCoreService couponCoreService;

    @ApiOperation(value = "删除缓存")
    @PostMapping("/redis/delete")
    public CommonResult deletRedis(@RequestParam("key") String key) {
        if (StringUtils.isBlank(key)) {
            return CommonResult.failed("key不能为空");
        }
        redisUtils.del(key);
        return CommonResult.success();
    }

    @ApiOperation(value = "获取缓存")
    @PostMapping("/redis/get")
    public CommonResult getRedis(@RequestParam("key") String key) {
        if (StringUtils.isBlank(key)) {
            return CommonResult.failed("key不能为空");
        }
        return CommonResult.success(redisUtils.get(key));
    }

    @ApiOperation(value = "刷新红包批次")
    @PostMapping("/coupon/refreshBatch")
    public CommonResult refreshCouponBatch(@RequestParam("batchNo") String batchNo, @RequestParam("env") String env) {

        if (StringUtils.isNotBlank(env) && (env.equals(EnvConst.ENV_STAGE) || env.equals(EnvConst.ENV_PRODUCT))) {
            couponQueryService.refreshCache(batchNo, env);
        } else {
            couponQueryService.refreshCache(batchNo);
        }

        return CommonResult.success();
    }

    @ApiOperation(value = "测试es接口性能")
    @PostMapping("/coupon/testEs")
    public CommonResult testEs(@RequestBody CouponEsSearchDto couponEsSearchDto) throws InterruptedException {

        Integer seconds = couponEsSearchDto.getSeconds();
        if (seconds == null){
            seconds = 10;
        }
        Integer threadCount = couponEsSearchDto.getThreadCount();
        if (threadCount == null){
            threadCount = 1;
        }

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000), new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("调用testEs线程");
                return thread;
            }
        }, new ThreadPoolExecutor.AbortPolicy());

        long start = System.currentTimeMillis();

        int count = 0;
        AtomicInteger atomicCount = new AtomicInteger(0);

        if (threadCount > 1){
            Integer finalSeconds = seconds;

            for (Integer i = 0; i < threadCount; i++) {
                threadPoolExecutor.execute(() -> {
                    while (true){
                        couponSearchService.searchEsByLimit(couponEsSearchDto);
                        atomicCount.getAndAdd(1);

                        if (System.currentTimeMillis() - start > finalSeconds * 1000){
                            break;
                        }
                    }
                });
            }


        }else {
            while (true) {
                couponSearchService.searchEsByLimit(couponEsSearchDto);
                count++;
                if (System.currentTimeMillis() - start > seconds * 1000) {
                    break;
                }
            }
        }
        if(threadCount > 1){
            synchronized(threadPoolExecutor){
                threadPoolExecutor.wait(seconds * 1000);
            }
            count = atomicCount.get();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("cost", System.currentTimeMillis() - start);
        result.put("count", count);
        result.put("avgCost", BigDecimal.valueOf((System.currentTimeMillis() - start) * threadCount).divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP));
        result.put("qps", BigDecimal.valueOf(count).divide(BigDecimal.valueOf(System.currentTimeMillis() - start), 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(1000)));

        return CommonResult.success(result);
    }
    @ApiOperation(value = "测试鲲鹏接口性能")
    @PostMapping("/coupon/testKunPeng")
    public CommonResult testKunPeng(@RequestBody CouponListRequest listRequest) throws InterruptedException {

        Integer seconds = listRequest.getSeconds();
        if (seconds == null){
            seconds = 10;
        }
        Integer threadCount = listRequest.getThreadCount();
        if (threadCount == null){
            threadCount = 1;
        }

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000), new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("调用testEs线程");
                return thread;
            }
        }, new ThreadPoolExecutor.AbortPolicy());

        long start = System.currentTimeMillis();

        int count = 0;
        AtomicInteger atomicCount = new AtomicInteger(0);

        if (threadCount > 1){
            Integer finalSeconds = seconds;

            for (Integer i = 0; i < threadCount; i++) {
                threadPoolExecutor.execute(() -> {
                    while (true){
                        couponCoreService.getCouponList(listRequest);
                        atomicCount.getAndAdd(1);

                        if (System.currentTimeMillis() - start > finalSeconds * 1000){
                            break;
                        }
                    }
                });
            }


        }else {
            while (true) {
                couponCoreService.getCouponList(listRequest);
                count++;
                if (System.currentTimeMillis() - start > seconds * 1000) {
                    break;
                }
            }
        }
        if(threadCount > 1){
            synchronized(threadPoolExecutor){
                threadPoolExecutor.wait(seconds * 1000);
            }
            count = atomicCount.get();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("cost", System.currentTimeMillis() - start);
        result.put("count", count);
        result.put("avgCost", BigDecimal.valueOf((System.currentTimeMillis() - start) * threadCount).divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP));
        result.put("qps", BigDecimal.valueOf(count).divide(BigDecimal.valueOf(System.currentTimeMillis() - start), 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(1000)));
        return CommonResult.success(result);
    }

    @ApiOperation(value = "test")
    @PostMapping("/coupon/test")
    public CommonResult refreshCouponBatch() {
        return CommonResult.success();
    }

    @ApiOperation(value = "鲲鹏获取红包列表接口getCouponList")
    @PostMapping("/coupon/getCouponList")
    public CouponCommonResult getCouponList(@RequestBody CouponListRequest listRequest) {
        return couponCoreService.getCouponList(listRequest);
    }

    @ApiOperation(value = "鲲鹏获取订单红包列表接口getCouponOrderList")
    @PostMapping("/coupon/getCouponOrderList")
    public CouponCommonResult getCouponOrderList(@RequestBody CouponOrderListRequest listRequest)  {
        return couponCoreService.getCouponOrderList_ConnectPool(listRequest);
    }
}
