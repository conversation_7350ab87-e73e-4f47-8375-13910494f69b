//package com.ly.localactivity.marketing.common.config;
//
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * FilterConfig
// *
// * <AUTHOR>
// * @date 2024/1/17
// */
//@Configuration
//public class FilterConfig {
//    @Bean
//    public FilterRegistrationBean<CorsFilter> filterRegistrationBean() {
//        FilterRegistrationBean<CorsFilter> filterRegistrationBean = new FilterRegistrationBean<>();
//        filterRegistrationBean.setFilter(new CorsFilter());
//        filterRegistrationBean.addUrlPatterns("/*");
//        return filterRegistrationBean;
//    }
//}
