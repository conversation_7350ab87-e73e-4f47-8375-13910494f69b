package com.ly.localactivity.marketing.common.interceptor;

import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * job接口权限校验
 *
 * @date 2023/8/29
 */
@Aspect
@Component
@Slf4j
public class AuthorizeJobAspect {

    private static final String JOB_HEADER_KEY = "JobAuth.Header";
    private static final String JOB_TOKEN_KEY = "JobAuth.Token";

    @Pointcut("execution(* com.ly.localactivity.controller.job.*Controller.*(..))")
    public void authorizeJob() {
    }

    @Around("authorizeJob()")
    public Object doAround(ProceedingJoinPoint joinPoint) {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        try {
            String header = ConfigCenterClient.get(JOB_HEADER_KEY);
            String token = ConfigCenterClient.get(JOB_TOKEN_KEY);
            if (StringUtils.isBlank(header) || StringUtils.isBlank(token)) {
                return CommonResult.failed("接口校验失败");
            }
            String reqToken = request.getHeader(header);
            if (StringUtils.isBlank(reqToken) || !token.equals(reqToken)) {
                return CommonResult.unauthorized(null);
            }

            long currentTime = System.currentTimeMillis();
            log.info("开始执行job接口：{}", request.getRequestURI());
            Object obj = joinPoint.proceed();
            log.info("job接口执行完成：{}，耗时：{}ms", request.getRequestURI(), System.currentTimeMillis() - currentTime);

            return obj;
        } catch (Throwable throwable) {
            log.error("AuthorizeJobAspect error:{}", throwable.getMessage());
            return CommonResult.failed("接口异常");
        }

    }
}
