package com.ly.localactivity.service;

import com.ly.localactivity.marketing.service.coupon.ICouponQueryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * CouponQueryServiceTest
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CouponQueryServiceTest {
    @Resource
    private ICouponQueryService couponQueryService;

    @Test
    public void testRefreshCache() {
        couponQueryService.refreshCache("AP_AC3KNRF6JAZ");
    }
}
