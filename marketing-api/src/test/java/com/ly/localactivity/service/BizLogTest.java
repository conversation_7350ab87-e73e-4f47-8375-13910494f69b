package com.ly.localactivity.service;

import com.ly.localactivity.framework.bizlog.BizLogUtils;
import com.ly.localactivity.framework.bizlog.model.BizLogDto;
import com.ly.localactivity.framework.enums.LogActionEnum;
import com.ly.localactivity.framework.utils.BeanCompareUtils;
import com.ly.localactivity.marketing.domain.marketing.OperationPosition;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * BizLogTest
 *
 * <AUTHOR>
 * @date 2024/3/4
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class BizLogTest {

    @Resource
    private BizLogUtils bizLogUtils;

    @Test
    public void test() {
        Object newObject = new Object();
        Object oldObject = new Object();
        bizLogUtils.writeLog(BizLogDto.builder()
                .action(LogActionEnum.Query)
                .logType1("业务模块1")
                .logType2("业务功能1")
                .filter("过滤条件，查询时使用，对应的数据id")
//                        .content("操作内容，自定义的操作内容")
                // 使用工具类生成操作内容，比对新旧对象变化
                .content(BeanCompareUtils.compareStr(oldObject, newObject))
                .operateTime(LocalDateTime.now())
                .operator("操作人")
                .operatorNum("操作人工号")
                .build());
    }
    @Test
    public void compareTest(){
        OperationPosition oldObject = new OperationPosition();
        oldObject.setCode("123");
        OperationPosition newObject = new OperationPosition();
        newObject.setCode("123333");
        String str = BeanCompareUtils.compareStr(oldObject, newObject);
        System.out.println(str);
    }
}
