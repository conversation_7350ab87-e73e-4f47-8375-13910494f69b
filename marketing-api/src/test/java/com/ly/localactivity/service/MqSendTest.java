package com.ly.localactivity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.mq.TurboMqUtils;
import com.ly.localactivity.framework.utils.SignUtils;
import com.ly.localactivity.marketing.common.constants.MQConst;
import com.ly.localactivity.marketing.common.enums.PlatformEnum;
import com.ly.localactivity.marketing.domain.marketing.dto.CouponRechargeDto;
import com.ly.localactivity.marketing.service.coupon.ICouponRechargeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * mq发送测试
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class MqSendTest {
    @Resource
    private TurboMqUtils turboMqUtils;
    @Resource
    private ICouponRechargeService couponRechargeService;

    @Test
    public void sendCouponRecharge() {
        String str = "{\"batchNo\":[\"AP_AC3KNRF6JAZ\"],\"cacheKey\":\"eeb5c19c26658ac300d15d3940e675c2\",\"deviceId\":\"D61D3A97-1C5A-4DCE-8031-7B93B1000CB4\",\"from\":\"travelaround\",\"memberId\":23596440,\"platform\":\"433\",\"refId\":\"998999\",\"sendTime\":\"2024-04-25 10:19:33.054\",\"sign\":\"8a9fd80020ec81884b7c8f024f449b39\",\"tradeNo\":\"8da74daef5f944299f56feccb560d389\"}";
        CouponRechargeDto couponRechargeDto = JSON.parseObject(str, CouponRechargeDto.class);
//        CouponRechargeDto couponRechargeDto = new CouponRechargeDto();
//        couponRechargeDto.setFrom("test");
//        couponRechargeDto.setOrderNo("test");
//        couponRechargeDto.setBatchNo(Arrays.asList("AP_AC3KBTZWV3F", "AP_AC3KBU2FJCA", "AP_AC3KNRF6JAZ"));
////        couponRechargeDto.setBatchNo(Arrays.asList("AP_AC3KBU2FJCA"));
//        couponRechargeDto.setRefId("testRefId");
//        couponRechargeDto.setPlatform(PlatformEnum.Wx_App.getCode());
////        couponRechargeDto.setMemberId(1811294569l);
//        couponRechargeDto.setOpenId("o498X0Sq4_cJR19_HP1NcQHl-2Gg");
//        couponRechargeDto.setUnionId("ohmdTt8d5OQUKGc0Kj5SM-ilBQvI");
//        couponRechargeDto.setDeviceId("asdasdasdsadas");
//        couponRechargeDto.setIsPackage(false);
//        couponRechargeDto.setSign("52956670977e166da5a5d01c1f408172");
//        couponRechargeDto.setTradeNo("asdsasdsaddsadsadad");
        turboMqUtils.sendMsg(MQConst.GROUP_LA_MARKETING_DEFAULT, MQConst.TOPIC_LA_MARKETING_COUPON_RECHARGE, JSON.toJSONString(couponRechargeDto));
    }

    @Test
    public void rechargeTest(){
        String str = "{\"batchNo\":[\"AP_AC3KNRF6JAZ\"],\"cacheKey\":\"eeb5c19c26658ac300d15d3940e675c2\",\"deviceId\":\"D61D3A97-1C5A-4DCE-8031-7B93B1000CB4\",\"from\":\"travelaround\",\"memberId\":23596440,\"platform\":\"433\",\"refId\":\"998999\",\"sendTime\":\"2024-04-25 10:19:33.054\",\"sign\":\"8a9fd80020ec81884b7c8f024f449b39\",\"tradeNo\":\"8da74daef5f944299f56feccb560d389\"}";
        CouponRechargeDto couponRechargeDto = JSON.parseObject(str, CouponRechargeDto.class);
        String sign = SignUtils.getSign("FC1mwZaiBZ1GtA4m58vt", (JSONObject) com.alibaba.fastjson2.JSON.toJSON(couponRechargeDto));
        couponRechargeService.recharge(couponRechargeDto, true);
    }
}
