package com.ly.localactivity.service;

import com.ly.localactivity.marketing.external.coupon.ro.CouponCheckRequest;
import com.ly.localactivity.marketing.external.coupon.ro.CouponRechargeRequest;
import com.ly.localactivity.marketing.external.coupon.service.CouponCoreService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * CouponCoreServiceTest
 *
 * <AUTHOR>
 * @date 2024/1/30
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CouponCoreServiceTest {
    @Resource
    private CouponCoreService couponCoreService;

    @Test
    public void getCommonHeader() {
        System.out.println(couponCoreService.getCommonRequestHeader());
    }

    @Test
    public void TestRecharge() {
        System.out.println(couponCoreService.recharge(CouponRechargeRequest.builder().build()));
    }

    @Test
    public void TestCheckCoupon() {
        System.out.println(couponCoreService.checkCoupon(CouponCheckRequest.builder().build()));
    }
}
