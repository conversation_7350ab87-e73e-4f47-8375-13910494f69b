package com.ly.localactivity.service;

import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.marketing.application.IOperationPromotionApp;
import com.ly.localactivity.marketing.application.model.ro.PromotionPlanApiRo;
import com.ly.localactivity.marketing.application.model.vo.OperationPositionApiVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * OperationPromotionTest
 *
 * <AUTHOR>
 * @date 2024/2/18
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class OperationPromotionTest {

    @Resource
    private IOperationPromotionApp operationPromotionApp;

    @Test
    public void testPlan() {
        PromotionPlanApiRo planRo = new PromotionPlanApiRo();
        planRo.setPlatform("433");
        planRo.setCityId(226L);
        planRo.setPositionCodes(Arrays.asList("8_1_1_2_1_2_1"));
        planRo.setQueryParams(null);
//        List<OperationPositionApiVo> promotionPlan = operationPromotionApp.getPromotionPlan(planRo);
//        System.out.println(JSON.toJSONString(promotionPlan));
    }
}
