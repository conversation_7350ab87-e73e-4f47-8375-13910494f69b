package com.ly.localactivity;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.marketing.application.model.ro.CouponQueryRo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.MessageFormat;
import java.util.*;

/**
 * ApiToolsTest
 *
 * @auth aaron
 * @date 2023/12/19
 */
@RunWith(SpringRunner.class)
public class ApiToolsTest {

    @Test
    public void test() {
        String appKey = "localactivity-innerapi";
        String appSecret = "vjmAu8rsSzUaaZ4oa05vVjSyG9p7Mu8L";
        String url = "https://resource.t.17usoft.com/localactivity-marketingapi/coupon/queryList";
        String reqStr = "{\"appId\":\"localactivity-innerapi\",\"limitData\":{\"cityIds\":\"259\",\"resourceIds\":\"345353\",\"secondCategorys\":\"101\",\"supplierId\":\"45131\"},\"memberId\":\"23596440\",\"platform\":433,\"projectType\":1,\"requestId\":\"09338b5e-824c-41b7-9d19-6a28acaddee9\",\"sign\":\"52e1ff815159479a41b7d06f949ed5e9\",\"simple\":true,\"timestamp\":1717034583338}";
        CouponQueryRo requestDto = JSON.parseObject(reqStr, CouponQueryRo.class);
        requestDto.setTimestamp(System.currentTimeMillis());
        requestDto.setRequestId(UUID.randomUUID().toString());

        String sign = getSign(appSecret, JSONObject.parseObject(JSONObject.toJSONString(requestDto)));
        requestDto.setSign(sign);
        System.out.println("开始请求:" + JSON.toJSONString(requestDto));
        String result = HttpUtil.post(url, JSONObject.toJSONString(requestDto));
        System.out.println(result);
    }

    private static String getFormatParams(Map<String, Object> params) {
        List<Map.Entry<String, Object>> infoIds = new ArrayList<Map.Entry<String, Object>>(params.entrySet());
        Collections.sort(infoIds, new Comparator<Map.Entry<String, Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> arg0, Map.Entry<String, Object> arg1) {
                return (arg0.getKey()).compareTo(arg1.getKey());
            }
        });
        String ret = "";
        for (Map.Entry<String, Object> entry : infoIds) {
            if(entry.getKey().equals("sign")){
                continue;
            }
            ret += entry.getKey();
            ret += "=";
            ret += entry.getValue();
            ret += "&";
        }
        return ret;
    }

    public static String getSign(String appSecret, JSONObject params) {
        // 参数进行字典排序
        String sortStr = getFormatParams(params);
        // 将密钥key拼接在字典排序后的参数字符串中,得到待签名字符串。
        sortStr += MessageFormat.format("appSecret={0}", appSecret);

        System.out.println("待签名字符串：" + sortStr);
        // 使用md5算法加密待加密字符串
        return DigestUtil.md5Hex(sortStr);
    }
}
