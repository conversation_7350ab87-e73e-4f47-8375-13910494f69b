package com.ly.localactivity.application;

import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.marketing.application.ICouponServiceApp;
import com.ly.localactivity.marketing.application.model.ro.CouponQueryRo;
import com.ly.localactivity.marketing.application.model.vo.CouponQueryResponse;
import com.ly.localactivity.marketing.common.enums.coupon.CouponProjectTypeEnum;
import com.ly.localactivity.marketing.service.marketing.impl.CouponBatchListServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * CouponServiceAppTest
 *
 * <AUTHOR>
 * @date 2024/5/6
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CouponServiceAppTest {
    @Resource
    private ICouponServiceApp couponServiceApp;

    @Test
    public void  test(){

        CouponQueryRo queryRo = new CouponQueryRo();
        queryRo.setPlatform(433);
        queryRo.setDeviceId("D61D3A97-1C5A-4DCE-8031-7B93B1000CB4");
        queryRo.setMemberId(23596440L);
        queryRo.setProjectType(CouponProjectTypeEnum.DayTrip.getCode());

        CommonResult<CouponQueryResponse> response = couponServiceApp.queryCouponList(queryRo);
        System.out.println(JSON.toJSONString(response));
    }


    @Resource
    private CouponBatchListServiceImpl couponBatchListService;
    @Test
    public void test1(){
//        couponBatchListService.batchPushCouponBatchList(null);
    }
}
