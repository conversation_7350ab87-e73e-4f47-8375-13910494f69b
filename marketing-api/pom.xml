<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ly.localactivity</groupId>
        <artifactId>localactivity-marketing</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>marketing-api</artifactId>
    <name>marketing-api</name>

    <dependencies>
        <dependency>
            <groupId>com.ly.localactivity</groupId>
            <artifactId>marketing-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>5.2.1.RELEASE</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <activatedProperties>dev</activatedProperties>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <activatedProperties>test</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>qa</id>
            <properties>
                <activatedProperties>qa</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <activatedProperties>uat</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>stage</id>
            <properties>
                <activatedProperties>stage</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <activatedProperties>product</activatedProperties>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>localactivity-marketing-api</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*.yml</include>
                    <include>*.properties</include>
                    <include>logback-spring.xml</include>
                    <include>mapper/**</include>
                    <include>checklist.xml</include>
                    <include>config/**</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/config/${activatedProperties}</directory>
            </resource>
<!--            <resource>-->
<!--                <directory>src/main/resources/conf/custom/notenv</directory>-->
<!--            </resource>-->
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <!-- pdf文件后缀名，不被filter打包时编码 -->
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
